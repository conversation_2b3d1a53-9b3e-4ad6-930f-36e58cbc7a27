package com.howbuy.tms.high.orders.service.common.enums;

import com.howbuy.tms.common.enums.busi.DisCodeEnum;
import org.apache.commons.lang3.StringUtils;

public enum FundBuyStatusChannelResourceEnum {
    // 基金档案页 好买
    FUND_PROFILE_PAGE_HM("1", DisCodeEnum.HM,"好买基金档案页"),

    // 基金档案页 好臻
    FUND_PROFILE_PAGE_HZ("2", DisCodeEnum.HZ,"好臻基金档案页"),

    ;

    private final String code;

    private final DisCodeEnum disCodeEnum;

    private final String desc;

    FundBuyStatusChannelResourceEnum(String code, DisCodeEnum disCodeEnum, String desc) {
        this.code = code;
        this.disCodeEnum = disCodeEnum;
        this.desc = desc;
    }

    /**
     * 获取渠道来源
     * @return
     */
    public static FundBuyStatusChannelResourceEnum getEnumByCodeAndDisCode(String code, String disCode) {
        if(StringUtils.isAnyBlank(code,disCode)){
            return null;
        }
        for (FundBuyStatusChannelResourceEnum fundBuyStatusChannelResourceEnum : FundBuyStatusChannelResourceEnum.values()) {
            if(fundBuyStatusChannelResourceEnum.getCode().equals(code) && fundBuyStatusChannelResourceEnum.getDisCodeEnum().getCode().equals(disCode)){
                return fundBuyStatusChannelResourceEnum;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public DisCodeEnum getDisCodeEnum() {
        return disCodeEnum;
    }
}
