/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.orders.facade.search.querymemberinfo.enums;

/**
 * @description: 会员等级枚举
 * <AUTHOR>
 * @date 2025/9/9
 * @since JDK 1.8
 */
public enum MemberLevelEnum {

    /**
     * 非会员
     */
    NON_MEMBER("0", "非会员"),

    /**
     * 臻享会员
     */
    ZHEN_XIANG_MEMBER("23301", "臻享会员"),
    
    /**
     * 私享会员
     */
    SI_XIANG_MEMBER("23302", "私享会员"),
    
    /**
     * 尊享会员
     */
    ZUN_XIANG_MEMBER("23303", "尊享会员");

    private final String code;
    private final String description;

    MemberLevelEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据代码获取枚举
     * 
     * @param code 会员等级代码
     * @return 会员等级枚举
     */
    public static MemberLevelEnum getByCode(String code) {
        if (code == null) {
            return null;
        }
        for (MemberLevelEnum level : MemberLevelEnum.values()) {
            if (level.code.equals(code)) {
                return level;
            }
        }
        return null;
    }
}
