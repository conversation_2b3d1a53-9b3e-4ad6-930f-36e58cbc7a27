package com.howbuy.tms.high.orders.service.facade.search.queryacctownershiporderinfo;

import com.alibaba.fastjson.JSON;
import com.howbuy.tms.common.constant.ExceptionCodes;
import com.howbuy.tms.common.enums.busi.OwnershipTransferIdentityEnum;
import com.howbuy.tms.common.enums.busi.ProductDBTypeEnum;
import com.howbuy.tms.common.enums.busi.YesOrNoEnum;
import com.howbuy.tms.common.exception.BusinessException;
import com.howbuy.tms.common.message.MessageSource;
import com.howbuy.tms.common.outerservice.cc.center.queryhboneno.QueryHbOneNoOuterService;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.QueryHighProductOuterService;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductDBInfoBean;
import com.howbuy.tms.high.orders.facade.search.queryacctownershiporder.AcctOwnershipOrderDto;
import com.howbuy.tms.high.orders.facade.search.queryacctownershiporder.QueryAcctOwnershipOrderRequest;
import com.howbuy.tms.high.orders.facade.search.queryacctownershiporder.QueryAcctOwnershipOrderResponse;
import com.howbuy.tms.high.orders.facade.search.queryacctownershiporder.QueryFundOwnershipOrderListFacadeService;
import com.howbuy.tms.high.orders.service.service.acctbalancebaseinfo.AcctBalanceBaseInfoService;
import com.howbuy.tms.high.orders.service.service.ownershiprightorder.OwnershipOrderInfo;
import com.howbuy.tms.high.orders.service.service.acctbalancebaseinfo.bean.QueryAcctBalanceBaseParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description:查询股权订单
 * @Author: yun.lu
 * Date: 2023/8/16 15:29
 */
@DubboService
@Service("queryFundOwnershipOrderListFacadeService")
@Slf4j
public class QueryFundOwnershipOrderListFacadeServiceImpl implements QueryFundOwnershipOrderListFacadeService {
    @Autowired
    private QueryHbOneNoOuterService queryHbOneNoOuterService;
    @Autowired
    private AcctBalanceBaseInfoService acctBalanceBaseInfoService;
    @Autowired
    private QueryHighProductOuterService queryHighProductOuterService;

    /**
     * @api {DUBBO} com.howbuy.tms.high.orders.facade.search.queryacctownershiporder.QueryFundOwnershipOrderListFacadeService.execute(QueryAcctOwnershipOrderRequest request)
     * @apiVersion 1.0.0
     * @apiGroup QueryFundOwnershipOrderListFacadeServiceImpl
     * @apiName execute
     * @apiDescription 查询股权订单
     * @apiParam (请求参数) {String} fundCode 产品编码
     * @apiParam (请求参数) {String} onlyTransfer 是否只股权转让的,0:不是;1:是
     * @apiParam (请求参数) {String} txAcctNo 交易账号
     * @apiParam (请求参数) {String} hbOneNo 一账通账号
     * @apiParam (请求参数) {String} disCode 分销机构代码
     * @apiParam (请求参数) {String} outletCode 网点代码
     * @apiParam (请求参数) {String} appDt 申请日期
     * @apiParam (请求参数) {String} appTm 申请时间
     * @apiParam (请求参数) {Number} pageNo 页码
     * @apiParam (请求参数) {Number} pageSize 每页记录数
     * @apiParam (请求参数) {String} operIp 交易IP
     * @apiParam (请求参数) {String} txCode 交易码
     * @apiParam (请求参数) {String} txChannel 交易渠道 1-柜台,2-网站,3-电话,4-Wap,5-App,6-机构
     * @apiParam (请求参数) {String} dataTrack 数据跟踪
     * @apiParam (请求参数) {String} subOutletCode 子网点代码
     * @apiParamExample 请求参数示例
     * hbOneNo=SWV&onlyTransfer=b5&pageSize=6034&disCode=M&txChannel=Dpk&appTm=UyUVjO2&fundCode=4ElEN&subOutletCode=M&pageNo=8743&operIp=Wsdxa&txAcctNo=Rum&appDt=Nyms3Wb&dataTrack=zLk0jL&txCode=UTB&outletCode=YnyK
     * @apiSuccess (响应结果) {Array} acctOwnershipOrderDtoList 转让明细
     * @apiSuccess (响应结果) {String} acctOwnershipOrderDtoList.orderNo 订单号
     * @apiSuccess (响应结果) {String} acctOwnershipOrderDtoList.fundCode 产品code
     * @apiSuccess (响应结果) {String} acctOwnershipOrderDtoList.mBusinessCode 中台业务编码
     * @apiSuccess (响应结果) {String} acctOwnershipOrderDtoList.ackDt 确认日期
     * @apiSuccess (响应结果) {Number} acctOwnershipOrderDtoList.ackVol 确认份额
     * @apiSuccess (响应结果) {Number} acctOwnershipOrderDtoList.ackAmt 确认金额
     * @apiSuccess (响应结果) {Number} acctOwnershipOrderDtoList.fee 费用
     * @apiSuccess (响应结果) {Number} acctOwnershipOrderDtoList.transferPrice 转让价格
     * @apiSuccess (响应结果) {String} acctOwnershipOrderDtoList.ownershipTransferIdentity 转让标识
     * @apiSuccess (响应结果) {String} currency 币种
     * @apiSuccess (响应结果) {String} returnCode 返回码
     * @apiSuccess (响应结果) {String} description 描述
     * @apiSuccess (响应结果) {Number} totalCount 总记录数
     * @apiSuccess (响应结果) {Number} totalPage 总页数
     * @apiSuccess (响应结果) {Number} pageNo 当前页
     * @apiSuccessExample 响应结果示例
     * {"returnCode":"zWOMCIe0V","totalPage":5822,"pageNo":934,"acctOwnershipOrderDtoList":[{"ackVol":6079.703680889666,"orderNo":"4vts","fundCode":"hjMTprkD","ackAmt":5186.244141549524,"fee":793.7139879851173,"ackDt":"9D","mBusinessCode":"zXO0Jc","transferPrice":612.1013966628242,"ownershipTransferIdentity":"L"}],"description":"ORJzfoFD3","currency":"amEwlnKnV0","totalCount":8747}
     */
    @Override
    public QueryAcctOwnershipOrderResponse execute(QueryAcctOwnershipOrderRequest request) {
        // 1.参数校验
        checkAndBuildParam(request);
        // 2.合法性校验->产品必须是股权的
        Map<String, HighProductDBInfoBean> highProductDbInfoMap = queryHighProductOuterService.getHighProductDBInfoMap(Collections.singletonList(request.getFundCode()));
        if (highProductDbInfoMap == null) {
            log.error("QueryFundOwnershipOrderDetailFacadeServiceImpl,根据产品code查询不到产品信息,fundCode={}", request.getFundCode());
            throw new BusinessException(ExceptionCodes.PARAMS_ERROR, "根据产品code查询不到产品信息");
        }
        HighProductDBInfoBean highProductDbInfoBean = highProductDbInfoMap.get(request.getFundCode());
        if (highProductDbInfoBean == null) {
            log.error("QueryFundOwnershipOrderDetailFacadeServiceImpl,根据产品code查询不到产品信息,fundCode={}", request.getFundCode());
            throw new BusinessException(ExceptionCodes.PARAMS_ERROR, "根据产品code查询不到产品信息");
        }
        if (!ProductDBTypeEnum.GUQUAN.getCode().equals(highProductDbInfoBean.getFundSubType())) {
            log.error("QueryFundOwnershipOrderDetailFacadeServiceImpl,不是股权订单,该接口不可以调用,fundSubType={}", highProductDbInfoBean.getFundSubType());
            throw new BusinessException(ExceptionCodes.PARAMS_ERROR, "该产品不是股权产品");
        }

        // 3.查询股权订单
        QueryAcctBalanceBaseParam queryAcctBalanceBaseParam = new QueryAcctBalanceBaseParam();
        queryAcctBalanceBaseParam.setTxAcctNo(request.getTxAcctNo());
        queryAcctBalanceBaseParam.setHbOneNo(request.getHbOneNo());
        queryAcctBalanceBaseParam.setFundCodeList(Collections.singletonList(request.getFundCode()));
        List<OwnershipOrderInfo> ownershipOrderInfoList = acctBalanceBaseInfoService.queryAcctOwnershipOrderInfo(queryAcctBalanceBaseParam);
        // 4.查询结果处理
        QueryAcctOwnershipOrderResponse response = new QueryAcctOwnershipOrderResponse();
        response.setReturnCode(ExceptionCodes.SUCCESS);
        response.setDescription(MessageSource.getMessageByCode(ExceptionCodes.SUCCESS));
        response.setCurrency(highProductDbInfoBean.getCurrency());

        if (CollectionUtils.isEmpty(ownershipOrderInfoList)) {
            log.info("QueryFundOwnershipOrderDetailFacadeServiceImpl-查询股权订单,该用户+产品,没有股权订单,request={}", JSON.toJSONString(request));
            return response;
        }
        // 4.1.如果只查询股权转让的
        if (request.getOnlyTransfer() != null && YesOrNoEnum.YES.getCode().equals(request.getOnlyTransfer())) {
            ownershipOrderInfoList = ownershipOrderInfoList.stream().filter(ownershipOrderInfo -> !OwnershipTransferIdentityEnum.NO_TRANSFER.getType().equals(ownershipOrderInfo.getOwnershipTransferIdentity())).collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(ownershipOrderInfoList)) {
            log.info("QueryFundOwnershipOrderDetailFacadeServiceImpl-查询股权转让订单,该用户+产品,过滤后没有符合条件的订单,ownershipOrderInfoList={}", JSON.toJSONString(ownershipOrderInfoList));
            return response;
        }
        // 4.2.构建返回实体
        List<AcctOwnershipOrderDto> acctOwnershipOrderDtoList = new ArrayList<>();
        for (OwnershipOrderInfo ownershipOrderInfo : ownershipOrderInfoList) {
            AcctOwnershipOrderDto acctOwnershipOrderDto = new AcctOwnershipOrderDto();
            BeanUtils.copyProperties(ownershipOrderInfo, acctOwnershipOrderDto);
            acctOwnershipOrderDto.setOwnershipTransferIdentity(ownershipOrderInfo.getOwnershipTransferIdentity());
            acctOwnershipOrderDtoList.add(acctOwnershipOrderDto);
        }
        response.setAcctOwnershipOrderDtoList(acctOwnershipOrderDtoList);
        return response;
    }


    /**
     * 参数校验与赋值
     */
    private void checkAndBuildParam(QueryAcctOwnershipOrderRequest request) {
        // 1.参数非空校验
        if (StringUtils.isBlank(request.getTxAcctNo()) && StringUtils.isBlank(request.getHbOneNo())) {
            throw new BusinessException(ExceptionCodes.PARAMS_ERROR, "一账通/交易账号不能同时为空");
        }
        if (StringUtils.isBlank(request.getFundCode())) {
            throw new BusinessException(ExceptionCodes.PARAMS_ERROR, "产品编码不能为空");
        }
        if (StringUtils.isEmpty(request.getTxAcctNo())) {
            String txAcctNo = queryHbOneNoOuterService.queryTxAcctNoByHbOneNo(request.getHbOneNo());
            request.setTxAcctNo(txAcctNo);
        }
        if (StringUtils.isEmpty(request.getHbOneNo())) {
            String hbOneNo = queryHbOneNoOuterService.queryHbOneNoByTxAcctNo(request.getTxAcctNo());
            request.setHbOneNo(hbOneNo);
        }

    }
}
