package com.howbuy.tms.high.orders.service.service.acctbalancebaseinfo.bean;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Description: 客户确认持仓返回实体
 * @Author: yun.lu
 * @Date: 2024/07/29 10:00:00
 */
@Getter
@Setter
public class CustConfirmBalanceDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 基金代码
     */
    private String fundCode;

    /**
     * 子基金代码
     */
    private String subFundCode;
    /**
     * 交易账号
     */
    private String txAcctNo;
    /**
     * 持仓份额
     */
    private BigDecimal balanceVol;
    /**
     * 分销渠道
     */
    private String disCode;
    /**
     * 币种
     */
    private String currency;

    /**
     * 确认日期(只有子账本明细有)
     */
    private String ackDt;
    /**
     * 登记日期(只有子账本明细有)
     */
    private String regDt;
    /**
     * 成立日期(只有子账本明细有)
     */
    private String establishDt;
    /**
     * 销售类型: 1-直销;2-代销
     */
    private String scaleType;
}