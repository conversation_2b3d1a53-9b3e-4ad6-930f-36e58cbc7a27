<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.tms.high.orders.dao.mapper.customize.CmCustFundDirectPoMapper">
    <resultMap id="BaseResultMap" type="com.howbuy.tms.high.orders.dao.po.CmCustFundDirectPo"
               extends="com.howbuy.tms.high.orders.dao.mapper.CmCustFundDirectPoAutoMapper.BaseResultMap">
    </resultMap>

    <select id="selectDirectBalance" parameterType="map"
            resultMap="com.howbuy.tms.high.orders.dao.mapper.CmCustFundDirectPoAutoMapper.BaseResultMap">
        select t.fundcode,
               t.fundname,
               t.fundtype,
               t.currency,
               t.balancevol,
               t.mjjdm,
               t.CREDT,
               t.IS_HK_PRODUCT,
               t.DISCODE
        from cm_custfund_direct t
        <where>
            t.hboneno = #{hbOneNo,jdbcType=VARCHAR}
            <if test="disCodeList != null and disCodeList.size() > 0">
                and t.discode in
                <foreach collection="disCodeList" index="index" item="disCode" open="(" separator="," close=")">
                    #{disCode}
                </foreach>
            </if>
            <bind name="status" value="balanceStatus.toString()"/>
            <if test='status == "0"'>
                AND ((ABS(t.balancevol)  <![CDATA[<=]]> 1 and t.is_hk_product = 0) OR
                     (ABS(t.balancevol)  <![CDATA[<=]]> 0 and t.is_hk_product = 1))
            </if>
            <if test='status == "1"'>
                AND ((ABS(t.balancevol)  <![CDATA[>]]> 1 and t.is_hk_product = 0) OR
                     (ABS(t.balancevol)  <![CDATA[>]]> 0 and t.is_hk_product = 1))
            </if>
            <if test='status == "2"'>
                AND 1 = 1
            </if>
        </where>
        order by t.fundcode
    </select>
    <select id="queryConfirmBalanceBaseInfo"
            parameterType="com.howbuy.tms.high.orders.dao.vo.QueryAcctBalanceBaseInfoParamVo"
            resultMap="com.howbuy.tms.high.orders.dao.mapper.CmCustFundDirectPoAutoMapper.BaseResultMap">
        select t.fundcode,
               t.fundname,
               t.fundtype,
               t.currency,
               t.balancevol,
               t.mjjdm,
               t.CREDT,
               t.IS_HK_PRODUCT,
               t.DISCODE
        from cm_custfund_direct t
        where ((t.BALANCEVOL >= 1 AND t.is_hk_product=0 ) or (t.BALANCEVOL >= 0 AND t.is_hk_product=1 ))
        <if test="paramVo.hbOneNo != null and paramVo.hbOneNo != ''">
            and t.hboneno = #{paramVo.hbOneNo,jdbcType=VARCHAR}
        </if>
        <if test="paramVo.disCodeList != null and paramVo.disCodeList.size() > 0">
            and t.DISCODE in
            <foreach collection="paramVo.disCodeList" index="index" item="disCode" open="(" separator="," close=")">
                #{disCode}
            </foreach>
        </if>
        <if test="paramVo.fundCodeList != null and paramVo.fundCodeList.size() > 0">
            and(t.FUNDCODE in
            <foreach collection="paramVo.fundCodeList" index="index" item="fundCode" open="(" separator="," close=")">
                #{fundCode}
            </foreach>
            or t.MJJDM in
            <foreach collection="paramVo.fundCodeList" index="index" item="fundCode" open="(" separator="," close=")">
                #{fundCode}
            </foreach>
            )
        </if>
    </select>

    <select id="queryBalanceTxAcctNoByFundCode" resultType="java.lang.String" parameterType="map">
        select distinct (T2.HBONENO)
        from CM_CUSTFUND_DIRECT T2
        where ((BALANCEVOL <![CDATA[>]]> 1 AND is_hk_product=0 ) or (BALANCEVOL <![CDATA[>]]> 0 AND is_hk_product=1 ))
          and T2.FUNDCODE = #{fundCode,jdbcType=VARCHAR}
    </select>
    <select id="queryBalanceHbOneNoByPage" resultType="java.lang.String" parameterType="map">
        select distinct (T2.HBONENO)
        from CM_CUSTFUND_DIRECT T2
        where BALANCEVOL <![CDATA[>]]> 1 limit #{offset},#{pageSize}
    </select>

    <resultMap id="custFundBalanceMap" type="com.howbuy.tms.high.orders.dao.vo.ConfirmBalanceVo">
        <result column="hboneNo" jdbcType="VARCHAR" property="hboneNo" />
        <result column="fund_code" jdbcType="VARCHAR" property="fundCode" />
        <result column="DIS_CODE" jdbcType="VARCHAR" property="disCode" />
        <result column="ack_dt" jdbcType="VARCHAR" property="ackDt" />
        <result column="reg_dt" jdbcType="VARCHAR" property="regDt" />
        <result column="ESTABLISH_DT" jdbcType="VARCHAR" property="establishDt" />
        <result column="balance_vol" jdbcType="DECIMAL" property="balanceVol" />
        <result column="currency" jdbcType="VARCHAR" property="currency" />
        <result column="sub_fund_code" jdbcType="VARCHAR" property="subFundCode" />
    </resultMap>
    <select id="queryUnHkConfirmBalanceBaseInfo" resultMap="custFundBalanceMap" parameterType="map">
        select
        t.hboneno as hboneNo,
        ifnull(t.mjjdm,t.fundcode) as fund_code,
        CASE WHEN t.MJJDM IS NULL THEN NULL ELSE t.fundcode END as sub_fund_code,
        t.currency,
        t.balancevol as balance_vol,
        t.DISCODE as dis_code,
        t.currency,
        t.CREDT as reg_dt
        from cm_custfund_direct t
        where t.BALANCEVOL >= 1 AND t.is_hk_product=0 and t.hboneno = #{hboneNo,jdbcType=VARCHAR}
        <if test="disCodeList != null and disCodeList.size() > 0 ">
        and t.DISCODE in
            <foreach collection="disCodeList" index="index" item="disCode" open="(" separator="," close=")">
                #{disCode}
            </foreach>
        </if>
        <if test = "fundCode != null and fundCode != ''">
            and ifnull(t.mjjdm,t.fundcode) = #{fundCode,jdbcType = VARCHAR}
        </if>
    </select>

    <!-- 查询直销清仓持仓信息（持仓为0但非香港产品） -->
    <select id="queryDirectLiquidationBalance" resultMap="custFundBalanceMap" parameterType="map">
        select
        t.hboneno as hboneNo,
        ifnull(t.mjjdm,t.fundcode) as fund_code,
        CASE WHEN t.MJJDM IS NULL THEN NULL ELSE t.fundcode END as sub_fund_code,
        t.currency,
        t.balancevol as balance_vol,
        t.DISCODE as dis_code,
        t.currency
        from cm_custfund_direct t
        where t.BALANCEVOL  &lt; 1 AND t.is_hk_product=0 and t.hboneno = #{hboneNo,jdbcType=VARCHAR}
        <if test="disCodeList != null and disCodeList.size() > 0 ">
        and t.DISCODE in
            <foreach collection="disCodeList" index="index" item="disCode" open="(" separator="," close=")">
                #{disCode,jdbcType = VARCHAR}
            </foreach>
        </if>
        <if test = "fundCode != null and fundCode != ''">
            and ifnull(t.mjjdm,t.fundcode) = #{fundCode,jdbcType = VARCHAR}
        </if>
    </select>
</mapper>