package com.howbuy.tms.high.orders.dao.mapper.customize;

import com.howbuy.tms.high.orders.dao.mapper.CmCustFundDirectPoAutoMapper;
import com.howbuy.tms.high.orders.dao.po.CmCustFundDirectPo;
import java.util.List;

import com.howbuy.tms.high.orders.dao.vo.ConfirmBalanceVo;
import com.howbuy.tms.high.orders.dao.vo.QueryAcctBalanceBaseInfoParamVo;
import org.apache.ibatis.annotations.Param;

/**
 * 去O
 */
public interface CmCustFundDirectPoMapper extends CmCustFundDirectPoAutoMapper {

    List<CmCustFundDirectPo> selectDirectBalance(@Param("hbOneNo") String hbOneNo,@Param("balanceStatus") String balanceStatus,@Param("disCodeList")List<String> disCodeList);

    List<CmCustFundDirectPo> queryConfirmBalanceBaseInfo(@Param("paramVo")QueryAcctBalanceBaseInfoParamVo paramVo);

    List<String> queryBalanceTxAcctNoByFundCode(@Param("fundCode")String fundCode);

    List<String> queryBalanceHbOneNoByPage(@Param("offset")int offset, @Param("pageSize")int pageSize);

    List<ConfirmBalanceVo> queryUnHkConfirmBalanceBaseInfo(@Param("hboneNo")String hboneNo, @Param("disCodeList")List<String> disCodeList,@Param("fundCode") String fundCode);

    /**
     * 查询直销清仓持仓信息（持仓为0但非香港产品）
     * @param hboneNo 一账通账号
     * @param disCodeList 分销渠道列表
     * @param fundCode 基金代码
     * @return 清仓持仓信息列表
     */
    List<ConfirmBalanceVo> queryDirectLiquidationBalance(@Param("hboneNo")String hboneNo, @Param("disCodeList")List<String> disCodeList,@Param("fundCode") String fundCode);
}