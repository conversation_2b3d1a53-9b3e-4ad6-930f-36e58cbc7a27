package com.howbuy.tms.high.orders.service.service.acctbalancebaseinfo.bean;

import com.howbuy.tms.high.orders.facade.common.BaseDto;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description: 清仓持仓基础信息
 * @Author: AI Assistant  
 * @Date: 2025/09/12
 */
@Data
public class QueryLiquidationBalanceFundInfo extends BaseDto {

    /**
     * 交易账号
     */
    private String txAcctNo;

    /**
     * 一账通账号
     */
    private String hbOneNo;

    /**
     * 产品编码
     */
    private String fundCode;

    /**
     * 持仓份额(清仓状态下为0或接近0)
     */
    private BigDecimal balanceVol;

    /**
     * 是否是香港产品
     */
    private String isHkProduct;

    /**
     * 分销渠道
     */
    private String disCode;


    /**
     * 销售类型: 1-直销;2-代销
     */
    private String scaleType;

}