/**
 * Copyright (c) 2025, <PERSON>gH<PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.orders.facade.search.queryactivebuysellstats;

import com.howbuy.tms.common.client.BaseFacade;

/**
 * @api {DUBBO} com.howbuy.tms.high.orders.facade.search.queryactivebuysellstats.QueryActiveBuySellStatsFacade.execute(QueryActiveBuySellStatsRequest request)
 * @apiVersion 1.0.0
 * @apiGroup QueryActiveBuySellStatsFacade
 * @apiName execute
 * @apiDescription 查询主动买卖统计数据接口，查询客户近12个月的代销和直销买卖交易记录
 *
 * @apiParam (请求参数) {String} disCode 分销机构代码
 * @apiParam (请求参数) {String} outletCode 网点代码
 * @apiParam (请求参数) {String} [appDt] 申请日期(yyyyMMdd)
 * @apiParam (请求参数) {String} [appTm] 申请时间(HHMMSS)
 * @apiParam (请求参数) {String} operIp 交易Ip
 * @apiParam (请求参数) {String} [txCode] 交易码(有默认值，无需设置)
 * @apiParam (请求参数) {String} txChannel 交易渠道<br>1-柜台;2-网站;3-电话;4-Wap;5-App
 * @apiParam (请求参数) {String} dataTrack 数据跟踪
 * @apiParam (请求参数) {String} [txAcctNo] 交易账号
 * @apiParam (请求参数) {String} hbOneNo 一账通账号(必填)
 *
 * @apiSuccess (返回参数) {String} returnCode 返回码
 * @apiSuccess (返回参数) {String} description 返回描述
 * @apiSuccess (返回参数) {List} tradeDetailList 交易明细列表
 * @apiSuccess (返回参数) {String} tradeDetailList.fundCode 基金代码
 * @apiSuccess (返回参数) {String} tradeDetailList.orderDate 订单日期(yyyyMMdd)
 * @apiSuccess (返回参数) {String} tradeDetailList.operationType 订单操作类型(1-买入,2-卖出)
 * @apiSuccess (返回参数) {BigDecimal} tradeDetailList.orderAmount 订单金额
 * @apiSuccess (返回参数) {String} tradeDetailList.orderNo 订单号
 *
 * @description: 查询主动买卖统计数据接口，支持查询代销和直销的买卖交易记录
 * <AUTHOR>
 * @date 2025/9/19 20:30
 * @since JDK 1.8
 */
public interface QueryActiveBuySellStatsFacade extends BaseFacade<QueryActiveBuySellStatsRequest, QueryActiveBuySellStatsResponse> {

}
