/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.tms.high.orders.facade.search.queryactivebuysellstats;

import com.howbuy.tms.high.orders.facade.common.BaseDto;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * @description: 交易明细信息
 * <AUTHOR>
 * @date 2025/9/15 20:30
 * @since JDK 1.8
 */
@Getter
@Setter
public class TradeDetailInfoResponse extends BaseDto {


    private static final long serialVersionUID = -8311583840597772117L;
    /**
     * 基金代码
     */
    private String fundCode;

    /**
     * 订单日期(yyyyMMdd)
     */
    private String orderDate;

    /**
     * 订单操作类型(1-买入,2-卖出)
     */
    private String operationType;

    /**
     * 订单金额
     */
    private BigDecimal orderAmount;

    /**
     * 订单号
     */
    private String orderNo;
}
