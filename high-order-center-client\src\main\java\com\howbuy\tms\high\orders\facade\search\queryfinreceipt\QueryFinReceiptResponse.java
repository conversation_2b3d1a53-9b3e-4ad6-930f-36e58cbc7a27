/**
 * Copyright (c) 2017, <PERSON>gH<PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.orders.facade.search.queryfinreceipt;

import com.howbuy.tms.high.orders.facade.common.OrderSearchBaseResponse;
import com.howbuy.tms.high.orders.facade.search.queryacctbalance.QueryAcctBalanceResponse;

import java.math.BigDecimal;
import java.util.List;

/**
 * @description:(查询资金到账提醒数据接口返回结果)
 * @reason:
 * <AUTHOR> @date 2023年1月10日 下午7:55:20
 * @since JDK 1.8
 */
public class QueryFinReceiptResponse extends OrderSearchBaseResponse {

    private static final long serialVersionUID = -1672264945538427945L;


    /**
     * 交易账号
     */
    private String txAcctNo;
    /**
     * 一账通账号
     */
    private String hbOneNo;

    /**
     * 购买待退款订单数
     */
    private Integer buyUnrefundedPiece;
    /**
     * 赎回待回款订单数
     */
    private Integer redeemUnrefundedPiece;

    /**
     * 买入待确认金额
     */
    private BigDecimal totalBuyUnConfirmAmt;

    /**
     * 即将资金到账金额
     */
    private BigDecimal totalUnRefundAmt;

    /**
     * 即将资金到账订单数
     */
    private int totalUnRefundNum;

    /**
     * 总在途订单数:包含待付款订单数+待确认订单数
     */
    private int totalOnWayNum;

    /**
     * 待付款订单
     */
    private List<QueryAcctBalanceResponse.DealOrderBean> unpaidList;
    /**
     * 待确认订单
     */
    private List<QueryAcctBalanceResponse.DealOrderBean> unconfirmedList;

    public String getTxAcctNo() {
        return txAcctNo;
    }

    public void setTxAcctNo(String txAcctNo) {
        this.txAcctNo = txAcctNo;
    }

    public String getHbOneNo() {
        return hbOneNo;
    }

    public void setHbOneNo(String hbOneNo) {
        this.hbOneNo = hbOneNo;
    }

    public Integer getBuyUnrefundedPiece() {
        return buyUnrefundedPiece;
    }

    public void setBuyUnrefundedPiece(Integer buyUnrefundedPiece) {
        this.buyUnrefundedPiece = buyUnrefundedPiece;
    }

    public int getTotalOnWayNum() {
        return totalOnWayNum;
    }

    public void setTotalOnWayNum(int totalOnWayNum) {
        this.totalOnWayNum = totalOnWayNum;
    }

    public BigDecimal getTotalBuyUnConfirmAmt() {
        return totalBuyUnConfirmAmt;
    }

    public void setTotalBuyUnConfirmAmt(BigDecimal totalBuyUnConfirmAmt) {
        this.totalBuyUnConfirmAmt = totalBuyUnConfirmAmt;
    }

    public BigDecimal getTotalUnRefundAmt() {
        return totalUnRefundAmt;
    }

    public void setTotalUnRefundAmt(BigDecimal totalUnRefundAmt) {
        this.totalUnRefundAmt = totalUnRefundAmt;
    }

    public int getTotalUnRefundNum() {
        return totalUnRefundNum;
    }

    public void setTotalUnRefundNum(int totalUnRefundNum) {
        this.totalUnRefundNum = totalUnRefundNum;
    }


    public Integer getRedeemUnrefundedPiece() {
        return redeemUnrefundedPiece;
    }

    public void setRedeemUnrefundedPiece(Integer redeemUnrefundedPiece) {
        this.redeemUnrefundedPiece = redeemUnrefundedPiece;
    }

    public List<QueryAcctBalanceResponse.DealOrderBean> getUnpaidList() {
        return unpaidList;
    }

    public void setUnpaidList(List<QueryAcctBalanceResponse.DealOrderBean> unpaidList) {
        this.unpaidList = unpaidList;
    }

    public List<QueryAcctBalanceResponse.DealOrderBean> getUnconfirmedList() {
        return unconfirmedList;
    }

    public void setUnconfirmedList(List<QueryAcctBalanceResponse.DealOrderBean> unconfirmedList) {
        this.unconfirmedList = unconfirmedList;
    }
}
