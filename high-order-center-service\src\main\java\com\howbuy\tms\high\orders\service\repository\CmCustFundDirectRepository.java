package com.howbuy.tms.high.orders.service.repository;

import com.howbuy.tms.high.orders.dao.mapper.customize.CmCustFundDirectPoMapper;
import com.howbuy.tms.high.orders.dao.po.CmCustFundDirectPo;
import com.howbuy.tms.high.orders.dao.vo.ConfirmBalanceVo;
import com.howbuy.tms.high.orders.dao.vo.QueryAcctBalanceBaseInfoParamVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class CmCustFundDirectRepository {
    @Autowired
    private CmCustFundDirectPoMapper cmCustFundDirectPoMapper;

    public List<CmCustFundDirectPo> selectDirectBalance(String hbOneNo, String balanceStatus, List<String> disCodeList) {
        return cmCustFundDirectPoMapper.selectDirectBalance(hbOneNo, balanceStatus, disCodeList);
    }

    public List<CmCustFundDirectPo> queryConfirmBalanceBaseInfo(QueryAcctBalanceBaseInfoParamVo paramVo) {
        return cmCustFundDirectPoMapper.queryConfirmBalanceBaseInfo(paramVo);
    }

    public List<String> queryBalanceTxAcctNoByFundCode(String fundCode) {
        return cmCustFundDirectPoMapper.queryBalanceTxAcctNoByFundCode(fundCode);
    }

    public List<String> queryBalanceHbOneNoByPage(int pageNo, int pageSize) {
        int offset = (pageNo - 1) * pageSize;
        return cmCustFundDirectPoMapper.queryBalanceHbOneNoByPage(offset, pageSize);
    }

    public List<ConfirmBalanceVo> queryUnHkConfirmBalanceBaseInfo(String hboneNo, List<String> disCodeList, String fundCode) {
        return cmCustFundDirectPoMapper.queryUnHkConfirmBalanceBaseInfo(hboneNo, disCodeList, fundCode);

    }

    /**
     * 查询直销清仓持仓信息（持仓为0但非香港产品）
     */
    public List<ConfirmBalanceVo> queryDirectLiquidationBalance(String hboneNo, List<String> disCodeList, String fundCode) {
        return cmCustFundDirectPoMapper.queryDirectLiquidationBalance(hboneNo, disCodeList, fundCode);
    }
}
