/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.tms.high.orders.service.common.utils;

import com.howbuy.tms.common.enums.busi.ProductDBTypeEnum;
import com.howbuy.tms.common.enums.busi.StandardFixedIncomeFlagEnum;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashSet;
import java.util.Set;

/**
 * @description: 交易工具类
 * <AUTHOR>
 * @date 2025/9/4 17:01
 * @since JDK 1.8
 */
public class TradeUtils {

    private static final Logger log = LoggerFactory.getLogger(TradeUtils.class);
    /**
     * 净值型产品包括：阳光私募/固定收益-券商小集合/固定收益-现金管理（本次变动）
     *
     * @param standardFixedIncomeFlag 标准固收标识(固收类有此标识:0-非标准固收，1-标准固收，2-现金管理， 3-券商集合理财 4-纯债产品)
     * @param productSubType          产品子类型(好买产品线)
     * @param fundCode                基金代码
     * @return 是否是净值型产品:true,净值型产品;false,非净值型产品
     */
    public static boolean isNavTypeFund(String standardFixedIncomeFlag, String productSubType, String fundCode) {
        if (StringUtils.isBlank(productSubType)) {
            log.info("isNavTypeFund-没有productSubType,非净值型产品,fundCode={}", fundCode);
            return false;
        }
        // 阳光私募:【产品子类型ProductSubType】≠2-类固定收益/5-私募股权
        if (!ProductDBTypeEnum.GUDINGSHOUYI.getCode().equals(productSubType) && !ProductDBTypeEnum.GUQUAN.getCode().equals(productSubType)) {
            log.info("isNavTypeFund,阳光私募属于净值型产品,fundCode={}", fundCode);
            return true;
        }
        // 固定收益-券商小集合/固定收益-现金管理
        if (ProductDBTypeEnum.GUDINGSHOUYI.getCode().equals(productSubType) && StandardFixedIncomeFlagEnum.CASH_MANAGER.getCode().equals(standardFixedIncomeFlag) ||
                ProductDBTypeEnum.GUDINGSHOUYI.getCode().equals(productSubType) && StandardFixedIncomeFlagEnum.BROKERAGE_COLLECTION_GS.getCode().equals(standardFixedIncomeFlag)) {
            log.info("isNavTypeFund,固定收益-券商小集合/固定收益-现金管理,fundCode={}", fundCode);
            return true;
        }
        return false;
    }

    /**
     * 净值型产品包括：阳光私募/固定收益-券商小集合
     *
     * @param standardFixedIncomeFlag 标准固收标识(固收类有此标识:0-非标准固收，1-标准固收，2-现金管理， 3-券商集合理财 4-纯债产品)
     * @param productSubType          产品子类型(好买产品线)
     * @param fundCode                基金代码
     * @return 是否是净值型产品:true,净值型产品;false,非净值型产品
     */
    public static boolean isSunshineAndBrokerage(String standardFixedIncomeFlag, String productSubType, String fundCode) {
        if (StringUtils.isBlank(productSubType)) {
            log.info("isNavTypeFund-没有productSubType,非净值型产品,fundCode={}", fundCode);
            return false;
        }
        // 阳光私募:【产品子类型ProductSubType】≠2-类固定收益/5-私募股权
        if (!ProductDBTypeEnum.GUDINGSHOUYI.getCode().equals(productSubType) && !ProductDBTypeEnum.GUQUAN.getCode().equals(productSubType)) {
            log.info("isNavTypeFund,阳光私募属于净值型产品,fundCode={}", fundCode);
            return true;
        }
        // 固定收益-券商小集合/固定收益-现金管理
        if (ProductDBTypeEnum.GUDINGSHOUYI.getCode().equals(productSubType) && StandardFixedIncomeFlagEnum.BROKERAGE_COLLECTION_GS.getCode().equals(standardFixedIncomeFlag)) {
            log.info("isNavTypeFund,固定收益-券商小集合,fundCode={}", fundCode);
            return true;
        }
        return false;
    }
}
