/**
 * Copyright (c) 2017, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */


package com.howbuy.tms.high.orders.facade.search.queryacctbalancewithouthk;

import com.howbuy.tms.common.enums.busi.DisCodeEnum;
import com.howbuy.tms.common.enums.busi.YesOrNoEnum;
import com.howbuy.tms.high.orders.facade.common.OrderSearchBaseRequest;
import com.howbuy.tms.high.orders.facade.enums.QueryBalanceStatusEnum;
import com.howbuy.tms.high.orders.facade.enums.TxCodes;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description:(查询客户持仓接口请求参数)
 * @reason:
 * @date 2018年6月21日 下午4:55:44
 * @since JDK 1.7
 */
@Data
public class QueryAcctBalanceWithoutHkRequest extends OrderSearchBaseRequest {

    private static final long serialVersionUID = 2435434454295240441L;


    public QueryAcctBalanceWithoutHkRequest() {
        setTxCode(TxCodes.QUERY_ACC_BALANCE_WITHOUT_HK);
        setDisCode(DisCodeEnum.HM.getCode());
    }

    /**
     * 产品代码
     */
    private String productCode;

    /**
     * 分销机构代码列表-股权直销改造
     */
    private List<String> disCodeList;

    /**
     * 兼容老逻辑,注意,字段不传也是查持仓的
     * 持仓状态,0:不持仓,1:持仓,2:全部
     * 默认查持仓的
     */
    private String balanceStatus =  QueryBalanceStatusEnum.HOLD.getDesc();


    /**
     * 是否查询清仓待回款的,1:查询,0:不查询,默认不查询
     */
    private String queryWithAllRedeemWaiteRefund= YesOrNoEnum.NO.getCode();


    /**
     * 不过滤香港产品,1:是,0:否
     */
    private String notFilterHkFund;

    /**
     * 不过滤好臻产品,1:是,0:否
     */
    private String notFilterHzFund;

}

