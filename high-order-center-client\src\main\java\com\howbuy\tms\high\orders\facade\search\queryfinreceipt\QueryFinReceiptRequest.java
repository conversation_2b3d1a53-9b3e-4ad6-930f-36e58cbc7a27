/**
 * Copyright (c) 2017, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */


package com.howbuy.tms.high.orders.facade.search.queryfinreceipt;

import com.howbuy.tms.common.enums.busi.DisCodeEnum;
import com.howbuy.tms.common.enums.busi.YesOrNoEnum;
import com.howbuy.tms.high.orders.facade.common.OrderSearchBaseRequest;
import com.howbuy.tms.high.orders.facade.enums.TxCodes;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @description:(查询资金到账提醒数据接口请求参数)
 * @reason:
 * @date 2018年6月21日 下午4:55:44
 * @since JDK 1.7
 */
@Getter
@Setter
public class QueryFinReceiptRequest extends OrderSearchBaseRequest {

    private static final long serialVersionUID = -3882167711821963597L;

    public QueryFinReceiptRequest() {
        setTxCode(TxCodes.HIGH_FIN_RECEIPT);
        setDisCode(DisCodeEnum.HM.getCode());
    }

    /**
     * 分销机构代码列表
     */
    private List<String> disCodeList;
    /**
     * 是否授权
     */
    @Deprecated
    private String isAuth = YesOrNoEnum.YES.getCode();
    /**
     * 是否仅需香港产品
     */
    private String onlyHkProduct;

    /**
     * 不过滤香港产品,1:是,0:否
     */
    private String notFilterHkFund;

    /**
     * 不过滤好臻产品,1:是,0:否
     */
    private String notFilterHzFund;

}

