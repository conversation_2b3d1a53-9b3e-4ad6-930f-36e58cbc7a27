/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.orders.facade.search.querymemberinfo.enums;

/**
 * @description: 会员背景色枚举
 * <AUTHOR>
 * @date 2025/9/9
 * @since JDK 1.8
 */
public enum BackgroundColorEnum {

    /**
     * 红色背景
     */
    RED("1", "红色"),
    
    /**
     * 黑色背景
     */
    BLACK("2", "黑色");

    private final String code;
    private final String description;

    BackgroundColorEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据代码获取枚举
     * 
     * @param code 背景色代码
     * @return 背景色枚举
     */
    public static BackgroundColorEnum getByCode(String code) {
        if (code == null) {
            return null;
        }
        for (BackgroundColorEnum color : BackgroundColorEnum.values()) {
            if (color.code.equals(code)) {
                return color;
            }
        }
        return null;
    }
}
