---
type: "agent_requested"
description: "Example description"
---

# 单元测试执行指南

## 🚀 快速使用

### 方式1: 使用PowerShell脚本（推荐）
```powershell
# 完整执行（包含依赖安装）
.\run-unit-test.ps1 QueryFundPositionDateServiceTestM

# 快速执行（跳过依赖安装）
.\run-unit-test.ps1 QueryFundPositionDateServiceTestM -SkipDependencies
```

### 方式2: 手动执行命令
```powershell
# 1. 安装依赖模块
mvn -pl high-order-center-dao install "-Dfile.encoding=UTF-8"
mvn -pl high-order-center-client install "-Dfile.encoding=UTF-8"

# 2. 执行测试
mvn -pl high-order-center-service test "-Dtest=TestClassName" "-Djacoco.skip=true" "-Dfile.encoding=UTF-8"
```

## 📋 核心依赖关系

```
high-order-center-service (测试模块)
    ↓ 依赖
high-order-center-client
    ↓ 依赖  
high-order-center-dao
```

**关键**: 必须按依赖顺序先安装被依赖的模块。

## ⚙️ 配置要求

### Service模块pom.xml
```xml
<plugin>
    <groupId>org.apache.maven.plugins</groupId>
    <artifactId>maven-surefire-plugin</artifactId>
    <version>2.22.2</version>
    <configuration>
        <encoding>UTF-8</encoding>
        <skipTests>false</skipTests>
        <includes>
            <include>**/*TestM.java</include>
        </includes>
    </configuration>
</plugin>
```

### 测试文件命名
- 测试类必须以 `TestM.java` 结尾
- 例如: `QueryFundPositionDateServiceTestM.java`

## ✅ 验证结果

脚本执行成功后会显示：
- ✓ Test execution successful!
- Test Summary: Tests run: 9, Failures: 0, Errors: 0, Skipped: 0

## 🔧 常见问题

| 问题 | 解决方案 |
|------|----------|
| 编译错误 - 找不到类 | 按依赖顺序重新安装模块 |
| 测试被跳过 | 确保pom.xml中配置了`<skipTests>false</skipTests>` |
| JaCoCo错误 | 脚本已自动添加`-Djacoco.skip=true`参数 |

---

**工具版本**: v2.0 (精简版)  
**验证状态**: ✅ 已验证可用  
**最后更新**: 2025-09-15
