package com.howbuy.tms.high.orders.service.facade.search.queryCustBalanceFund;

import com.alibaba.fastjson.JSON;
import com.howbuy.tms.common.constant.ExceptionCodes;
import com.howbuy.tms.common.outerservice.cc.center.queryhboneno.QueryHbOneNoOuterService;
import com.howbuy.tms.high.orders.facade.search.queryCustBalanceFund.QueryCustBalanceFundFacade;
import com.howbuy.tms.high.orders.facade.search.queryCustBalanceFund.QueryCustBalanceFundRequest;
import com.howbuy.tms.high.orders.facade.search.queryCustBalanceFund.QueryCustBalanceFundResponse;
import com.howbuy.tms.high.orders.service.service.acctbalancebaseinfo.AcctBalanceBaseInfoService;
import com.howbuy.tms.high.orders.service.service.acctbalancebaseinfo.bean.AcctBalanceBaseInfo;
import com.howbuy.tms.high.orders.service.service.acctbalancebaseinfo.bean.QueryAcctBalanceBaseParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description:查询用户持仓基金
 * @Author: yun.lu
 * Date: 2024/7/23 15:20
 */
@DubboService
@Service
@Slf4j
public class QueryCustBalanceFundService implements QueryCustBalanceFundFacade {
    @Autowired
    private QueryHbOneNoOuterService queryHbOneNoOuterService;
    @Autowired
    private AcctBalanceBaseInfoService acctBalanceBaseInfoService;

    @Override
    public QueryCustBalanceFundResponse execute(QueryCustBalanceFundRequest request) {
        QueryCustBalanceFundResponse response = new QueryCustBalanceFundResponse();
        response.setReturnCode(ExceptionCodes.SUCCESS);
        // 1.参数校验
        String txAcctNo = request.getTxAcctNo();
        String hbOneNo = request.getHbOneNo();
        if (StringUtils.isBlank(txAcctNo) && StringUtils.isBlank(hbOneNo)) {
            log.info("查询用户持仓基金,交易账号与一账通不能同时为空");
            response.setReturnCode(ExceptionCodes.PARAMS_ERROR);
            return response;
        }
        if (StringUtils.isEmpty(txAcctNo)) {
            txAcctNo = queryHbOneNoOuterService.queryTxAcctNoByHbOneNo(hbOneNo);
        }
        if (StringUtils.isEmpty(hbOneNo)) {
            hbOneNo = queryHbOneNoOuterService.queryHbOneNoByTxAcctNo(txAcctNo);
        }
        // 2.查询持仓基金
        QueryAcctBalanceBaseParam queryAcctBalanceBaseParam = new QueryAcctBalanceBaseParam();
        queryAcctBalanceBaseParam.setTxAcctNo(txAcctNo);
        queryAcctBalanceBaseParam.setHbOneNo(hbOneNo);
        queryAcctBalanceBaseParam.setIncludeDirect(true);
        queryAcctBalanceBaseParam.setDisCodeList(request.getDisCodeList());
        List<AcctBalanceBaseInfo> balanceBaseInfoList = acctBalanceBaseInfoService.queryAcctBalanceBaseInfo(queryAcctBalanceBaseParam);
        if (CollectionUtils.isEmpty(balanceBaseInfoList)) {
            log.info("该用户没有持仓,queryAcctBalanceBaseParam={}", JSON.toJSONString(queryAcctBalanceBaseParam));
            return response;
        }
        List<String> fundCodeList = balanceBaseInfoList.stream().map(AcctBalanceBaseInfo::getFundCode).distinct().collect(Collectors.toList());
        response.setFundCodeList(fundCodeList);
        return response;
    }
}
