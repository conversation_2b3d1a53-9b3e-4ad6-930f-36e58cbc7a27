---
type: "agent_requested"
description: "Example description"
---

# Maven多模块项目单元测试执行规则文档

## 核心依赖关系

```
high-order-center-service (测试模块)
    ↓ 依赖
high-order-center-client
    ↓ 依赖
high-order-center-dao
```

**关键点**: 必须按依赖顺序先安装被依赖的模块，否则编译失败。

## 标准执行流程

```powershell
# 1. 安装DAO模块
mvn -pl high-order-center-dao install "-Dfile.encoding=UTF-8"

# 2. 安装Client模块
mvn -pl high-order-center-client install "-Dfile.encoding=UTF-8"
```

### 步骤2: 执行测试

```powershell
# 执行指定测试类
mvn -pl high-order-center-service test "-Dtest=TestClassName" "-Djacoco.skip=true" "-Dfile.encoding=UTF-8"
```

## 一键执行命令模板

```powershell
# 完整流程（复制即用）
mvn -pl high-order-center-dao install "-Dfile.encoding=UTF-8" && mvn -pl high-order-center-client install "-Dfile.encoding=UTF-8" && mvn -pl high-order-center-service test "-Dtest=YourTestClassName" "-Djacoco.skip=true" "-Dfile.encoding=UTF-8"
```

## 关键配置要求

### Service模块pom.xml配置
```xml
<plugin>
    <groupId>org.apache.maven.plugins</groupId>
    <artifactId>maven-surefire-plugin</artifactId>
    <version>2.22.2</version>
    <configuration>
        <encoding>UTF-8</encoding>
        <skipTests>false</skipTests>
        <includes>
            <include>**/*TestM.java</include>
        </includes>
    </configuration>
</plugin>
```

### 测试文件命名规范
- 测试类必须以 `TestM.java` 结尾
- 例如: `QueryFundPositionDateServiceTestM.java`

## 常见问题解决

### 编译错误 - 找不到类
**解决**: 按依赖顺序重新安装模块

### 测试被跳过
**解决**: 确保service模块pom.xml中配置了`<skipTests>false</skipTests>`

### JaCoCo错误
**解决**: 添加参数`-Djacoco.skip=true`

---

**文档版本**: v2.0 (精简版)
**最后更新**: 2025-09-15
