---
type: "agent_requested"
description: "Example description"
---

## 项目介绍
	高端订单服务（high-order-center）是一个专注于金融和基金行业的交易处理系统，主要用于处理高端基金产品的订单交易，提供丰富的查询和交易功能。系统面向金融行业的公募和私募基金业务，以及保险业务，支持中国大陆和香港地区的业务需求。
    
## 版本信息
    Spring Cloud Alibaba Version	Spring Cloud Version	    Spring Boot Version
    2.2.9.RELEASE                   Spring Cloud Hoxton.SR12    2.3.12.RELEASE

## 主要功能
1. 客户产品查询：查询客户持仓基金、账户余额、交易订单等
2. 产品交易处理：支持基金的申购、赎回、分红等业务操作
3. 风险匹配评估：提供客户风险等级与产品风险等级的匹配评估
4. 交易状态查询：查询交易订单处理状态和结果
5. 产品限额管理：管理产品的购买限额和额度控制

## 项目结构
- high-order-center-client：对外提供的接口和模型定义
- high-order-center-service：实现业务逻辑层
- high-order-center-dao：数据访问层，定义数据模型和DAO接口
- high-order-trade-remote：交易远程服务
- high-order-search-remote：搜索远程服务

## 技术栈
- 框架：Spring Boot, Spring Cloud
- 微服务通信：Dubbo
- 数据库：MySQL
- ORM：Mybatis
- 消息队列：RocketMQ, ActiveMQ
- 缓存：Redis
- 服务注册与发现：Nacos, Zookeeper

## 结构定义
    facade      对外接口dubbo
    controller  对外接口http
    listener    外部消息监听
    service     业务逻辑
    repository  仓储
    mapper      <red>只能由repository访问</red>

## 事务规约
    1、事务只能加在repository层
    2、事务内不能包含外部服务调用
    3、事务尽量小

## 系统链路
    [controller/facade/scheduler/listener] -> service -> repository -> mapper
            ||
            \/
          service
            ||
            \/
         repository
            ||
            \/
          mapper

## 关键业务模型
- CustBooksPo：客户账本信息
- SubCustBooksPo：子账本信息
- DealOrderPo：交易订单信息
- HighDealOrderDtlPo：高端交易订单详情

## 开发规范
1. 遵循接口设计规范，所有对外接口需有详细的接口文档
2. 代码提交前需通过单元测试和代码质量检查
3. 遵循代码分层原则，不允许跨层调用
4. 使用@Setter/@Getter替代@Data注解
5. 禁止使用BeanUtils.copyProperties进行对象属性复制

## 环境配置
- 开发环境：Java 8, Maven 3.6+
- 开发工具：建议使用IntelliJ IDEA或Eclipse
- 代码库：Git版本控制