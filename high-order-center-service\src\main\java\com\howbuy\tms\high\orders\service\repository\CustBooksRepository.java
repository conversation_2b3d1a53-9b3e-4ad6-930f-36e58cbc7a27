package com.howbuy.tms.high.orders.service.repository;

import com.howbuy.tms.high.orders.dao.mapper.customize.CustBooksPoMapper;
import com.howbuy.tms.high.orders.dao.po.CmCusttradeDirectPo;
import com.howbuy.tms.high.orders.dao.po.CustBooksPo;
import com.howbuy.tms.high.orders.dao.po.CustBooksPoExample;
import com.howbuy.tms.high.orders.dao.vo.BalanceVo;
import com.howbuy.tms.high.orders.dao.vo.ConfirmBalanceVo;
import com.howbuy.tms.high.orders.dao.vo.FundAcctVo;
import com.howbuy.tms.high.orders.dao.vo.QueryAcctBalanceBaseInfoParamVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

@Repository
public class CustBooksRepository {
    @Autowired
    private CustBooksPoMapper custBooksPoMapper;


    public List<String> queryBalanceTxAcctNoByFundCode(String fundCode) {
        return custBooksPoMapper.queryBalanceTxAcctNoByFundCode(fundCode);
    }

    public CustBooksPo selectCustAllBooksByTxAcctNoAndFundCode(String txAcctNo, String fundCode, List<String> disCodeList) {
        return custBooksPoMapper.selectCustAllBooksByTxAcctNoAndFundCode(txAcctNo, fundCode, disCodeList);
    }

    public List<CustBooksPo> selectStockBooksByTxAcctNoAndFundCode(String txAcctNo, String fundCode) {
        return custBooksPoMapper.selectStockBooksByTxAcctNoAndFundCode(txAcctNo, fundCode);
    }

    public List<String> selectStockBooksByTxAcctNoAndFundCodeBatch(String txAcctNo, List<String> fundCodes) {
        return custBooksPoMapper.selectStockBooksByTxAcctNoAndFundCodeBatch(txAcctNo, fundCodes);
    }

    public List<String> selectStockBooksByTxAcctNoAndDisCode(String txAcctNo, String disCode) {
        return custBooksPoMapper.selectStockBooksByTxAcctNoAndDisCode(txAcctNo, disCode);
    }

    public List<CustBooksPo> selectThreeElementsByTxAcctNoAndFundCode(String txAcctNo, String fundCode) {
        return custBooksPoMapper.selectThreeElementsByTxAcctNoAndFundCode(txAcctNo, fundCode);
    }

    public int selectInTransit(CustBooksPo custBooksPo) {
        return custBooksPoMapper.selectInTransit(custBooksPo);
    }

    public List<CustBooksPo> selectBalanceList(String txAcctNo, List<String> disCodeList, String protocolType, String productCode) {
        return custBooksPoMapper.selectBalanceList(txAcctNo, disCodeList, protocolType, productCode);
    }

    public List<BalanceVo> selectBalanceWithLockPeriod(List<String> disCodeList, String txAcctNo, List<String> fundCodeList, String balanceStatus) {
        return custBooksPoMapper.selectBalanceWithLockPeriod(disCodeList, txAcctNo, fundCodeList, balanceStatus);
    }

    public List<BalanceVo> selectBalanceDtl(String disCode, String txAcctNo, String protocolNo, String productCode, String cpAcctNo, String taTradeDt) {
        return custBooksPoMapper.selectBalanceDtl(disCode, txAcctNo, protocolNo, productCode, cpAcctNo, taTradeDt);
    }

    public List<BalanceVo> selectBalanceDtlByDisCodeList(List<String> disCodeList, String txAcctNo, String protocolNo, String productCode, String cpAcctNo, String taTradeDt) {
        return custBooksPoMapper.selectBalanceDtlByDisCodeList(disCodeList, txAcctNo, protocolNo, productCode, cpAcctNo, taTradeDt);
    }

    public List<String> selectHisCpAcctNo(String txAcctNo, String disCode, String fundCode, String taCode) {
        return custBooksPoMapper.selectHisCpAcctNo(txAcctNo, disCode, fundCode, taCode);
    }

    public BigDecimal selectBuyOnWayAmt(List<String> disCodeList, String txAcctNo) {
        return custBooksPoMapper.selectBuyOnWayAmt(disCodeList, txAcctNo);
    }

    public CustBooksPo selectByProtocolNoAndCpAcctNo(String txAcctNo, String protocolNo, String cpAcctNo, String productCode, String disCode, String fundShareClass, String protocolType) {
        return custBooksPoMapper.selectByProtocolNoAndCpAcctNo(txAcctNo, protocolNo, cpAcctNo, productCode, disCode, fundShareClass, protocolType);
    }

    public List<CustBooksPo> getCustBalanceDetail(String txAcctNo, String disCode, List<String> fundCodes, String cpAcctNo, String productChannel) {
        return custBooksPoMapper.getCustBalanceDetail(txAcctNo, disCode, fundCodes, cpAcctNo, productChannel);
    }

    public List<CustBooksPo> selectUnconfirmedVol(List<String> disCodeList, String txAcctNo, String fundCode) {
        return custBooksPoMapper.selectUnconfirmedVol(disCodeList, txAcctNo, fundCode);
    }

    public List<CustBooksPo> selectUnconfirmedVolByCpAcctNo(List<String> disCodeList, String txAcctNo, String fundCode, String cpAcctNo) {
        return custBooksPoMapper.selectUnconfirmedVolByCpAcctNo(disCodeList, txAcctNo, fundCode, cpAcctNo);
    }

    public List<CustBooksPo> selectCustBooksByTxAcctNoOrFundCodes(String txAcctNo, List<String> fundCodes) {
        return custBooksPoMapper.selectCustBooksByTxAcctNoOrFundCodes(txAcctNo, fundCodes);
    }

    public List<BalanceVo> selectBalanceDtlWithOpenDt(List<String> disCodeList, String txAcctNo, String productCode) {
        return custBooksPoMapper.selectBalanceDtlWithOpenDt(disCodeList, txAcctNo, productCode);
    }

    public List<CustBooksPo> selectConsignmentOnWayAmt(List<String> disCodeList, String txAcctNo) {
        return custBooksPoMapper.selectConsignmentOnWayAmt(disCodeList, txAcctNo);
    }


    public int getUnAckNum(String txAcctNo, String productCode) {
        return custBooksPoMapper.getUnAckNum(txAcctNo, productCode);
    }

    public List<BalanceVo> selectNetBuyAmountQX(String hbOneNo, List<String> fundCodeList) {
        return custBooksPoMapper.selectNetBuyAmountQX(hbOneNo, fundCodeList);
    }

    public List<FundAcctVo> selectHzBalanceByFundCode(String fundCode, String txAcctNo) {
        return custBooksPoMapper.selectHzBalanceByFundCode(fundCode, txAcctNo);
    }

    public List<BalanceVo> queryConfirmBalanceBaseInfo(QueryAcctBalanceBaseInfoParamVo paramVo) {
        return custBooksPoMapper.queryConfirmBalanceBaseInfo(paramVo);
    }

    public int isIntransitForCloseAct(String txAcctNo, String disCode, List<String> cpAcctNos, String protocolNo) {
        return custBooksPoMapper.isIntransitForCloseAct(txAcctNo, disCode, cpAcctNos, protocolNo);
    }

    public List<BalanceVo> getHighCustBalanceDetail(String txAcctNo, String disCode, String fundCode, List<String> cpAcctNos) {
        return custBooksPoMapper.getHighCustBalanceDetail(txAcctNo, disCode, fundCode, cpAcctNos);
    }

    public List<CustBooksPo> queryByFundCode(List<String> fundCodeList) {
        CustBooksPoExample custBooksPoExample = new CustBooksPoExample();
        custBooksPoExample.createCriteria().andProductCodeIn(fundCodeList);
        return custBooksPoMapper.selectByExample(custBooksPoExample);
    }

    public List<String> queryBalanceTxAcctNoByPage(int pageNum, int pageSize) {
        int offset = (pageNum - 1) * pageSize;
        return custBooksPoMapper.queryBalanceTxAcctNoByPage(offset, pageSize);
    }

    public List<String> queryUnConfirmedOrderFundCode(Set<String> unBalanceFundCodeSet, String txAcctNo) {
        return custBooksPoMapper.queryUnConfirmedOrderFundCode(unBalanceFundCodeSet, txAcctNo);
    }

    public List<ConfirmBalanceVo> queryAgentConfirmBalance(String txAcctNo, String fundCode, List<String> disCodeList) {
        return custBooksPoMapper.queryAgentConfirmBalance(txAcctNo, fundCode, disCodeList);
    }

    /**
     * 查询代销清仓持仓信息（持仓为0但存在确认交易）
     */
    public List<ConfirmBalanceVo> queryAgentLiquidationBalance(String txAcctNo, String fundCode, List<String> disCodeList) {
        return custBooksPoMapper.queryAgentLiquidationBalance(txAcctNo, fundCode, disCodeList);
    }
}
