/**
 * Copyright (c) 2020, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.tms.high.orders.service.facade.search.querysupsignagreementlist;

import com.howbuy.interlayer.product.model.HighProductBaseInfoModel;
import com.howbuy.tms.common.constant.ExceptionCodes;
import com.howbuy.tms.common.enums.busi.DisCodeEnum;
import com.howbuy.tms.common.enums.busi.YesOrNoEnum;
import com.howbuy.tms.common.outerservice.acccenter.querycustbankcard.QueryCustBankCardContext;
import com.howbuy.tms.common.outerservice.acccenter.querycustbankcard.QueryCustBankCardOuterService;
import com.howbuy.tms.common.outerservice.acccenter.querycustbankcard.QueryCustBankCardResult;
import com.howbuy.tms.common.outerservice.acccenter.querycustmobile.QueryCustMobileContext;
import com.howbuy.tms.common.outerservice.acccenter.querycustmobile.QueryCustMobileOutService;
import com.howbuy.tms.common.outerservice.acccenter.querycustmobile.QueryCustMobileResult;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.QueryHighProductOuterService;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.CustSupSignAgreementBean;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductControlBean;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductDBInfoBean;
import com.howbuy.tms.high.orders.dao.po.CustBooksPo;
import com.howbuy.tms.high.orders.facade.search.querysupsignagreementlist.QuerySupSignAgreementListFacade;
import com.howbuy.tms.high.orders.facade.search.querysupsignagreementlist.QuerySupSignAgreementListRequest;
import com.howbuy.tms.high.orders.facade.search.querysupsignagreementlist.QuerySupSignAgreementListResponse;
import com.howbuy.tms.high.orders.facade.search.querysupsignagreementlist.QuerySupSignAgreementListResponse.SupSignAgreement;
import com.howbuy.tms.high.orders.facade.search.querysupsignagreementlist.QuerySupSignAgreementListResponse.SupSignFund;
import com.howbuy.tms.high.orders.service.repository.CustBooksRepository;
import com.howbuy.tms.high.orders.service.repository.CustEsignatureAgreementRepository;
import com.howbuy.tms.high.orders.service.repository.DealOrderRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 查询待补签协议列表
 *
 * <AUTHOR>
 * @date 2020/12/9 16:07
 * @since JDK 1.8
 */
@DubboService
@Service("querySupSignAgreementListFacade")
@Slf4j
public class QuerySupSignAgreementListFacadeService implements QuerySupSignAgreementListFacade {

    @Autowired
    private QueryHighProductOuterService queryHighProductOuterService;
    @Autowired
    private CustEsignatureAgreementRepository custEsignatureAgreementRepository;
    @Autowired
    private CustBooksRepository custBooksRepository;
    @Autowired
    private DealOrderRepository dealOrderRepository;
    @Autowired
    private QueryCustBankCardOuterService queryCustBankCardOuterService;
    @Autowired
    private QueryCustMobileOutService queryCustMobileOutService;

    /**
     * @api {DUBBO} com.howbuy.tms.high.orders.facade.search.querysupsignagreementlist.QuerySupSignAgreementListFacade.execute(QuerySupSignAgreementListRequest request)
     * @apiVersion 1.0.0
     * @apiGroup QuerySupSignAgreementListFacadeService
     * @apiName execute
     * @apiDescription 查询待补签协议列表
     * @apiParam (请求参数) {String} isHzAuth 是否好臻数据授权
     * @apiParam (请求参数) {String} txAcctNo 交易账号
     * @apiParam (请求参数) {String} hbOneNo 一账通账号
     * @apiParam (请求参数) {String} disCode 分销机构代码
     * @apiParam (请求参数) {String} outletCode 网点代码
     * @apiParam (请求参数) {String} appDt 申请日期
     * @apiParam (请求参数) {String} appTm 申请时间
     * @apiParam (请求参数) {Number} pageNo 页码
     * @apiParam (请求参数) {Number} pageSize 每页记录数
     * @apiParam (请求参数) {String} operIp 交易IP
     * @apiParam (请求参数) {String} txCode 交易码
     * @apiParam (请求参数) {String} txChannel 交易渠道 1-柜台,2-网站,3-电话,4-Wap,5-App,6-机构
     * @apiParam (请求参数) {String} dataTrack 数据跟踪
     * @apiParam (请求参数) {String} subOutletCode 子网点代码
     * @apiParamExample 请求参数示例
     * hbOneNo=FZvVdXLK7&pageSize=6221&disCode=8dbx2tBup&txChannel=i63Tvp8Qud&appTm=WFbk&subOutletCode=itELV5tqj&pageNo=7873&operIp=jZ7FZ0EICb&txAcctNo=Y&appDt=mm1IF1&dataTrack=PLVR5q1&txCode=b&outletCode=ZvS0nDX&isHzAuth=8pMO
     * @apiSuccess (响应结果) {Array} fundList 待签署协议列表
     * @apiSuccess (响应结果) {String} fundList.fundCode 产品编码
     * @apiSuccess (响应结果) {String} fundList.fundName 产品名称
     * @apiSuccess (响应结果) {String} fundList.disCode 分销编码
     * @apiSuccess (响应结果) {String} fundList.hkSaleFlag 是否香港产品,1:是;0:不是
     * @apiSuccess (响应结果) {String} fundList.taCode ta编码
     * @apiSuccess (响应结果) {String} fundList.mobile 手机号码
     * @apiSuccess (响应结果) {String} fundList.mobileMask 手机号
     * @apiSuccess (响应结果) {String} fundList.bankCode 银行编码
     * @apiSuccess (响应结果) {String} fundList.custBankId 银行id
     * @apiSuccess (响应结果) {String} fundList.bankAcct 用户银行账户
     * @apiSuccess (响应结果) {Array} fundList.agreementList 待签署协议列表
     * @apiSuccess (响应结果) {String} fundList.agreementList.agreementCode 协议编码
     * @apiSuccess (响应结果) {String} fundList.agreementList.agreementName 协议名称
     * @apiSuccess (响应结果) {String} fundList.agreementList.signEndDtm 签署截止日期
     * @apiSuccess (响应结果) {String} fundList.agreementList.signReason 签署原因
     * @apiSuccess (响应结果) {String} isHasHkProduct 是否有香港产品
     * @apiSuccess (响应结果) {String} isHasHzProduct 是否有好臻产品
     * @apiSuccess (响应结果) {String} returnCode 返回码
     * @apiSuccess (响应结果) {String} description 描述
     * @apiSuccess (响应结果) {Number} totalCount 总记录数
     * @apiSuccess (响应结果) {Number} totalPage 总页数
     * @apiSuccess (响应结果) {Number} pageNo 当前页
     * @apiSuccessExample 响应结果示例
     * {"returnCode":"d","totalPage":6049,"pageNo":9790,"fundList":[{"bankCode":"Of","taCode":"HG3","fundCode":"6LqbBmP","mobile":"Bgeb9W6U","bankAcct":"M8S","agreementList":[{"signReason":"TbGrAca3C","agreementName":"4q7b2frBk","agreementCode":"MvexZtl7","signEndDtm":"hFT"}],"disCode":"xaXy","custBankId":"Ie3TGH","fundName":"bQK7j","hkSaleFlag":"x8fFO8BYCB","mobileMask":"gRDNTqe"}],"isHasHzProduct":"Wpdv2Wkb","description":"zU2PxZfS","totalCount":9538,"isHasHkProduct":"MjBOV5N"}
     */
    @Override
    public QuerySupSignAgreementListResponse execute(QuerySupSignAgreementListRequest request) {
        QuerySupSignAgreementListResponse response = new QuerySupSignAgreementListResponse();
        response.setReturnCode(ExceptionCodes.SUCCESS);
        // 参数校验
        if (StringUtils.isBlank(request.getTxAcctNo())) {
            response.setReturnCode(ExceptionCodes.PARAMS_ERROR);
            response.setDescription("txAcctNo不能为空");
            return response;
        }
        // 查询有效待补签协议列表
        List<CustSupSignAgreementBean> allAgreementList = queryHighProductOuterService.queryCustSupSignAgreementList(request.getTxAcctNo());
        if (allAgreementList == null || allAgreementList.isEmpty()) {
            response.setFundList(Collections.emptyList());
            return response;
        }
        // 查询已补签协议列表
        Set<String> signedSet = querySignedSupSignAgreementSet(request.getTxAcctNo());
        // 获取剩余待补签协议列表
        List<SupSignFund> unsignAgreementList = getUnsignAgreementList(allAgreementList, signedSet);

        // 查询各基金持仓银行预留手机信息
        setMobile(unsignAgreementList, request);

        // 设置基金信息
        setFundInfo(unsignAgreementList);
        // 补签协议过滤
        List<SupSignFund> supSignFunds = filterSupSignAgreementList(unsignAgreementList, request, response);
        response.setFundList(supSignFunds);
        return response;
    }

    /**
     * 非好臻授权的,需要将香港+好臻的都剔除了
     *
     * @param agreementList 待签署协议
     * @param request       入参
     * @param response      返回结果
     * @return 过滤后的待签署列表
     */
    private List<SupSignFund> filterSupSignAgreementList(List<SupSignFund> agreementList, QuerySupSignAgreementListRequest request, QuerySupSignAgreementListResponse response) {
        if (CollectionUtils.isEmpty(agreementList)) {
            return new ArrayList<>();
        }
        List<String> fundCodeList = agreementList.stream().map(SupSignFund::getFundCode).distinct().collect(Collectors.toList());

        Map<String, HighProductDBInfoBean> highProductDbInfoBeanMap = queryHighProductOuterService.getHighProductDBInfoMap(fundCodeList);

        List<HighProductControlBean> productControlList = queryHighProductOuterService.getHighProductControlByFundCodes(fundCodeList);
        Map<String, HighProductControlBean> productControlBeanMap = productControlList.stream().collect(Collectors.toMap(HighProductControlBean::getFundCode, highProductControlBean -> highProductControlBean));
        List<SupSignFund> newFundList = new ArrayList<>();
        String isDataAuth = request.getIsDataAuth();
        String filterHz = request.getFilterHz();
        for (SupSignFund supSignFund : agreementList) {
            String fundCode = supSignFund.getFundCode();
            HighProductDBInfoBean highProductDbInfoBean = highProductDbInfoBeanMap.get(fundCode);
            HighProductControlBean highProductControlBean = productControlBeanMap.get(fundCode);
            supSignFund.setDisCode(highProductControlBean.getDisCode());
            supSignFund.setHkSaleFlag(highProductDbInfoBean.getHkSaleFlag());
            // 如果需要过滤好臻,直接过滤
            if (DisCodeEnum.HZ.getCode().equals(highProductControlBean.getDisCode())) {
                if (StringUtils.isNotBlank(filterHz) && YesOrNoEnum.YES.getCode().equals(filterHz)) {
                    log.info("制定过滤好臻产品,fundCode:{}", fundCode);
                    continue;
                }
            }
            // 1.查询产品分销渠道,没有授权,就需要过滤好臻
            if (DisCodeEnum.HZ.getCode().equals(highProductControlBean.getDisCode())) {
                response.setIsHasHzProduct(YesOrNoEnum.YES.getCode());
                if (StringUtils.isNotBlank(isDataAuth) && YesOrNoEnum.NO.getCode().equals(isDataAuth)) {
                    log.info("没有授权,就需要过滤好臻,fundCode:{}", fundCode);
                    continue;
                }
            }
            // 2.查询是否香港产品,没有授权,就需要过滤香港
            if (YesOrNoEnum.YES.getCode().equals(highProductDbInfoBean.getHkSaleFlag())) {
                response.setIsHasHkProduct(YesOrNoEnum.YES.getCode());
                if (StringUtils.isNotBlank(isDataAuth) && YesOrNoEnum.NO.getCode().equals(isDataAuth)) {
                    log.info("没有授权,就需要过滤香港,fundCode:{}", fundCode);
                    continue;
                }
            }
            newFundList.add(supSignFund);
        }
        return newFundList;
    }

    /**
     * 设置基金信息
     *
     * @param unsignAgreementList
     * @return void
     * @author: huaqiang.liu
     * @date: 2020/12/22 16:20
     * @since JDK 1.8
     */
    private void setFundInfo(List<SupSignFund> unsignAgreementList) {
        if (unsignAgreementList == null || unsignAgreementList.isEmpty()) {
            return;
        }
        // 获取基金列表
        List<String> fundCodeList = new ArrayList<>(unsignAgreementList.size());
        unsignAgreementList.forEach(agree -> fundCodeList.add(agree.getFundCode()));
        // 查询产品信息
        Map<String, HighProductBaseInfoModel> productMap = queryHighProductOuterService.getHighProductBaseInfoMap(fundCodeList);
        // 设置taCode
        unsignAgreementList.forEach(agree -> agree.setTaCode(productMap.get(agree.getFundCode()).getTaCode()));
    }

    /**
     * 查询并设置银行预留手机
     *
     * @param
     * @return void
     * @author: huaqiang.liu
     * @date: 2020/12/10 10:34
     * @since JDK 1.8
     */
    private void setMobile(List<SupSignFund> unsignAgreementList, QuerySupSignAgreementListRequest request) {
        if (unsignAgreementList == null || unsignAgreementList.isEmpty()) {
            return;
        }
        String hboneMobile = null;
        Map<String, QueryCustBankCardResult> bankMap = new HashMap<>(unsignAgreementList.size());
        for (SupSignFund fund : unsignAgreementList) {
            // 查询持仓资金账号
            List<String> cpAcctNos = queryCpAcctNo(request.getTxAcctNo(), fund.getFundCode());
            for (String cpAcctNo : cpAcctNos) {
                QueryCustBankCardResult bankInfo = bankMap.get(cpAcctNo);
                // 查询卡信息
                if (bankInfo == null) {
                    QueryCustBankCardResult result = queryBankInfo(request, cpAcctNo);
                    if (StringUtils.isNotBlank(result.getMobileBank())) {
                        bankInfo = result;
                        bankMap.put(cpAcctNo, bankInfo);
                    }
                }
                // 设置手机、卡信息
                if (bankInfo != null) {
                    fund.setBankCode(bankInfo.getBankCode());
                    fund.setCustBankId(bankInfo.getCpAcctNo());
                    fund.setBankAcct(bankInfo.getBankAcct());
                    fund.setMobile(bankInfo.getMobileBank());
                    fund.setMobileMask(bankInfo.getMobileBankMask());
                    break;
                }
            }
            // 无预留手机，取一账通手机
            if (StringUtils.isBlank(fund.getMobile())) {
                if (hboneMobile == null) {
                    // 查询一账通信息
                    // 查询明文预留手机
                    QueryCustMobileContext mobileCtx = new QueryCustMobileContext();
                    mobileCtx.setTxAcctNo(request.getTxAcctNo());
                    mobileCtx.setDisCode(request.getDisCode());
                    QueryCustMobileResult mobileResult = queryCustMobileOutService.queryCustMobile(mobileCtx);
                    hboneMobile = mobileResult.getMobile();
                }
                // 设置手机
                fund.setMobile(hboneMobile);
                if (StringUtils.isNotBlank(hboneMobile)) {
                    // todo 后期有需求，掩码取值需要改下,换接口查询
                    fund.setMobileMask(maskPhoneNumber(hboneMobile));
                }

            }
        }
    }

    private String maskPhoneNumber(String phoneNumber) {
        // 检查号码长度是否大于4
        if (phoneNumber.length() <= 4) {
            return phoneNumber;
        }
        return phoneNumber.substring(0, phoneNumber.length() - 4) + "****";
    }

    /**
     * 查询银行及预留手机
     *
     * @param request
     * @return com.howbuy.tms.common.outerservice.acccenter.querycustbankcard.QueryCustBankCardResult
     * @author: huaqiang.liu
     * @date: 2020/12/10 13:47
     * @since JDK 1.8
     */
    private QueryCustBankCardResult queryBankInfo(QuerySupSignAgreementListRequest request, String cpAcctNo) {
        // 查询明文卡信息
        QueryCustBankCardContext ctx = new QueryCustBankCardContext();
        ctx.setTxAcctNo(request.getTxAcctNo());
        ctx.setCpAcctNo(cpAcctNo);
        ctx.setDisCode(request.getDisCode());
        ctx.setOutletCode(request.getOutletCode());
        QueryCustBankCardResult bankInfo = queryCustBankCardOuterService.queryCudtBankCardWithBankAcctPlaintext(ctx);
        // 查询明文预留手机
        QueryCustMobileContext mobileCtx = new QueryCustMobileContext();
        mobileCtx.setTxAcctNo(request.getTxAcctNo());
        mobileCtx.setDisCode(request.getDisCode());
        QueryCustMobileResult mobileResult = queryCustMobileOutService.queryCustMobile(mobileCtx);
        if (mobileResult.getBankMobileMap() != null) {
            bankInfo.setMobileBank(mobileResult.getBankMobileMap().get(cpAcctNo));
        }

        return bankInfo;
    }

    /**
     * 查询持仓资金账号
     *
     * @param txAcctNo
     * @param fundCode
     * @return java.lang.String
     * @author: huaqiang.liu
     * @date: 2020/12/10 11:14
     * @since JDK 1.8
     */
    private List<String> queryCpAcctNo(String txAcctNo, String fundCode) {
        HashSet<String> cpAcctNos = new HashSet<>();
        // 查询持仓
        List<CustBooksPo> custBooksPos = custBooksRepository.selectStockBooksByTxAcctNoAndFundCode(txAcctNo, fundCode);
        if (custBooksPos != null && !custBooksPos.isEmpty()) {
            custBooksPos.forEach(po -> cpAcctNos.add(po.getCpAcctNo()));
        }
        // 查询在途
        List<String> list = dealOrderRepository.selectBuyOnwayCpAcctNo(txAcctNo, fundCode);
        if (!list.isEmpty()) {
            list.forEach(s -> cpAcctNos.add(s));
        }
        return new ArrayList<>(cpAcctNos);
    }

    /**
     * 查询已签协议
     *
     * @param txAcctNo
     * @return java.util.Set<java.lang.String>
     * @author: huaqiang.liu
     * @date: 2020/12/10 10:11
     * @since JDK 1.8
     */
    private Set<String> querySignedSupSignAgreementSet(String txAcctNo) {
        List<String> signedList = custEsignatureAgreementRepository.querySignedSupSignAgreementList(txAcctNo);
        return new HashSet<>(signedList);
    }

    /**
     * 筛选出待签协议
     *
     * @param allAgreementList
     * @param signedSet
     * @return java.util.List<com.howbuy.tms.high.orders.facade.search.querysupsignagreementlist.QuerySupSignAgreementListResponse.SupSignFund>
     * @author: huaqiang.liu
     * @date: 2020/12/10 10:11
     * @since JDK 1.8
     */
    private List<SupSignFund> getUnsignAgreementList(List<CustSupSignAgreementBean> allAgreementList, Set<String> signedSet) {
        // 结果列表
        List<SupSignFund> rsList = new ArrayList<>();
        // 存放已选出的产品
        Map<String, SupSignFund> map = new HashMap<>();
        for (CustSupSignAgreementBean bean : allAgreementList) {
            // 跳过已签署的协议
            if (signedSet.contains(bean.getAgreementCode())) {
                continue;
            }

            SupSignFund agreement = map.get(bean.getFundCode());
            // 为新的产品创建产品对象，并放入List和map
            if (agreement == null) {
                agreement = new SupSignFund();
                rsList.add(agreement);
                map.put(bean.getFundCode(), agreement);

                agreement.setFundCode(bean.getFundCode());
                agreement.setFundName(bean.getFundName());
            }
            // 在产品的协议列表中添加协议
            SupSignAgreement info = new SupSignAgreement();
            BeanUtils.copyProperties(bean, info);
            agreement.getAgreementList().add(info);
        }
        return rsList;
    }
}