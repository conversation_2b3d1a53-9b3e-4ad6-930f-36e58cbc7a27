/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.tms.high.orders.service.business.factory.fundBuyStatus;

import com.howbuy.tms.high.orders.service.facade.search.querybuyfundstatus.bean.FundBuyStatusDto;
import com.howbuy.tms.high.orders.service.facade.search.querybuyfundstatus.bean.QueryFundBuyStatusParam;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * @description: 通用基金是否可购买校验
 * <AUTHOR>
 * @date 2025/9/10 15:10
 * @since JDK 1.8
 */
@Service
public class CommonBuyFundStatusChannelResourceService extends AbstractFundBuyStatusChannelSourceService{

    @Override
    public boolean getChannelSource(String disCode,String channelSource) {
        return StringUtils.isBlank(channelSource);
    }

    @Override
    public FundBuyStatusDto getFundBuyStatus(QueryFundBuyStatusParam queryFundBuyStatusParam) {
        return getFundBuyStatusLogicService(queryFundBuyStatusParam);
    }

}
