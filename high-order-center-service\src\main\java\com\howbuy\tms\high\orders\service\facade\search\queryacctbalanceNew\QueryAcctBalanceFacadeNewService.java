/**
 * Copyright (c) 2017, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.orders.service.facade.search.queryacctbalanceNew;

import com.alibaba.fastjson.JSON;
import com.howbuy.dtms.order.client.domain.BalanceBeanVO;
import com.howbuy.dtms.order.client.domain.UnconfirmeProductVO;
import com.howbuy.dtms.order.client.domain.response.cash.QueryCashBalanceByFundTxAcctResponse;
import com.howbuy.dtms.order.client.domain.response.hkbalance.dubbo.QueryBalanceResponse;
import com.howbuy.dtms.order.client.facade.query.balance.QueryBalanceFacade;
import com.howbuy.dtms.order.client.facade.query.cashbalance.QueryCashBalanceByFundTxAcctFacade;
import com.howbuy.interlayer.product.model.HighProductDBInfoModel;
import com.howbuy.interlayer.product.service.HighProductService;
import com.howbuy.tms.common.enums.busi.YesOrNoEnum;
import com.howbuy.tms.common.enums.database.HighNavDivTypeEnum;
import com.howbuy.tms.high.orders.facade.search.queryacctbalance.QueryAcctBalanceRequest;
import com.howbuy.tms.high.orders.facade.search.queryacctbalance.QueryAcctBalanceResponse;
import com.howbuy.tms.high.orders.facade.search.queryacctbalance.UnconfirmeProduct;
import com.howbuy.tms.high.orders.facade.search.queryacctbalanceNew.QueryAcctBalanceNewFacade;
import com.howbuy.tms.high.orders.facade.search.queryacctbalancewithouthk.QueryAcctBalanceWithoutHkFacade;
import com.howbuy.tms.high.orders.service.business.queryliquidation.QueryLiquidationService;
import com.howbuy.tms.high.orders.service.common.utils.TradeUtils;
import com.howbuy.tms.high.orders.service.task.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 查询用户持仓,海外的转包海外接口,其他的查询之前的接口
 */
@DubboService
@Service("queryAcctBalanceNewFacade")
@Slf4j
public class QueryAcctBalanceFacadeNewService implements QueryAcctBalanceNewFacade {
    @Autowired
    private HighProductService highProductService;
    @Autowired
    private QueryAcctBalanceWithoutHkFacade queryAcctBalanceWithoutHkFacade;
    @Autowired
    private QueryBalanceFacade queryBalanceFacade;
    @Autowired
    private QueryCashBalanceByFundTxAcctFacade queryCashBalanceByFundTxAcctFacade;
    @Autowired
    private QueryLiquidationService queryLiquidationService;
    @Autowired
    private HowBuyRunTaskUil howBuyRunTaskUil;

    @Override
    public QueryAcctBalanceResponse execute(QueryAcctBalanceRequest request) {
        log.info("QueryAcctBalanceNewFacade-查询新持仓接口,request={}", JSON.toJSONString(request));
        // 是否只需要查询香港产品
        Boolean hkProduct = null;
        if (StringUtils.isNotBlank(request.getProductCode())) {
            hkProduct = isHkProduct(request.getProductCode());
        }
        if (StringUtils.isNotBlank(request.getHkSaleFlag()) && YesOrNoEnum.YES.getCode().equals(request.getHkSaleFlag())) {
            hkProduct = true;
        } else if (StringUtils.isNotBlank(request.getHkSaleFlag()) && YesOrNoEnum.NO.getCode().equals(request.getHkSaleFlag())) {
            hkProduct = false;
        }
        // 1.查询非海外的持仓
        List<HowbuyBaseTask> taskList = new ArrayList<>(3);
        QueryBalanceContext queryBalanceContext = new QueryBalanceContext();
        taskList.add(new QueryUnHkBalanceTask(queryBalanceContext, request, hkProduct, queryAcctBalanceWithoutHkFacade));
        // 2.查询海外持仓
        taskList.add(new QueryHkBalanceTask(request, hkProduct, queryBalanceContext, queryBalanceFacade));
        // 3.查询现金余额
        taskList.add(new QueryCashBalanceTask(request, queryBalanceContext, hkProduct, queryCashBalanceByFundTxAcctFacade));
        howBuyRunTaskUil.runTask(taskList);

        // 4.数据汇总
        return buildTotalBalanceInfo(queryBalanceContext, request);
    }

    /***
     * 设置汇总数据
     * @param queryBalanceContext 持仓信息
     */
    private QueryAcctBalanceResponse buildTotalBalanceInfo(QueryBalanceContext queryBalanceContext, QueryAcctBalanceRequest request) {
        QueryAcctBalanceResponse totalBalance = new QueryAcctBalanceResponse();
        QueryBalanceResponse hkBalance = queryBalanceContext.getHkBalance();
        QueryAcctBalanceResponse unHkBalance = queryBalanceContext.getUnHkBalance();
        QueryCashBalanceByFundTxAcctResponse cashBalance = queryBalanceContext.getCashBalance();
        totalBalance.setReturnCode(unHkBalance.getReturnCode());
        totalBalance.setDescription(unHkBalance.getDescription());
        // 0.是否有海外产品
        if (CollectionUtils.isNotEmpty(hkBalance.getBalanceList())) {
            totalBalance.setHasHKProduct(YesOrNoEnum.YES.getCode());
        } else {
            totalBalance.setHasHKProduct(YesOrNoEnum.NO.getCode());
        }
        // 是否过滤香港持仓
        if (StringUtils.isNotBlank(request.getNotFilterHkFund()) && YesOrNoEnum.NO.getCode().equals(request.getNotFilterHkFund())) {
            hkBalance = new QueryBalanceResponse();
            cashBalance = new QueryCashBalanceByFundTxAcctResponse();
        }

        // 1.总市值
        BigDecimal unHkTotalMarketValue = unHkBalance.getTotalMarketValue() == null ? BigDecimal.ZERO : unHkBalance.getTotalMarketValue();
        BigDecimal hkTotalMarketValue = hkBalance.getDisPlayCurrencyTotalMarketValue() == null ? BigDecimal.ZERO : hkBalance.getDisPlayCurrencyTotalMarketValue();
        BigDecimal totalExchangeCashBalance = cashBalance.getTotalExchangeCashBalance() == null ? BigDecimal.ZERO : cashBalance.getTotalExchangeCashBalance();
        totalBalance.setTotalMarketValue(unHkTotalMarketValue.add(hkTotalMarketValue).add(totalExchangeCashBalance));
        // 香港展示总市值
        totalBalance.setDisPlayHkTotalMarketValue(hkTotalMarketValue);
        // 2.总未确认金额
        BigDecimal unHkTotalUnconfirmedAmt = unHkBalance.getTotalUnconfirmedAmt() == null ? BigDecimal.ZERO : unHkBalance.getTotalUnconfirmedAmt();
        BigDecimal hkTotalUnconfirmedAmt = hkBalance.getTotalUnconfirmedAmt() == null ? BigDecimal.ZERO : hkBalance.getTotalUnconfirmedAmt();
        totalBalance.setTotalUnconfirmedAmt(unHkTotalUnconfirmedAmt.add(hkTotalUnconfirmedAmt));
        // 3.总未确认数量
        int unHkTotalUnconfirmedNum = unHkBalance.getTotalUnconfirmedNum() == null ? 0 : unHkBalance.getTotalUnconfirmedNum();
        int hkTotalUnconfirmedNum = hkBalance.getTotalUnconfirmedNum() == null ? 0 : hkBalance.getTotalUnconfirmedNum();
        totalBalance.setTotalUnconfirmedNum(unHkTotalUnconfirmedNum + hkTotalUnconfirmedNum);
        // 4.赎回未确认数量
        int unHkRedeemUnconfirmedNum = unHkBalance.getRedeemUnconfirmedNum() == null ? 0 : unHkBalance.getRedeemUnconfirmedNum();
        int hkRedeemUnconfirmedNum = hkBalance.getRedeemUnconfirmedNum() == null ? 0 : hkBalance.getRedeemUnconfirmedNum();
        totalBalance.setRedeemUnconfirmedNum(unHkRedeemUnconfirmedNum + hkRedeemUnconfirmedNum);
        // 5.当前总收益
        BigDecimal unHkTotalCurrentAsset = unHkBalance.getTotalCurrentAsset() == null ? BigDecimal.ZERO : unHkBalance.getTotalCurrentAsset();
        BigDecimal hkTotalCurrentAsset = hkBalance.getTotalCurrentAsset() == null ? BigDecimal.ZERO : hkBalance.getTotalCurrentAsset();
        totalBalance.setTotalCurrentAsset(unHkTotalCurrentAsset.add(hkTotalCurrentAsset));
        // 6.总收益计算状态: 0-计算中;1-计算成功
        String unHkTotalIncomCalStat = unHkBalance.getTotalIncomCalStat() == null ? YesOrNoEnum.YES.getCode() : unHkBalance.getTotalIncomCalStat();
        // 海外没有该字段,都是计算成功
        totalBalance.setTotalIncomCalStat(YesOrNoEnum.NO.getCode().equals(unHkTotalIncomCalStat) ? YesOrNoEnum.NO.getCode() : YesOrNoEnum.YES.getCode());
        // 7.总回款
        BigDecimal unHkTotalCashCollection = unHkBalance.getTotalCashCollection() == null ? BigDecimal.ZERO : unHkBalance.getTotalCashCollection();
        BigDecimal hkTotalCashCollection = hkBalance.getTotalCashCollection() == null ? BigDecimal.ZERO : hkBalance.getTotalCashCollection();
        totalBalance.setTotalCashCollection(unHkTotalCashCollection.add(hkTotalCashCollection));
        // 8.现金余额
        totalBalance.setTotalExchangeCashBalance(cashBalance.getTotalExchangeCashBalance());
        totalBalance.setTotalExchangeFrozenAmt(cashBalance.getTotalExchangeFrozenAmt());
        totalBalance.setTotalExchangeAvailableBalance(cashBalance.getTotalExchangeAvailableBalance());
        // 9.是否有好臻产品
        totalBalance.setHasHZProduct(unHkBalance.getHasHZProduct());
        // 10.总未确认产品列表
        setUnConfirmProduct(unHkBalance, hkBalance, totalBalance);
        // 11.持仓信息汇总
        List<QueryAcctBalanceResponse.BalanceBean> totalBalanceList = getTotalBalanceBeanList(unHkBalance, hkBalance);
        totalBalance.setBalanceList(totalBalanceList);
        // 12.持有净值型产品持仓,1:是,0:不是
        totalBalance.setHasBalanceNavProduct(getHasBalanceNavProduct(totalBalanceList));
        // 13.是否曾经有持仓,1:是,0:否
        totalBalance.setHasBalancePrevious(getHasBalancePrevious(totalBalanceList, request));
        // 14.是否分红中,1:是,0:否
        totalBalance.setIsDiving(getIsDiving(totalBalanceList));
        return totalBalance;
    }

    /**
     * 是否分红中,1:是,0:否
     */
    private String getIsDiving(List<QueryAcctBalanceResponse.BalanceBean> totalBalanceList) {
        if (CollectionUtils.isEmpty(totalBalanceList)) {
            log.info("没有持仓,不是分红中");
            return YesOrNoEnum.NO.getCode();
        }
        for (QueryAcctBalanceResponse.BalanceBean balanceBean : totalBalanceList) {
            if (StringUtils.isNotBlank(balanceBean.getNavDivFlag()) && !HighNavDivTypeEnum.NO_NEED_DIV.getCode().equals(balanceBean.getNavDivFlag())) {
                log.info("getIsDiving-分红状态非无需分红提醒,就认为是分红中,fundCode={}", balanceBean.getProductCode());
                return YesOrNoEnum.YES.getCode();
            }
        }
        log.info("没有无需分红提醒,认为不是分红中");
        return YesOrNoEnum.NO.getCode();
    }

    /**
     * 是否有过持仓
     *
     * @param totalBalanceList 持仓列表
     * @return 1:有,0:无
     */
    private String getHasBalancePrevious(List<QueryAcctBalanceResponse.BalanceBean> totalBalanceList, QueryAcctBalanceRequest request) {
        // 1.查询是否有持仓
        if (CollectionUtils.isNotEmpty(totalBalanceList)) {
            List<QueryAcctBalanceResponse.BalanceBean> confirmBalanceList = totalBalanceList.stream().filter(QueryAcctBalanceResponse.BalanceBean::isBalance).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(confirmBalanceList)) {
                log.info("getHasBalancePrevious-当前有确认持仓");
                return YesOrNoEnum.YES.getCode();
            }
        }
        // 2.查询清仓产品列表
        List<String> liquidationFundCodeList = queryLiquidationService.queryAllChannelLiquidationFundCodeList(request.getHbOneNo(), request.getTxAcctNo(), request.getDisCodeList(), request.getHkSaleFlag());
        if (CollectionUtils.isNotEmpty(liquidationFundCodeList)) {
            log.info("getHasBalancePrevious-有清仓产品");
            return YesOrNoEnum.YES.getCode();
        }
        return YesOrNoEnum.NO.getCode();
    }

    /**
     * 设置未确认信息
     */
    private void setUnConfirmProduct(QueryAcctBalanceResponse unHkBalance, QueryBalanceResponse hkBalance, QueryAcctBalanceResponse totalBalance) {
        List<UnconfirmeProduct> unConfirmedProducts = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(unHkBalance.getUnconfirmeProducts())) {
            unConfirmedProducts.addAll(unHkBalance.getUnconfirmeProducts());
        }
        if (CollectionUtils.isNotEmpty(hkBalance.getUnconfirmeProducts())) {
            for (UnconfirmeProductVO unConfirmedProduct : hkBalance.getUnconfirmeProducts()) {
                UnconfirmeProduct unconfirmeProduct = getUnconfirmeProduct(unConfirmedProduct);
                unConfirmedProducts.add(unconfirmeProduct);
            }
        }
        totalBalance.setUnconfirmeProducts(unConfirmedProducts);
    }

    private List<QueryAcctBalanceResponse.BalanceBean> getTotalBalanceBeanList(QueryAcctBalanceResponse unHkBalance, QueryBalanceResponse hkBalance) {
        List<QueryAcctBalanceResponse.BalanceBean> totalBalanceList = new ArrayList<>();
        List<QueryAcctBalanceResponse.BalanceBean> unHkBalanceList = unHkBalance.getBalanceList();
        if (CollectionUtils.isNotEmpty(unHkBalanceList)) {
            unHkBalanceList.forEach(balanceBean -> balanceBean.setHkSaleFlag(YesOrNoEnum.NO.getCode()));
            totalBalanceList.addAll(unHkBalanceList);
        }
        List<BalanceBeanVO> hkBalanceList = hkBalance.getBalanceList();
        if (CollectionUtils.isNotEmpty(hkBalanceList)) {
            for (BalanceBeanVO balanceBeanVO : hkBalanceList) {
                QueryAcctBalanceResponse.BalanceBean balanceBean = buildHkBalance(balanceBeanVO);
                totalBalanceList.add(balanceBean);
            }
        }
        return totalBalanceList;
    }

    /**
     * 持有净值型产品持仓,1:是,0:不是,balanceNew查询接口才有值
     */
    private String getHasBalanceNavProduct(List<QueryAcctBalanceResponse.BalanceBean> totalBalanceList) {
        if (CollectionUtils.isEmpty(totalBalanceList)) {
            log.info("getHasBalanceNavProduct-没有持仓,没有净值型产品");
            return YesOrNoEnum.NO.getCode();
        }
        List<QueryAcctBalanceResponse.BalanceBean> navProductList = totalBalanceList.stream().filter(balanceBean -> YesOrNoEnum.YES.getCode().equals(balanceBean.getIsNavProduct())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(navProductList)) {
            log.info("getHasBalanceNavProduct-持有净值型产品");
            return YesOrNoEnum.YES.getCode();
        }
        log.info("getHasBalanceNavProduct-有持仓,但是没有持有净值型产品");
        return YesOrNoEnum.NO.getCode();
    }

    /**
     * 待确认的基金信息
     *
     * @param unConfirmedProduct
     * @return
     */
    private UnconfirmeProduct getUnconfirmeProduct(UnconfirmeProductVO unConfirmedProduct) {
        UnconfirmeProduct unconfirmeProduct = new UnconfirmeProduct();
        unconfirmeProduct.setFundCode(unConfirmedProduct.getFundCode());
        unconfirmeProduct.setProductType(unConfirmedProduct.getProductType());
        unconfirmeProduct.setProductSubType(unConfirmedProduct.getProductSubType());
        unconfirmeProduct.setUnconfirmedAmt(unConfirmedProduct.getUnconfirmedAmt());
        unconfirmeProduct.setHkSaleFlag(unConfirmedProduct.getHkSaleFlag());
        unconfirmeProduct.setDisCode(unConfirmedProduct.getDisCode());
        return unconfirmeProduct;
    }

    /**
     * 构建海外持仓信息
     */
    private QueryAcctBalanceResponse.BalanceBean buildHkBalance(BalanceBeanVO balanceBeanVO) {
        QueryAcctBalanceResponse.BalanceBean balanceBean = new QueryAcctBalanceResponse.BalanceBean();
        // Replace BeanUtils.copyProperties with explicit setters
        balanceBean.setDisCode(balanceBeanVO.getDisCode());
        balanceBean.setDisCodeList(balanceBeanVO.getDisCodeList());
        balanceBean.setProductCode(balanceBeanVO.getProductCode());
        balanceBean.setSubProductCode(balanceBeanVO.getSubProductCode());
        balanceBean.setProductName(balanceBeanVO.getProductName());
        balanceBean.setProductType(balanceBeanVO.getProductType());
        balanceBean.setProductSubType(balanceBeanVO.getProductSubType());
        balanceBean.setBalanceVol(balanceBeanVO.getBalanceVol());
        balanceBean.setUnconfirmedVol(balanceBeanVO.getUnconfirmedVol());
        balanceBean.setUnconfirmedAmt(balanceBeanVO.getUnconfirmedAmt());
        balanceBean.setCurrency(balanceBeanVO.getCurrency());
        balanceBean.setNav(balanceBeanVO.getNav());
        balanceBean.setNavDt(balanceBeanVO.getNavDt());
        balanceBean.setNavDivFlag(balanceBeanVO.getNavDivFlag());
        balanceBean.setMarketValue(balanceBeanVO.getMarketValue());
        balanceBean.setCurrencyMarketValue(balanceBeanVO.getCurrencyMarketValue());
        balanceBean.setScaleType(balanceBeanVO.getScaleType());
        balanceBean.setHkSaleFlag(balanceBeanVO.getHkSaleFlag());
        balanceBean.setStageEstablishFlag(balanceBeanVO.getStageEstablishFlag());
        balanceBean.setFractionateCallFlag(balanceBeanVO.getFractionateCallFlag());
        balanceBean.setFundCXQXStr(balanceBeanVO.getFundCXQXStr());
        balanceBean.setNetBuyAmount(balanceBeanVO.getNetBuyAmount());
        balanceBean.setCurrencyNetBuyAmount(balanceBeanVO.getCurrencyNetBuyAmount());
        balanceBean.setIncomeDt(balanceBeanVO.getIncomeDt());
        balanceBean.setIncomeCalStat(balanceBeanVO.getIncomeCalStat());
        balanceBean.setCurrentAsset(balanceBeanVO.getCurrentAsset());
        balanceBean.setIsNavProduct(TradeUtils.isNavTypeFund(balanceBeanVO.getStandardFixedIncomeFlag(), balanceBeanVO.getProductSubType(), balanceBeanVO.getProductCode()) ? YesOrNoEnum.YES.getCode() : YesOrNoEnum.NO.getCode());
        balanceBean.setCurrentAssetCurrency(balanceBeanVO.getCurrentAssetCurrency());
        balanceBean.setAccumIncome(balanceBeanVO.getAccumIncome());
        balanceBean.setAccumIncomeRmb(balanceBeanVO.getAccumIncomeRmb());
        balanceBean.setAccumRealizedIncome(balanceBeanVO.getAccumRealizedIncome());
        balanceBean.setAccumRealizedIncomeRmb(balanceBeanVO.getAccumRealizedIncomeRmb());
        balanceBean.setRePurchaseFlag(balanceBeanVO.getRePurchaseFlag());
        balanceBean.setBenchmark(balanceBeanVO.getBenchmark());
        balanceBean.setBenchmarkType(balanceBeanVO.getBenchmarkType());
        balanceBean.setValueDate(balanceBeanVO.getValueDate());
        balanceBean.setDueDate(balanceBeanVO.getDueDate());
        balanceBean.setStandardFixedIncomeFlag(balanceBeanVO.getStandardFixedIncomeFlag());
        balanceBean.setInvestmentHorizon(balanceBeanVO.getInvestmentHorizon());
        balanceBean.setCooperation(balanceBeanVO.getCooperation());
        balanceBean.setCrisisFlag(balanceBeanVO.getCrisisFlag());
        balanceBean.setYieldIncome(balanceBeanVO.getYieldIncome());
        balanceBean.setYieldIncomeDt(balanceBeanVO.getYieldIncomeDt());
        balanceBean.setCopiesIncome(balanceBeanVO.getCopiesIncome());
        balanceBean.setHwSaleFlag(balanceBeanVO.getHwSaleFlag());
        balanceBean.setRegDt(balanceBeanVO.getRegDt());
        balanceBean.setOneStepType(balanceBeanVO.getOneStepType());
        balanceBean.setTwoStepType(balanceBeanVO.getTwoStepType());
        balanceBean.setSecondStepType(balanceBeanVO.getSecondStepType());
        balanceBean.setProductSaleType(balanceBeanVO.getProductSaleType());
        balanceBean.setNaProductFeeType(balanceBeanVO.getNaProductFeeType());
        balanceBean.setReceivManageFee(balanceBeanVO.getReceivManageFee());
        balanceBean.setReceivPreformFee(balanceBeanVO.getReceivPreformFee());
        balanceBean.setCurrencyMarketValueExFee(balanceBeanVO.getCurrencyMarketValueExFee());
        balanceBean.setMarketValueExFee(balanceBeanVO.getMarketValueExFee());
        balanceBean.setBalanceIncomeNew(balanceBeanVO.getBalanceIncomeNew());
        balanceBean.setBalanceIncomeNewRmb(balanceBeanVO.getBalanceIncomeNewRmb());
        balanceBean.setBalanceFactor(balanceBeanVO.getBalanceFactor());
        balanceBean.setConvertFinish(balanceBeanVO.getConvertFinish());
        balanceBean.setBalanceFactorDate(balanceBeanVO.getBalanceFactorDate());
        balanceBean.setYieldRate(balanceBeanVO.getYieldRate());
        balanceBean.setAccumIncomeNew(balanceBeanVO.getAccumIncomeNew());
        balanceBean.setAccumIncomeNewRmb(balanceBeanVO.getAccumIncomeNewRmb());
        balanceBean.setBalanceCost(balanceBeanVO.getBalanceCost());
        balanceBean.setBalanceCostCurrency(balanceBeanVO.getBalanceCostCurrency());
        balanceBean.setDailyAsset(balanceBeanVO.getDailyAsset());
        balanceBean.setDailyAssetCurrency(balanceBeanVO.getDailyAssetCurrency());
        balanceBean.setCashCollection(balanceBeanVO.getCashCollection());
        balanceBean.setCurrencyCashCollection(balanceBeanVO.getCurrencyCashCollection());
        balanceBean.setAccumYieldRate(balanceBeanVO.getAccumYieldRate());
        balanceBean.setAccumCost(balanceBeanVO.getAccumCost());
        balanceBean.setAccumCostRmb(balanceBeanVO.getAccumCostRmb());
        balanceBean.setBalanceFloatIncome(balanceBeanVO.getBalanceFloatIncome());
        balanceBean.setBalanceFloatIncomeRmb(balanceBeanVO.getBalanceFloatIncomeRmb());
        balanceBean.setBalanceFloatIncomeRate(balanceBeanVO.getBalanceFloatIncomeRate());
        balanceBean.setDayAssetRate(balanceBeanVO.getDayAssetRate());
        balanceBean.setDayIncomeGrowthRate(balanceBeanVO.getDayIncomeGrowthRate());
        balanceBean.setAccumCostNew(balanceBeanVO.getAccumCostNew());
        balanceBean.setAccumCostRmbNew(balanceBeanVO.getAccumCostRmbNew());
        balanceBean.setBalanceAmt(balanceBeanVO.getBalanceAmt());
        balanceBean.setBalanceAmtRmb(balanceBeanVO.getBalanceAmtRmb());
        balanceBean.setAccumCollection(balanceBeanVO.getAccumCollection());
        balanceBean.setAccumCollectionRmb(balanceBeanVO.getAccumCollectionRmb());
        balanceBean.setBalanceAmtExFee(balanceBeanVO.getBalanceAmtExFee());
        balanceBean.setBalanceAmtExFeeRmb(balanceBeanVO.getBalanceAmtExFeeRmb());
        balanceBean.setSxz(balanceBeanVO.getSxz());
        balanceBean.setCurrentIncome(balanceBeanVO.getCurrentIncome());
        balanceBean.setCurrentIncomeRmb(balanceBeanVO.getCurrentIncomeRmb());
        balanceBean.setCurrentAccumIncome(balanceBeanVO.getCurrentAccumIncome());
        balanceBean.setCurrentAccumIncomeRmb(balanceBeanVO.getCurrentAccumIncomeRmb());
        balanceBean.setStageFlag(balanceBeanVO.getStageFlag());
        balanceBean.setEstablishDt(balanceBeanVO.getEstablishDt());
        balanceBean.setAssetUpdateDate(balanceBeanVO.getAssetUpdateDate());
        balanceBean.setUnitBalanceCostExFee(balanceBeanVO.getUnitBalanceCostExFee());
        balanceBean.setUnitBalanceCostExFeeRmb(balanceBeanVO.getUnitBalanceCostExFeeRmb());
        balanceBean.setOwnershipTransferIdentity(balanceBeanVO.getOwnershipTransferIdentity());
        balanceBean.setSfhwcxg(balanceBeanVO.getSfhwcxg());
        balanceBean.setCpqxsm(balanceBeanVO.getCpqxsm());
        balanceBean.setNavDisclosureType(balanceBeanVO.getNavDisclosureType());
        balanceBean.setAbnormalFlag(YesOrNoEnum.NO.getCode());
        balanceBean.setMarketValueCtl(balanceBeanVO.getMarketValueCtl());
        balanceBean.setCurrencyMarketValueCtl(balanceBeanVO.getCurrencyMarketValueCtl());
        balanceBean.setCurrencyMarketValueExFeeCtl(balanceBeanVO.getCurrencyMarketValueExFeeCtl());
        balanceBean.setMarketValueExFeeCtl(balanceBeanVO.getMarketValueExFeeCtl());
        balanceBean.setCurrentAssetCtl(balanceBeanVO.getCurrentAssetCtl());
        balanceBean.setCurrentAssetCurrencyCtl(balanceBeanVO.getCurrentAssetCurrencyCtl());
        balanceBean.setDailyAssetCtl(balanceBeanVO.getDailyAssetCtl());
        balanceBean.setDailyAssetCurrencyCtl(balanceBeanVO.getDailyAssetCurrencyCtl());
        balanceBean.setAccumIncomeCtl(balanceBeanVO.getAccumIncomeCtl());
        balanceBean.setAccumIncomeRmbCtl(balanceBeanVO.getAccumIncomeRmbCtl());
        balanceBean.setAccumRealizedIncomeCtl(balanceBeanVO.getAccumRealizedIncomeCtl());
        balanceBean.setAccumRealizedIncomeRmbCtl(balanceBeanVO.getAccumRealizedIncomeRmbCtl());
        balanceBean.setBalanceIncomeNewCtl(balanceBeanVO.getBalanceIncomeNewCtl());
        balanceBean.setBalanceIncomeNewRmbCtl(balanceBeanVO.getBalanceIncomeNewRmbCtl());
        balanceBean.setAccumIncomeNewCtl(balanceBeanVO.getAccumIncomeNewCtl());
        balanceBean.setAccumIncomeNewRmbCtl(balanceBeanVO.getAccumIncomeNewRmbCtl());
        balanceBean.setYieldRateCtl(balanceBeanVO.getYieldRateCtl());
        balanceBean.setBalanceVolCtl(balanceBeanVO.getBalanceVolCtl());
        balanceBean.setUnconfirmedVolCtl(balanceBeanVO.getUnconfirmedVolCtl());
        balanceBean.setNavCtl(balanceBeanVO.getNavCtl());
        balanceBean.setQianXiFlag(balanceBeanVO.getQianXiFlag());
        balanceBean.setUnPaidInAmt(balanceBeanVO.getUnPaidInAmt());
        balanceBean.setCurrencyUnPaidInAmt(balanceBeanVO.getCurrencyUnPaidInAmt());
        balanceBean.setPaidInAmt(balanceBeanVO.getSubTotalAmt());
        balanceBean.setPaidTotalAmt(balanceBeanVO.getPaidTotalAmt());
        balanceBean.setAccumBackRatio(balanceBeanVO.getAccumBackRatio());
        balanceBean.setPaidSubTotalRatio(balanceBeanVO.getPaidSubTotalRatio());
        balanceBean.setAbnormalFlag(YesOrNoEnum.NO.getCode());
        balanceBean.setAssetStrategyFirstType(balanceBeanVO.getAssetStrategyFirstType());
        return balanceBean;
    }

    /**
     * 判断产品是否是香港产品
     *
     * @param fundCode 产品编码
     * @return 是否香港产品, true-是香港产品, false-不是香港产品
     */
    public boolean isHkProduct(String fundCode) {
        List<HighProductDBInfoModel> highProductInfoList = highProductService.getHighProductDBInfo(Collections.singletonList(fundCode));
        if (CollectionUtils.isEmpty(highProductInfoList)) {
            log.info("isHkProduct-根据产品编码查询产品信息为空,就认为不是香港产品,fundCode={}", fundCode);
            return false;
        }
        String hkSaleFlag = highProductInfoList.get(0).getHkSaleFlag();
        if (StringUtils.isBlank(hkSaleFlag)) {
            log.info("isHkProduct-根据产品编码查询产品信息hkSaleFlag为空,就认为不是香港产品,fundCode={}", fundCode);
            return false;
        }
        return YesOrNoEnum.YES.getCode().equals(hkSaleFlag);
    }


}
