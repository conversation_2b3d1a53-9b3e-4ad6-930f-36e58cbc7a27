/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.orders.facade.search.querymemberinfo;

import com.howbuy.tms.common.client.BaseFacade;

/**
 * @description: 会员信息查询接口
 * <AUTHOR>
 * @date 2025/9/9 
 * @since JDK 1.8
 */
/**
 * @api {DUBBO} com.howbuy.tms.high.orders.facade.search.querymemberinfo.QueryMemberInfoFacade.execute(request)
 * @apiName 查询会员信息接口
 * @apiParam (request) {com.howbuy.tms.high.orders.facade.search.querymemberinfo.QueryMemberInfoRequest} request
 * @apiParam (request) {String} request.hbOneNo 一账通号
 * @apiParam (request) {String} request.txAcctNo 交易账号
 * @apiParam (request) {String} request.disCode 分销机构代码
 * @apiParam (request) {String} request.outletCode 网点代码
 * @apiParam (request) {String} request.appDt 申请日期
 * @apiParam (request) {String} request.appTm 申请时间
 * @apiParam (request) {Number} request.pageNo 页码
 * @apiParam (request) {Number} request.pageSize 每页记录数
 * @apiParam (request) {String} request.operIp 交易IP
 * @apiParam (request) {String} request.txCode 交易码
 * @apiParam (request) {String} request.txChannel 交易渠道 1-柜台,2-网站,3-电话,4-Wap,5-App,6-机构
 * @apiParam (request) {String} request.dataTrack 数据跟踪
 * @apiParam (request) {String} request.subOutletCode 子网点代码
 * @apiParamExample 请求参数示例
 * {
 *   "hbOneNo": "1234567890"
 * }
 * @apiSuccess (响应结果) {String} returnCode 返回码
 * @apiSuccess (响应结果) {String} description 描述
 * @apiSuccess (响应结果) {Object} memberInfo 会员信息
 * @apiSuccess (响应结果) {String} memberInfo.memberLevel 会员等级 23301-臻享会员 23302-私享会员 23303-尊享会员
 * @apiSuccess (响应结果) {String} memberInfo.backgroundColor 会员背景色 1-红色 2-黑色
 * @apiSuccessExample 响应结果示例
 * {
 *   "returnCode": "000000",
 *   "description": "成功",
 *   "memberInfo": {
 *     "memberLevel": "23301",
 *     "backgroundColor": "1"
 *   }
 * }
 */
public interface QueryMemberInfoFacade extends BaseFacade<QueryMemberInfoRequest, QueryMemberInfoResponse> {

}
