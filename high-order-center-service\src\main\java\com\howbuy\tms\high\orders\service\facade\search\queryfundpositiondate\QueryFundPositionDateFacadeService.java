/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.orders.service.facade.search.queryfundpositiondate;

import com.howbuy.tms.high.orders.facade.search.queryfundpositiondate.QueryFundPositionDateFacade;
import com.howbuy.tms.high.orders.facade.search.queryfundpositiondate.QueryFundPositionDateRequest;
import com.howbuy.tms.high.orders.facade.search.queryfundpositiondate.QueryFundPositionDateResponse;
import com.howbuy.tms.high.orders.service.service.queryfundpositiondate.QueryFundPositionDateService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @description: 基金产品持仓/清仓日期查询服务实现
 * <AUTHOR>
 * @date 2025/9/4 22:15
 * @since JDK 1.8
 */
@DubboService
@Service("queryFundLiquidationDateFacade")
@Slf4j
public class QueryFundPositionDateFacadeService implements QueryFundPositionDateFacade {

    @Resource
    private QueryFundPositionDateService queryFundPositionDateService;

    @Override
    public QueryFundPositionDateResponse execute(QueryFundPositionDateRequest request) {
        log.info("查询基金产品持仓/清仓日期开始, request: {}", request);

        return queryFundPositionDateService.queryFundPositionDate(request);
    }
}
