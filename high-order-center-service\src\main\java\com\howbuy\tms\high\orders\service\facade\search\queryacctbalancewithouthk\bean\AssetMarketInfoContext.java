package com.howbuy.tms.high.orders.service.facade.search.queryacctbalancewithouthk.bean;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductDBInfoBean;

import java.util.List;
import java.util.Map;

/**
     * 资产市值信息上下文
     */
    public class AssetMarketInfoContext {
        private final Map<String, HighProductDBInfoBean> highProductDbInfoBeanMap;
        private final List<String> splitFundList;
        private final List<String> qxProductIdList;

        public AssetMarketInfoContext(Map<String, HighProductDBInfoBean> highProductDbInfoBeanMap,
                                      List<String> splitFundList,
                                      List<String> qxProductIdList) {
            this.highProductDbInfoBeanMap = highProductDbInfoBeanMap;
            this.splitFundList = splitFundList;
            this.qxProductIdList = qxProductIdList;
        }

        public Map<String, HighProductDBInfoBean> getHighProductDbInfoBeanMap() {
            return highProductDbInfoBeanMap;
        }

        public List<String> getSplitFundList() {
            return splitFundList;
        }

        public List<String> getQxProductIdList() {
            return qxProductIdList;
        }
    }
