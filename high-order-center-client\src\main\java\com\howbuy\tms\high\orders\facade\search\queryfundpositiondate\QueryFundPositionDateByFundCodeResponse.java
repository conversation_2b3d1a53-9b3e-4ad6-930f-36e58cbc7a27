/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT Co., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.orders.facade.search.queryfundpositiondate;

import com.howbuy.tms.high.orders.facade.common.OrderSearchBaseResponse;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * @description: 根据基金编码查询基金持仓/清仓日期响应结果
 * <AUTHOR>
 * @date 2025/9/11 10:30
 * @since JDK 1.8
 */
@Setter
@Getter
public class QueryFundPositionDateByFundCodeResponse extends OrderSearchBaseResponse implements Serializable {

    private static final long serialVersionUID = 7218173061955053292L;
    /**
     * 基金编码
     */
    private String fundCode;

    /**
     * 首次持仓日期
     * 格式：yyyyMMdd
     */
    private String firstHoldDate;

    /**
     * 清仓日期
     * 格式：yyyyMMdd
     */
    private String liquidationDate;

    /**
     * 首次持有的订单
     */
    private String firstHoldDealNo;

    /**
     * 清仓的订单号
     */
    private String liquidationDealNo;
} 