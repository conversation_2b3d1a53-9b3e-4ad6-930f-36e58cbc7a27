/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.orders.facade.search.querymemberinfo.enums;

/**
 * @description: 页面来源枚举
 * <AUTHOR>
 * @date 2025/9/9
 * @since JDK 1.8
 */
public enum PageSourceEnum {

    /**
     * 我的页面
     */
    MY_PAGE("1", "我的页面"),
    
    /**
     * 私募持仓页面
     */
    PRIVATE_POSITION_PAGE("2", "私募持仓页面"),
    
    /**
     * 好臻专区页面
     */
    HAOZHEN_ZONE_PAGE("3", "好臻专区页面"),
    
    /**
     * 好买香港专区页面
     */
    HOWBUY_HK_ZONE_PAGE("4", "好买香港专区页面"),
    
    /**
     * 收益分析页面
     */
    PROFIT_ANALYSIS_PAGE("5", "收益分析页面");

    private final String code;
    private final String description;

    PageSourceEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据代码获取枚举
     * 
     * @param code 页面来源代码
     * @return 页面来源枚举
     */
    public static PageSourceEnum getByCode(String code) {
        if (code == null) {
            return null;
        }
        for (PageSourceEnum pageSource : PageSourceEnum.values()) {
            if (pageSource.code.equals(code)) {
                return pageSource;
            }
        }
        return null;
    }
}
