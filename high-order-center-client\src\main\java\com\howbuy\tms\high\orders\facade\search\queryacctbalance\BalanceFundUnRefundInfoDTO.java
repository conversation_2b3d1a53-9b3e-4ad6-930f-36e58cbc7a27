package com.howbuy.tms.high.orders.facade.search.queryacctbalance;

import com.howbuy.tms.high.orders.facade.common.BaseDto;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Description:基金维度持仓未资金到账信息
 * @Author: yun.lu
 * Date: 2025/9/3 17:47
 */
@Getter
@Setter
public class BalanceFundUnRefundInfoDTO extends BaseDto {
    /**
     * 基金代码
     */
    private String fundCode;

    /**
     * 总待回款金额
     */
    private BigDecimal totalRefundAmt;

    /**
     * 待资金到账交易
     */
    private List<RefundDealOrderDTO> refundDealOrderList;

}
