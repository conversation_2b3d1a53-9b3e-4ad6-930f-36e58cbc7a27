package com.howbuy.tms.high.orders.facade.search.queryhboneinlicaihideconf;

import com.howbuy.tms.common.enums.busi.DisCodeEnum;
import com.howbuy.tms.high.orders.facade.common.OrderSearchBaseRequest;
import com.howbuy.tms.high.orders.facade.enums.TxCodes;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * @Description:查询一账通是否在理财隐藏列表
 * @Author: yun.lu
 * Date: 2025/9/9 10:00
 */
@Getter
@Setter
public class QueryHboneNoInLiCaiHideConfRequest extends OrderSearchBaseRequest implements Serializable {

    /**
     * @api {dubbo} com.howbuy.tms.high.orders.facade.search.queryhboneinlicaihideconf.QueryHboneNoInLiCaiHideConfFacade.execute()
     * @apiVersion 1.0.0
     * @apiGroup QueryHboneNoInLiCaiHideConfFacade
     * @apiName execute
     * @apiDescription 查询一账通是否在理财隐藏列表
     * @apiParam (请求参数) {String} hbOneNo 一账通号
     * @apiSuccess (返回参数) {String} inHideConf 是否在理财隐藏列表,1:在,0:不在
     */

    public QueryHboneNoInLiCaiHideConfRequest() {
        setTxCode(TxCodes.QUERY_HBONENO_IN_LICAI_HIDE_CONF);
        setDisCode(DisCodeEnum.HM.getCode());
    }

}
