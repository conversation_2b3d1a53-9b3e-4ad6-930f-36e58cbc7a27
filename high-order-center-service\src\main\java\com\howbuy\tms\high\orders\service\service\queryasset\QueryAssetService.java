package com.howbuy.tms.high.orders.service.service.queryasset;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.howbuy.cc.center.asset.income.request.CurrentAssetRequest;
import com.howbuy.tms.common.enums.busi.AssetTypeEnum;
import com.howbuy.tms.common.outerservice.cc.center.querycurrentasset.QueryCurrentAssetOuterService;
import com.howbuy.tms.common.outerservice.cc.center.querycurrentasset.QueryCurrentAssetResult;
import com.howbuy.tms.common.utils.HttpConnectionPoolUtil;
import com.howbuy.tms.high.orders.facade.search.queryasset.*;
import com.howbuy.tms.high.orders.service.service.queryasset.bean.HoldStateDomain;
import com.howbuy.tms.high.orders.service.service.queryasset.bean.QueryHoldStateParam;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Service("queryAssetService")
public class QueryAssetService {
    private static final Logger logger = LogManager.getLogger(QueryAssetService.class);

    @Autowired
    private QueryCurrentAssetOuterService queryCurrentAssetOuterService;

    @Value("${asset.client.host}")
    private String assetClientHost;

    @Value("${asset.user.hold.state.url}")
    private String userHoldStateUrl;

    @Value("${asset.user.his.hold.state.url}")
    private String userHisHoldStateUrl;

    // 新资产中心
    private static String NEW_ASSET_CALL_TYPE = "1";

    /**
     * getCurrentAssetMap:(获取收益map)
     *
     * @param fundCodeList 产品代码
     * @param hbOneNo      一账通号
     * @param disCodeList  分销
     * @return
     */
    public Map<String, HighFundAssetIncomeDomain> getCurrentAssetMap(List<String> fundCodeList, String hbOneNo, List<String> disCodeList) {
        Map<String, HighFundAssetIncomeDomain> currentAssetMap = new HashMap<>();
        if (CollectionUtils.isEmpty(fundCodeList)) {
            return currentAssetMap;
        }

        // 批量查询基金收益
        HighClassifyAssetResponse queryCurrentAssetResult = null;
        try {
            queryCurrentAssetResult = queryCurrentNewAsset(disCodeList, fundCodeList, hbOneNo);
        } catch (Exception e) {
            logger.error("getCurrentAssetMap error :", e);
        }

        if (queryCurrentAssetResult != null && !CollectionUtils.isEmpty(queryCurrentAssetResult.getAssets())) {
            for (HighFundAssetIncomeDomain currentAssetDto : queryCurrentAssetResult.getAssets()) {
                currentAssetMap.put(currentAssetDto.getFundCode(), currentAssetDto);
            }
        }

        return currentAssetMap;
    }

    /**
     * queryCurrentAsset:(查询当前收益)
     *
     * @param disCodeList
     * @param fundCodeList
     * @param hbOneNo
     * @return
     */
    public HighClassifyAssetResponse queryCurrentNewAsset(List<String> disCodeList, List<String> fundCodeList, String hbOneNo) {
        CurrentAssetRequest request = new CurrentAssetRequest();
        request.setDisCodeList(disCodeList);
        request.setFundCodeList(fundCodeList);
        request.setHboneNo(hbOneNo);

        //查询用户持仓资产
        String url = assetClientHost + "/asset/highAsset/classifyAsset";
        logger.info("queryCurrentAsset url is:{}", url);
        String resp = HttpConnectionPoolUtil.post(url, request);
        logger.info("queryCurrentAsset url:{}, request:{}, response:{}", url, JSON.toJSONString(request), JSON.toJSONString(resp));
        if (StringUtils.isBlank(resp)) {
            return null;
        }
        return JSON.parseObject(resp, HighClassifyAssetResponse.class);
    }

    /**
     * queryCurrentAsset:(查询当前收益)
     *
     * @param disCodeList
     * @param fundCodeList
     * @param hbOneNo
     * @return
     */
    public HighClassifyAssetResponse queryCurrentOldAsset(List<String> disCodeList, List<String> fundCodeList, String hbOneNo) {
        HighClassifyAssetResponse response = new HighClassifyAssetResponse();
        List<HighFundAssetIncomeDomain> assets = new ArrayList<>();
        response.setAssets(assets);
        try {
            QueryCurrentAssetResult queryCurrentAssetResult = queryCurrentAssetOuterService.queryCurrentAsset(disCodeList, fundCodeList, hbOneNo, AssetTypeEnum.HIGH.getCode(), null);

            if (queryCurrentAssetResult != null && CollectionUtils.isNotEmpty(queryCurrentAssetResult.getAsset())) {
                List<QueryCurrentAssetResult.CurrentAssetDto> asset = queryCurrentAssetResult.getAsset();
                for (QueryCurrentAssetResult.CurrentAssetDto currentAssetDto : asset) {
                    HighFundAssetIncomeDomain domain = new HighFundAssetIncomeDomain();
                    BeanUtils.copyProperties(currentAssetDto, domain);
                    assets.add(domain);
                }
            }
        } catch (Exception e) {
            logger.error("queryCurrentOldAsset|QueryCurrentAssetOuterService error :", e);
        }
        return response;
    }

    /**
     * 查询资产中心,用户制定日期有没有持有制定产品(当天清仓的不算有持仓)
     * @param hbOneNo 一账通
     * @param fundCode 产品编码
     * @param queryDt 指定日期
     * @return 持有标识 0-未持有,1-持有
     */
    public String queryHisHoldInfo(String hbOneNo,String fundCode,String queryDt){
        logger.info("queryHisHoldInfo-查询资产中心,用户制定日期有没有持有制定产品,txAcctNo={},fundCode={},queryDt={}",hbOneNo,fundCode,queryDt);
        QueryHisHoldInfoRequest request = new QueryHisHoldInfoRequest();
        request.setEndDt(queryDt);
        request.setHboneNo(hbOneNo);
        request.setFundCode(fundCode);
        //分期收益
        logger.info("queryHisHoldInfo url is:{}", userHisHoldStateUrl);
        String resp = HttpConnectionPoolUtil.post(userHisHoldStateUrl, request);
        logger.info("queryHisHoldInfo url:{}, request:{}, response:{}", userHisHoldStateUrl, JSON.toJSONString(request), JSON.toJSONString(resp));
        if (StringUtils.isBlank(resp)) {
            return null;
        }
        try {
            JSONObject respObject = JSONObject.parseObject(resp);
            return respObject.getString("holdFlag");
        } catch (Exception e) {
            logger.error("根据产品编码,一账通号查不到分期收益信息,接口返回数据异常,hbOneNo={},fundCode={},resp={},e={}", hbOneNo, fundCode, resp, e);
            return null;
        }


    }


    /**
     * 分期收益
     */
    public StagesIncomeDto queryStagesIncome(String hbOneNo, String fundCode) {
        QueryStagesIncomeRequest request = new QueryStagesIncomeRequest();
        request.setHboneNo(hbOneNo);
        request.setFundCode(fundCode);

        //分期收益
        String url = assetClientHost + "/asset/high/incomestatistics/stagesincome";
        logger.info("queryStagesIncome url is:{}", url);
        String resp = HttpConnectionPoolUtil.post(url, request);
        logger.info("queryStagesIncome url:{}, request:{}, response:{}", url, JSON.toJSONString(request), JSON.toJSONString(resp));
        if (StringUtils.isBlank(resp)) {
            return null;
        }
        try {
            JSONObject respObject = JSONObject.parseObject(resp);
            return respObject.getObject("stagesIncome", StagesIncomeDto.class);
        } catch (Exception e) {
            logger.error("根据产品编码,一账通号查不到分期收益信息,接口返回数据异常,hbOneNo={},fundCode={},resp={},e={}", hbOneNo, fundCode, resp, e);
            return null;
        }

    }

    /**
     * 查询是否隐藏入口相关用户持仓条件信息
     */
    public HoldStateDomain queryUserHoldState(QueryHoldStateParam queryHoldStateParam) {
        //分期收益
        logger.info("queryUserHoldState url is:{}", userHoldStateUrl);
        String resp = HttpConnectionPoolUtil.post(userHoldStateUrl, queryHoldStateParam);
        logger.info("queryUserHoldState url:{}, queryHoldStateParam:{}, response:{}", userHoldStateUrl, JSON.toJSONString(queryHoldStateParam), JSON.toJSONString(resp));
        if (StringUtils.isBlank(resp)) {
            return null;
        }
        try {
            JSONObject respObject = JSONObject.parseObject(resp);
            return respObject.getObject("holdStateDomain", HoldStateDomain.class);
        } catch (Exception e) {
            logger.error("查询是否隐藏入口相关用户持仓条件信息,接口返回数据异常,queryHoldStateParam:{},resp={},e={}", JSON.toJSONString(queryHoldStateParam), resp, e);
            return null;
        }

    }
}
