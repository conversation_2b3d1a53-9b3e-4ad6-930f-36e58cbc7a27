/**
 * Copyright (c) 2017, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.orders.service.facade.search.queryfinreceipt;

import com.alibaba.fastjson.JSON;
import com.howbuy.dtms.order.client.domain.request.finrecept.QueryFinReciptRequest;
import com.howbuy.dtms.order.client.domain.response.Response;
import com.howbuy.dtms.order.client.facade.query.balance.QueryFinreceiptFacade;
import com.howbuy.tms.common.constant.ExceptionCodes;
import com.howbuy.tms.common.enums.busi.DisCodeEnum;
import com.howbuy.tms.common.enums.busi.ScaleTypeEnum;
import com.howbuy.tms.common.enums.busi.YesOrNoEnum;
import com.howbuy.tms.common.message.MessageSource;
import com.howbuy.tms.common.outerservice.cc.center.queryhboneno.QueryHbOneNoOuterService;
import com.howbuy.tms.high.orders.dao.vo.DealOrderVo;
import com.howbuy.tms.high.orders.facade.search.queryacctbalance.QueryAcctBalanceResponse;
import com.howbuy.tms.high.orders.facade.search.queryfinreceipt.QueryFinReceiptFacade;
import com.howbuy.tms.high.orders.facade.search.queryfinreceipt.QueryFinReceiptRequest;
import com.howbuy.tms.high.orders.facade.search.queryfinreceipt.QueryFinReceiptResponse;
import com.howbuy.tms.high.orders.service.common.enums.HighOrderConstants;
import com.howbuy.tms.high.orders.service.service.acctbalancebaseinfo.AcctBalanceBaseInfoService;
import com.howbuy.tms.high.orders.service.service.acctbalancebaseinfo.bean.BuyUnConfirmUnHkOrderBean;
import com.howbuy.tms.high.orders.service.service.acctbalancebaseinfo.bean.QueryBalanceParam;
import com.howbuy.tms.high.orders.service.service.acctbalancebaseinfo.bean.RefundDealOrderInfo;
import com.howbuy.tms.high.orders.service.service.acctbalancebaseinfo.bean.SellUnConfirmUnHkOrderBean;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR> @description:查询在途笔数、资金到账提醒数据接口实现
 * @reason:
 * @date 202年1月10日 下午8:24:12
 * @since JDK 1.8
 */
@DubboService
@Service("queryFinReceiptFacade")
public class QueryFinReceiptFacadeService implements QueryFinReceiptFacade {

    private static final Logger logger = LogManager.getLogger(QueryFinReceiptFacadeService.class);

    @Autowired
    private QueryHbOneNoOuterService queryHbOneNoOuterService;
    @Autowired
    private AcctBalanceBaseInfoService acctBalanceBaseInfoService;
    @Autowired
    private QueryFinreceiptFacade hkQueryFinreceiptFacade;


    /**
     * @api {DUBBO} com.howbuy.tms.high.orders.facade.search.queryfinreceipt.QueryFinReceiptFacade.execute(QueryFinReceiptRequest request)
     * @apiVersion 1.0.0
     * @apiGroup QueryFinReceiptFacadeService
     * @apiName execute
     * @apiDescription 查询在途笔数、资金到账提醒数据接口实现
     * @apiParam (请求参数) {String} txAcctNo 交易账号
     * @apiParam (请求参数) {Array} disCodeList 分销机构代码列表
     * @apiParam (请求参数) {String} onlyHkProduct 是否仅需香港产品
     * @apiParam (请求参数) {String} notFilterHkFund 不过滤香港产品,1:是,0:否
     * @apiParam (请求参数) {String} notFilterHzFund 不过滤好臻产品,1:是,0:否
     * @apiParam (请求参数) {String} txAcctNo 交易账号
     * @apiParam (请求参数) {String} hbOneNo 一账通账号
     * @apiParam (请求参数) {String} disCode 分销机构代码
     * @apiParam (请求参数) {String} outletCode 网点代码
     * @apiParam (请求参数) {String} appDt 申请日期
     * @apiParam (请求参数) {String} appTm 申请时间
     * @apiParam (请求参数) {Number} pageNo 页码
     * @apiParam (请求参数) {Number} pageSize 每页记录数
     * @apiParam (请求参数) {String} operIp 交易IP
     * @apiParam (请求参数) {String} txCode 交易码
     * @apiParam (请求参数) {String} txChannel 交易渠道 1-柜台,2-网站,3-电话,4-Wap,5-App,6-机构
     * @apiParam (请求参数) {String} dataTrack 数据跟踪
     * @apiParam (请求参数) {String} subOutletCode 子网点代码
     * @apiParamExample 请求参数示例
     * notFilterHzFund=a251NH&hbOneNo=XQ&pageSize=8517&disCode=Utv&txChannel=yNRoFsZ&appTm=fNn&disCodeList=mJTC&subOutletCode=ZhL4Lx&pageNo=4988&operIp=jvw6EpT&txAcctNo=bTU1hPz&onlyHkProduct=s&appDt=mMo1KAYe&dataTrack=4eYdaJ3l0&notFilterHkFund=Pmze&txCode=kwt&outletCode=5O
     * @apiSuccess (响应结果) {String} txAcctNo 交易账号
     * @apiSuccess (响应结果) {String} hbOneNo 一账通账号
     * @apiSuccess (响应结果) {Number} buyUnrefundedPiece 购买待退款订单数
     * @apiSuccess (响应结果) {Number} redeemUnrefundedPiece 赎回待回款订单数
     * @apiSuccess (响应结果) {Number} totalBuyUnConfirmAmt 买入待确认金额
     * @apiSuccess (响应结果) {Number} totalUnRefundAmt 即将资金到账金额
     * @apiSuccess (响应结果) {Number} totalUnRefundNum 即将资金到账订单数
     * @apiSuccess (响应结果) {Number} totalOnWayNum 总在途订单数:包含待付款订单数+待确认订单数
     * @apiSuccess (响应结果) {Array} unpaidList 待付款订单
     * @apiSuccess (响应结果) {String} unpaidList.dealNo 订单号
     * @apiSuccess (响应结果) {String} unpaidList.dealType 订单类型：0-直销、1-代销
     * @apiSuccess (响应结果) {Array} unconfirmedList 待确认订单
     * @apiSuccess (响应结果) {String} unconfirmedList.dealNo 订单号
     * @apiSuccess (响应结果) {String} unconfirmedList.dealType 订单类型：0-直销、1-代销
     * @apiSuccess (响应结果) {String} returnCode 返回码
     * @apiSuccess (响应结果) {String} description 描述
     * @apiSuccess (响应结果) {Number} totalCount 总记录数
     * @apiSuccess (响应结果) {Number} totalPage 总页数
     * @apiSuccess (响应结果) {Number} pageNo 当前页
     * @apiSuccessExample 响应结果示例
     * {"returnCode":"p","hbOneNo":"R7WZ","totalPage":3439,"pageNo":7413,"txAcctNo":"fFmMkOStY","unpaidList":[{"dealNo":"uMUAcfFy","dealType":"i"}],"description":"PZS","buyUnrefundedPiece":4768,"redeemUnrefundedPiece":5889,"totalCount":9102,"unconfirmedList":[{"dealNo":"gskzZG","dealType":"lGxcn1y"}]}
     */
    @Override
    public QueryFinReceiptResponse execute(QueryFinReceiptRequest request) {
        // 重新设置参数,加上鉴权逻辑
        setAndRebuildRequest(request);
        if (StringUtils.isBlank(request.getTxAcctNo())) {
            logger.error("QueryFinReceiptResponse-在途查询时交易账号为空,request:{}", JSON.toJSONString(request));
            QueryFinReceiptResponse response = new QueryFinReceiptResponse();
            response.setReturnCode(ExceptionCodes.SUCCESS);
            response.setDescription(MessageSource.getMessageByCode(ExceptionCodes.SUCCESS));
            response.setReturnCode(ExceptionCodes.ORDER_CENTER_TXCODE_IS_NULL_ERROR);
            response.setDescription(MessageSource.getMessageByCode(ExceptionCodes.ORDER_CENTER_TXCODE_IS_NULL_ERROR));
            return response;
        }
        // 查询国内在途信息
        QueryFinReceiptResponse unHkFinReceiptResponse = getUnHkFinReceiptInfo(request, request.getTxAcctNo(), request.getDisCodeList());
        // 查询海外在途信息
        com.howbuy.dtms.order.client.domain.response.finreceipt.QueryFinReceiptResponse hkFinReceiptResponse = getHkFinReceiptResponse(request.getHbOneNo(), request.getNotFilterHkFund());
        // 数据汇总
        QueryFinReceiptResponse queryFinReceiptResponse = buildFinReceiptResponse(unHkFinReceiptResponse, hkFinReceiptResponse);
        queryFinReceiptResponse.setTxAcctNo(request.getTxAcctNo());
        queryFinReceiptResponse.setHbOneNo(request.getHbOneNo());
        queryFinReceiptResponse.setReturnCode(ExceptionCodes.SUCCESS);
        queryFinReceiptResponse.setDescription(MessageSource.getMessageByCode(ExceptionCodes.SUCCESS));
        queryFinReceiptResponse.setReturnCode(ExceptionCodes.ORDER_CENTER_TXCODE_IS_NULL_ERROR);
        return queryFinReceiptResponse;
    }

    /**
     * 设置请求入参
     */
    private void setAndRebuildRequest(QueryFinReceiptRequest request) {
        // 初始化分销渠道
        request.setDisCodeList(getDisCodeList(request));
        if (StringUtils.isNotBlank(request.getIsAuth()) && YesOrNoEnum.NO.getCode().equals(request.getIsAuth())) {
            request.setNotFilterHkFund(YesOrNoEnum.NO.getCode());
            request.setNotFilterHzFund(YesOrNoEnum.NO.getCode());

        } else {
            if (StringUtils.isBlank(request.getNotFilterHkFund())) {
                request.setNotFilterHkFund(YesOrNoEnum.YES.getCode());
            }
            if (StringUtils.isBlank(request.getNotFilterHzFund())) {
                request.setNotFilterHzFund(YesOrNoEnum.YES.getCode());
            }
        }
        // 需要过滤好臻
        if (StringUtils.isNotBlank(request.getNotFilterHzFund()) && YesOrNoEnum.NO.getCode().equals(request.getNotFilterHzFund())) {
            request.getDisCodeList().remove(DisCodeEnum.HZ.getCode());
        }
        String txAcctNo = request.getTxAcctNo();
        String hbOneNo = request.getHbOneNo();
        if (StringUtils.isEmpty(txAcctNo) && StringUtils.isNotBlank(hbOneNo)) {
            txAcctNo = queryHbOneNoOuterService.queryTxAcctNoByHbOneNo(hbOneNo);
            request.setTxAcctNo(txAcctNo);
        }

        if (StringUtils.isEmpty(hbOneNo) && StringUtils.isNotBlank(txAcctNo)) {
            hbOneNo = queryHbOneNoOuterService.queryHbOneNoByTxAcctNo(txAcctNo);
            request.setHbOneNo(hbOneNo);
        }
    }

    private List<String> getDisCodeList(QueryFinReceiptRequest request) {
        if (CollectionUtils.isNotEmpty(request.getDisCodeList())) {
            return request.getDisCodeList();
        } else {
            List<String> disCodeList = new ArrayList<>();
            disCodeList.add(request.getDisCode());
            request.setDisCodeList(disCodeList);
            return disCodeList;
        }
    }

    /**
     * 合并在途返回信息
     */
    private QueryFinReceiptResponse buildFinReceiptResponse(QueryFinReceiptResponse unHkReceiptResponse, com.howbuy.dtms.order.client.domain.response.finreceipt.QueryFinReceiptResponse hkQueryFinReceiptResponse) {
        QueryFinReceiptResponse queryFinReceiptResponse = new QueryFinReceiptResponse();
        queryFinReceiptResponse.setReturnCode(unHkReceiptResponse.getReturnCode());
        // 购买待退款订单数
        int buyUnRefundedPiece = unHkReceiptResponse.getBuyUnrefundedPiece() == null ? 0 : unHkReceiptResponse.getBuyUnrefundedPiece();
        int hkUnRefundedPiece = hkQueryFinReceiptResponse.getBuyUnrefundedPiece() == null ? 0 : hkQueryFinReceiptResponse.getBuyUnrefundedPiece();
        queryFinReceiptResponse.setBuyUnrefundedPiece(buyUnRefundedPiece + hkUnRefundedPiece);
        // 赎回待回款订单数
        int unHkRedeemUnRefundedPiece = unHkReceiptResponse.getRedeemUnrefundedPiece() == null ? 0 : unHkReceiptResponse.getRedeemUnrefundedPiece();
        int hkRedeemUnRefundedPiece = hkQueryFinReceiptResponse.getRedeemUnrefundedPiece() == null ? 0 : hkQueryFinReceiptResponse.getRedeemUnrefundedPiece();
        queryFinReceiptResponse.setRedeemUnrefundedPiece(unHkRedeemUnRefundedPiece + hkRedeemUnRefundedPiece);
        // 待付款订单
        List<QueryAcctBalanceResponse.DealOrderBean> dealOrderBeanList = new ArrayList<>();
        List<QueryAcctBalanceResponse.DealOrderBean> unHkUnpaidList = unHkReceiptResponse.getUnpaidList();
        if (CollectionUtils.isNotEmpty(unHkUnpaidList)) {
            dealOrderBeanList.addAll(unHkUnpaidList);
        }
        List<String> hkUnpaidList = hkQueryFinReceiptResponse.getUnpaidList();
        if (CollectionUtils.isNotEmpty(hkUnpaidList)) {
            for (String dealNo : hkUnpaidList) {
                QueryAcctBalanceResponse.DealOrderBean dealOrderBean = new QueryAcctBalanceResponse.DealOrderBean();
                dealOrderBean.setDealNo(dealNo);
                dealOrderBean.setDealType("0");
                dealOrderBeanList.add(dealOrderBean);
            }
        }
        queryFinReceiptResponse.setUnpaidList(dealOrderBeanList);
        // 待确认订单
        List<QueryAcctBalanceResponse.DealOrderBean> unConfirmedList = new ArrayList<>();
        List<QueryAcctBalanceResponse.DealOrderBean> unHkUnConfirmedList = unHkReceiptResponse.getUnconfirmedList();
        if (CollectionUtils.isNotEmpty(unHkUnConfirmedList)) {
            unConfirmedList.addAll(unHkUnConfirmedList);
        }
        List<String> hkUnConfirmedList = hkQueryFinReceiptResponse.getUnconfirmedList();
        if (CollectionUtils.isNotEmpty(hkUnConfirmedList)) {
            for (String dealNo : hkUnConfirmedList) {
                QueryAcctBalanceResponse.DealOrderBean dealOrderBean = new QueryAcctBalanceResponse.DealOrderBean();
                dealOrderBean.setDealNo(dealNo);
                dealOrderBean.setDealType("0");
                unConfirmedList.add(dealOrderBean);
            }
        }
        queryFinReceiptResponse.setUnconfirmedList(unConfirmedList);
        // todo  在途订单数,金额,待资金到账进的等

        return queryFinReceiptResponse;
    }

    /**
     * 国内在途 信息
     */
    private QueryFinReceiptResponse getUnHkFinReceiptInfo(QueryFinReceiptRequest request, String txAcctNo, List<String> disCodeList) {
        if (request.getOnlyHkProduct() != null && YesOrNoEnum.YES.getCode().equals(request.getOnlyHkProduct())) {
            logger.info("仅查询香港在途,不需要查询非香港在途接口");
            QueryFinReceiptResponse queryFinReceiptResponse = new QueryFinReceiptResponse();
            queryFinReceiptResponse.setReturnCode(ExceptionCodes.SUCCESS);
            return queryFinReceiptResponse;
        }
        QueryFinReceiptResponse response = new QueryFinReceiptResponse();
        QueryBalanceParam queryBalanceParam = new QueryBalanceParam();
        queryBalanceParam.setTxAcctNo(txAcctNo);
        if (request.getNotFilterHzFund() != null && YesOrNoEnum.NO.getCode().equals(request.getNotFilterHzFund())) {
            disCodeList = disCodeList.stream().filter(x -> !DisCodeEnum.HZ.getCode().equals(x)).collect(Collectors.toList());
        }
        queryBalanceParam.setDisCodeList(disCodeList);
        // 待资金到账信息
        setUnRefundInfo(queryBalanceParam, response);
        // 待付款订单
        setUnPaidInfo(queryBalanceParam, response);
        // 待确认订单
        setUnConfirmInfo(queryBalanceParam, response);
        return response;
    }

    /**
     * 待确认订单
     */
    private void setUnConfirmInfo(QueryBalanceParam queryBalanceParam, QueryFinReceiptResponse response) {
        List<QueryAcctBalanceResponse.DealOrderBean> unConfirmList = new ArrayList<>();
        List<BuyUnConfirmUnHkOrderBean> buyUnConfirmUnHkOrderBeans = acctBalanceBaseInfoService.queryBuyUnConfirmUnHkOrderList(queryBalanceParam);
        for (BuyUnConfirmUnHkOrderBean buyUnConfirmUnHkOrderBean : buyUnConfirmUnHkOrderBeans) {
            QueryAcctBalanceResponse.DealOrderBean dealOrderBean = new QueryAcctBalanceResponse.DealOrderBean();
            dealOrderBean.setDealNo(buyUnConfirmUnHkOrderBean.getDealNo());
            dealOrderBean.setDealType(ScaleTypeEnum.DIRECT.getCode().equals(buyUnConfirmUnHkOrderBean.getScaleType()) ? HighOrderConstants.DIRECT_DEAL_TYPE : HighOrderConstants.CONSIGNMENT_DEAL_TYPE);
            unConfirmList.add(dealOrderBean);
        }
        List<SellUnConfirmUnHkOrderBean> sellUnConfirmUnHkOrderBeans = acctBalanceBaseInfoService.querySellUnConfirmUnHkOrderList(queryBalanceParam);
        for (SellUnConfirmUnHkOrderBean sellUnConfirmUnHkOrderBean : sellUnConfirmUnHkOrderBeans) {
            QueryAcctBalanceResponse.DealOrderBean dealOrderBean = new QueryAcctBalanceResponse.DealOrderBean();
            dealOrderBean.setDealNo(sellUnConfirmUnHkOrderBean.getDealNo());
            dealOrderBean.setDealType(ScaleTypeEnum.DIRECT.getCode().equals(sellUnConfirmUnHkOrderBean.getScaleType()) ? HighOrderConstants.DIRECT_DEAL_TYPE : HighOrderConstants.CONSIGNMENT_DEAL_TYPE);
            unConfirmList.add(dealOrderBean);
        }
        response.setUnconfirmedList(unConfirmList);
        response.setTotalOnWayNum(response.getTotalOnWayNum() + unConfirmList.size());
        response.setTotalBuyUnConfirmAmt(buyUnConfirmUnHkOrderBeans.stream().map(BuyUnConfirmUnHkOrderBean::getUnConfirmAmt).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
    }

    /**
     * 待付款订单
     */
    private void setUnPaidInfo(QueryBalanceParam queryBalanceParam, QueryFinReceiptResponse response) {
        List<QueryAcctBalanceResponse.DealOrderBean> unPaidList = new ArrayList<>();
        List<DealOrderVo> dealOrderVos = acctBalanceBaseInfoService.queryUnPayUnHkOrderList(queryBalanceParam);
        for (DealOrderVo dealOrderVo : dealOrderVos) {
            QueryAcctBalanceResponse.DealOrderBean dealOrderBean = new QueryAcctBalanceResponse.DealOrderBean();
            dealOrderBean.setDealNo(dealOrderVo.getDealNo());
            dealOrderBean.setDealType(dealOrderVo.getDealType());
            unPaidList.add(dealOrderBean);
        }
        response.setUnpaidList(unPaidList);
        response.setTotalOnWayNum(response.getTotalOnWayNum() + dealOrderVos.size());
    }

    /**
     * 待资金到账信息
     */
    private void setUnRefundInfo(QueryBalanceParam queryBalanceParam, QueryFinReceiptResponse response) {
        // 获取资金提醒笔数
        List<RefundDealOrderInfo> refundDealOrderInfos = acctBalanceBaseInfoService.queryRefundAmtDealOrderInfo(queryBalanceParam);
        List<RefundDealOrderInfo> buyRefundList = refundDealOrderInfos.stream().filter(x -> YesOrNoEnum.YES.getCode().equals(x.getIsBuyRefund())).collect(Collectors.toList());
        List<RefundDealOrderInfo> redeemRefundList = refundDealOrderInfos.stream().filter(x -> YesOrNoEnum.NO.getCode().equals(x.getIsBuyRefund())).collect(Collectors.toList());
        response.setBuyUnrefundedPiece(buyRefundList.size());
        response.setRedeemUnrefundedPiece(redeemRefundList.size());
        response.setTotalUnRefundNum(buyRefundList.size() + redeemRefundList.size());
        response.setTotalUnRefundAmt(refundDealOrderInfos.stream().map(RefundDealOrderInfo::getRefundAmt).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
    }

    /**
     * 香港在途信息
     */
    private com.howbuy.dtms.order.client.domain.response.finreceipt.QueryFinReceiptResponse getHkFinReceiptResponse(String hboneNo, String notFilterHzFund) {
        if (StringUtils.isBlank(hboneNo)) {
            logger.info("没有一账通号,不需要查询香港在途信息");
            return new com.howbuy.dtms.order.client.domain.response.finreceipt.QueryFinReceiptResponse();
        }
        if (YesOrNoEnum.NO.getCode().equals(notFilterHzFund)) {
            logger.info("没有香港数据授权,不查询香港在途信息");
            return new com.howbuy.dtms.order.client.domain.response.finreceipt.QueryFinReceiptResponse();
        }

        QueryFinReciptRequest queryFinReciptRequest = new QueryFinReciptRequest();
        queryFinReciptRequest.setHbOneNo(hboneNo);
        logger.info("查询海外在途-start,queryFinReciptRequest={}", JSON.toJSONString(queryFinReciptRequest));
        Response<com.howbuy.dtms.order.client.domain.response.finreceipt.QueryFinReceiptResponse> hkResponse = hkQueryFinreceiptFacade.execute(queryFinReciptRequest);
        logger.info("查询海外在途-结果,hkResponse={}", JSON.toJSONString(hkResponse));
        if (!HighOrderConstants.HK_SUCCESS_CODE.equals(hkResponse.getCode()) || hkResponse.getData() == null) {
            return new com.howbuy.dtms.order.client.domain.response.finreceipt.QueryFinReceiptResponse();
        }
        return hkResponse.getData();
    }


}
