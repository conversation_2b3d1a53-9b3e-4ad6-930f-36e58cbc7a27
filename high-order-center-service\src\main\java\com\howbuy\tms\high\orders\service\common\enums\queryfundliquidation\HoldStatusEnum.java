/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.orders.service.common.enums.queryfundliquidation;

/**
 * @description: 持仓状态枚举
 * <AUTHOR>
 * @date 2025/9/11
 * @since JDK 1.8
 */
public enum HoldStatusEnum {

    /**
     * 持仓
     */
    HOLDING("1", "持仓"),

    /**
     * 清仓
     */
    LIQUIDATION("2", "清仓");

    /**
     * 状态代码
     */
    private final String code;

    /**
     * 状态名称
     */
    private final String name;

    HoldStatusEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    /**
     * 根据代码获取枚举
     *
     * @param code 状态代码
     * @return 持仓状态枚举
     */
    public static HoldStatusEnum fromCode(String code) {
        for (HoldStatusEnum status : HoldStatusEnum.values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 检查代码是否有效
     *
     * @param code 状态代码
     * @return 是否有效
     */
    public static boolean isValidCode(String code) {
        return fromCode(code) != null;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}