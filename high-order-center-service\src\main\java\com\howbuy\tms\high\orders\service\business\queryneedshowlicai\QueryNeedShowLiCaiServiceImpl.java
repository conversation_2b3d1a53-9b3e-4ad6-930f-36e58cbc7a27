package com.howbuy.tms.high.orders.service.business.queryneedshowlicai;

import com.alibaba.fastjson.JSON;
import com.howbuy.acccenter.facade.query.queryacccustinfo.bean.AccCustInfoBean;
import com.howbuy.interlayer.product.enums.DisCodeEnum;
import com.howbuy.interlayer.product.enums.high.LiCaiShouYiWhiteTypeEnum;
import com.howbuy.interlayer.product.model.high.LiCaiShouYiWhiteConfModel;
import com.howbuy.interlayer.product.service.high.LiCaiShouYiWhiteConfService;
import com.howbuy.tms.common.enums.busi.YesOrNoEnum;
import com.howbuy.tms.common.enums.database.InvstTypeEnum;
import com.howbuy.tms.common.enums.database.TxChannelEnum;
import com.howbuy.tms.common.outerservice.acccenter.querycustInfo.QueryCustInfoOuterService;
import com.howbuy.tms.common.outerservice.cc.center.queryhboneno.QueryHbOneNoOuterService;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.QueryHighProductOuterService;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductFieldControlBean;
import com.howbuy.tms.common.utils.DateUtils;
import com.howbuy.tms.common.utils.StringUtils;
import com.howbuy.tms.high.orders.dao.po.UserShowResultInfoPo;
import com.howbuy.tms.high.orders.facade.search.queryacctBalancetotalinfo.QueryAcctBalanceTotalInfoFacadeRequest;
import com.howbuy.tms.high.orders.facade.search.queryacctBalancetotalinfo.QueryAcctBalanceTotalInfoFacadeResponse;
import com.howbuy.tms.high.orders.service.facade.search.queryLiCaiResult.AcctCustInfo;
import com.howbuy.tms.high.orders.service.facade.search.queryacctBalancetotalnfo.QueryAcctBalanceTotalInfoFacadeService;
import com.howbuy.tms.high.orders.service.service.acctbalancebaseinfo.AcctBalanceBaseInfoService;
import com.howbuy.tms.high.orders.service.service.acctbalancebaseinfo.bean.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description:是否需要理财分析逻辑
 * @Author: yun.lu
 * Date: 2024/12/10 15:51
 */
@Service("queryNeedShowLiCaiService")
@Slf4j
public class QueryNeedShowLiCaiServiceImpl {
    @Autowired
    private LiCaiShouYiWhiteConfService liCaiShouYiWhiteConfService;
    @Autowired
    private QueryCustInfoOuterService queryCustInfoOuterService;
    @Autowired
    private QueryHighProductOuterService queryHighProductOuterService;
    @Autowired
    private QueryHbOneNoOuterService queryHbOneNoOuterService;
    @Autowired
    private AcctBalanceBaseInfoService acctBalanceBaseInfoService;

    @Value("${liCai_hide_hbOneNo}")
    private String liCaiHideHbOneNo;

    /**
     * 私募首页_理财分析按钮_需要隐藏的产品
     */
    @Value("${liCai_hide_fund}")
    private String liCaiHideFund;

    /**
     * 原“理财分析”功能更名为“收益分析”
     * 1、入口显隐逻辑：满足以下任一条件，需隐藏入口，否则，入口正常展示
     * 通用说明
     *  好买基金产品：【是否香港专区HKSaleFlag】≠是  且【分销渠道】≠好臻分销 的产品
     *  持有条件：代销产品【持仓份额】>0；直销产品【持仓份额】＞1；
     * (1) 条件1：当前账户无好买基金产品的持仓
     * (2)条件2：当前账户，曾经持有过“特殊隐藏配置表”内的产品
     * (3)条件3：当前账户，曾经持有过理财分析隐藏产品”配置内的产品
     *   ① 取配置（非表）的产品，历史初始化配置包括：PE0053/W00001/SJC911/GD0793/GD0797/GD0798/GD0784/GD0785/170060/170110/170063/GD0513【本次新增】；
     *   ② 按母代码配置；
     * (4)条件5：当前账号【客户类型】=机构/产品
     * (5)条件6：当前客户在“理财分析隐藏客户”配置内,注：按【一账通】配置
     * 2、入口支持白名单配置，若在白名单内用户，则无需校验上面隐藏逻辑，固定显示 收益分析 入口
     *  ① 名单内容都包括：客户一账通号码、白名单添加日期时间、白名单添加申请人
     *  ② 产线已配置白名单客户：8002988714、8013714537、9200224630
     * @param hbOneNo 一账通号
     * @param txAcctNo 交易账号
     * @return UserShowResultInfoPo
     */
    public UserShowResultInfoPo getNeedShowLiCai(String hbOneNo, String txAcctNo) {
        log.info("getNeedShowLiCai,获取是否需要展示收益分析入口-hbOneNo={}", JSON.toJSONString(hbOneNo));
        if (StringUtils.isBlank(hbOneNo) && StringUtils.isBlank(txAcctNo)) {
            log.info("交易账号与一账通都是空的,不能查询是否展示收益分析入口");
            return null;
        }
        if (StringUtils.isBlank(txAcctNo)) {
            txAcctNo = queryHbOneNoOuterService.queryTxAcctNoByHbOneNo(hbOneNo);
        }
        if (StringUtils.isBlank(hbOneNo)) {
            hbOneNo = queryHbOneNoOuterService.queryHbOneNoByTxAcctNo(txAcctNo);
        }
        UserShowResultInfoPo userShowResultInfoPo = new UserShowResultInfoPo();
        userShowResultInfoPo.setHbOneNo(hbOneNo);
        userShowResultInfoPo.setTxAcctNo(txAcctNo);
        Date now = new Date();
        userShowResultInfoPo.setCreateTime(now);
        userShowResultInfoPo.setUpdateTime(now);
        userShowResultInfoPo.setModifyDt(DateUtils.formatToString(now, DateUtils.YYYYMMDD));
        // 0.当前用户是否是配置中需要隐藏的一账通
        if (!StringUtils.isBlank(liCaiHideHbOneNo)) {
            List<String> liCaiHideHbOneNoList = Arrays.asList(liCaiHideHbOneNo.split(","));
            if (CollectionUtils.isNotEmpty(liCaiHideHbOneNoList) && liCaiHideHbOneNoList.contains(hbOneNo)) {
                log.info("getNeedShowLiCai-因为一账通隐藏入口,hbOneNo={},shouYiHideHbOneNoList={}", hbOneNo, liCaiHideHbOneNo);
                userShowResultInfoPo.setShowLiCai(YesOrNoEnum.NO.getCode());
                userShowResultInfoPo.setShowLiCaiReason("因为一账通隐藏入口");
                return userShowResultInfoPo;
            }
        }
        // 1.是否在白名单中
        LiCaiShouYiWhiteConfModel liCaiShouYiWhiteConfModel = liCaiShouYiWhiteConfService.queryByHbOneNoAndType(hbOneNo, LiCaiShouYiWhiteTypeEnum.LI_CAI.getType());
        if (liCaiShouYiWhiteConfModel != null && StringUtils.isNotBlank(liCaiShouYiWhiteConfModel.getHbOneNo())) {
            log.info("getNeedShowLiCai-在白名单中,需要展示收益分析入口,hbOneNo={}", hbOneNo);
            userShowResultInfoPo.setShowLiCai(YesOrNoEnum.YES.getCode());
            userShowResultInfoPo.setShowLiCaiReason("在白名单中,需要展示收益分析入口");
            return userShowResultInfoPo;
        }
        // 3.当前账号【客户类型】=机构/产品
        AcctCustInfo acctCustInfo = queryAcctCustInfo(hbOneNo);
        if (acctCustInfo.isOrganizationAndFundAcct()) {
            log.info("当前账号【客户类型】=机构/产品,不展示收益分析入口,hbOneNo={}", hbOneNo);
            userShowResultInfoPo.setShowLiCai(YesOrNoEnum.NO.getCode());
            userShowResultInfoPo.setShowLiCaiReason("当前账号客户类型=机构/产品,不展示收益分析入口");
            return userShowResultInfoPo;
        }
        // 4.当前账户无好买基金产品的持仓,需要隐藏入口
        QueryBalanceParam param = new QueryBalanceParam();
        param.setTxAcctNo(txAcctNo);
        param.setHboneNo(hbOneNo);
        param.setDisCodeList(Collections.singletonList(DisCodeEnum.HOWBUY.getCode()));
        List<CustConfirmBalanceDto> confirmBalanceDtoList = acctBalanceBaseInfoService.queryUnHkCustConfirmBalance(param);
        if(CollectionUtils.isEmpty(confirmBalanceDtoList)){
            log.info("没有好买确认持仓,hbOneNo={},txAcctNo={}", hbOneNo,txAcctNo);
            userShowResultInfoPo.setShowLiCai(YesOrNoEnum.NO.getCode());
            userShowResultInfoPo.setShowLiCaiReason("没有好买确认持仓,不展示收益分析入口");
            return userShowResultInfoPo;
        }
        List<String> balanceFundCodeList = confirmBalanceDtoList.stream().map(CustConfirmBalanceDto::getFundCode).distinct().collect(Collectors.toList());
        // 查询清仓产品信息
        List<QueryLiquidationBalanceFundInfo> queryLiquidationBalanceFundInfoList = acctBalanceBaseInfoService.queryLiquidationBalanceFundInfo(param);
        if(CollectionUtils.isNotEmpty(queryLiquidationBalanceFundInfoList)){
            List<String> liquidationFundCodeList = queryLiquidationBalanceFundInfoList.stream().map(QueryLiquidationBalanceFundInfo::getFundCode).distinct().collect(Collectors.toList());
            balanceFundCodeList.addAll(liquidationFundCodeList);
        }
        // 配置的需要隐藏的产品
        List<String> liCaiHideFundList = new ArrayList<>();
        if (!StringUtils.isBlank(liCaiHideFund)) {
            liCaiHideFundList = Arrays.asList(liCaiHideFund.split(","));
        }
        // 查询所有有隐藏指标字段的产品
        List<HighProductFieldControlBean> fieldControlBeanList = queryHighProductOuterService.getAllFiledControlCached();
        List<String> fieldControlFundList = fieldControlBeanList.stream().map(HighProductFieldControlBean::getFundCode).distinct().collect(Collectors.toList());
        // 5.当前账户，曾经持有过“特殊隐藏配置表”内的产品
        for (String fundCode : balanceFundCodeList) {
            if(liCaiHideFundList.contains(fundCode)){
                log.info("曾经持有过“特殊隐藏配置表”内的产品,liCaiHideFundList={},hbOneNo={},txAcctNo={}",liCaiHideFundList, hbOneNo,txAcctNo);
                userShowResultInfoPo.setShowLiCai(YesOrNoEnum.NO.getCode());
                userShowResultInfoPo.setShowLiCaiReason("曾经持有过“特殊隐藏配置表”内的产品,不展示收益分析入口");
                return userShowResultInfoPo;
            }
        }

        // 6.当前账户，曾经持有过理财分析隐藏产品”配置内的产品
        for (String fundCode : balanceFundCodeList) {
            if(fieldControlFundList.contains(fundCode)){
                log.info("曾经持有过理财分析隐藏产品,fieldControlFundList={},hbOneNo={},txAcctNo={}",fieldControlFundList, hbOneNo,txAcctNo);
                userShowResultInfoPo.setShowLiCai(YesOrNoEnum.NO.getCode());
                userShowResultInfoPo.setShowLiCaiReason("曾经持有过理财分析隐藏产品,不展示收益分析入口");
                return userShowResultInfoPo;
            }
        }
        log.info("不满足不展示条件,展示收益分析入口,hbOneNo={}", hbOneNo);
        userShowResultInfoPo.setShowLiCai(YesOrNoEnum.YES.getCode());
        userShowResultInfoPo.setShowLiCaiReason("不满足不展示条件,展示收益分析入口");
        return userShowResultInfoPo;
    }

    /**
     * 校验结果
     */
    private UserShowResultInfoPo check(String hbOneNo, UserShowResultInfoPo userShowResultInfoPo, AcctCustInfo acctCustInfo, List<String> liCaiHideFundList, QueryAcctBalanceTotalInfoFacadeResponse response) {
        // 4.当前无持仓
        if (YesOrNoEnum.NO.getCode().equals(response.getHasBalance())) {
            log.info("当前账号当前无持仓,不展示理财分析入口,hbOneNo={}", hbOneNo);
            userShowResultInfoPo.setShowLiCai(YesOrNoEnum.NO.getCode());
            userShowResultInfoPo.setShowLiCaiReason("当前账号当前无持仓,不展示理财分析入口");
            return userShowResultInfoPo;
        }
        // 5.当前账户近1年内，曾经持有过“特殊隐藏配置表”内的产品
        if (YesOrNoEnum.YES.getCode().equals(response.getBalanceHiddenConfFundOneYear())) {
            log.info("当前账户近1年内，曾经持有过特殊隐藏配置表内的产品,不展示理财分析入口,hbOneNo={}", hbOneNo);
            userShowResultInfoPo.setShowLiCai(YesOrNoEnum.NO.getCode());
            userShowResultInfoPo.setShowLiCaiReason("当前账户近1年内，曾经持有过特殊隐藏配置表内的产品,不展示理财分析入口");
            return userShowResultInfoPo;
        }
        // 6.当前账户近1年内，曾经持配置中的产品
        if (CollectionUtils.isNotEmpty(liCaiHideFundList) && CollectionUtils.isNotEmpty(response.getBalanceConfigFundCodeList())) {
            log.info("当前账户近1年内，曾经持配置中的产品,不展示理财分析入口,hbOneNo={}", hbOneNo);
            userShowResultInfoPo.setShowLiCai(YesOrNoEnum.NO.getCode());
            userShowResultInfoPo.setShowLiCaiReason("当前账户近1年内，曾经持配置中的产品,不展示理财分析入口");
            return userShowResultInfoPo;
        }
        // 7.当前账户近1年内，曾经持有过“特殊产品交易类型转译表”内的产品
        if (YesOrNoEnum.YES.getCode().equals(response.getBalanceTransferConfFundOneYear())) {
            log.info("当前账户近1年内，曾经持有过特殊产品交易类型转译表内的产品,不展示理财分析入口,hbOneNo={}", hbOneNo);
            userShowResultInfoPo.setShowLiCai(YesOrNoEnum.NO.getCode());
            userShowResultInfoPo.setShowLiCaiReason("当前账户近1年内，曾经持有过特殊产品交易类型转译表内的产品,不展示理财分析入口");
            return userShowResultInfoPo;
        }
        // 8.若当前账号持有好臻/香港产品，且账号未授权查看好买旗下所有产品
        if (YesOrNoEnum.NO.getCode().equals(acctCustInfo.getIsDataAuth()) && (YesOrNoEnum.YES.getCode().equals(response.getHasHkProduct()) || YesOrNoEnum.YES.getCode().equals(response.getHasHzProduct()))) {
            log.info("若当前账号持有好臻/香港产品，且账号未授权查看好买旗下所有产品,不展示理财分析入口,hbOneNo={}", hbOneNo);
            userShowResultInfoPo.setShowLiCai(YesOrNoEnum.NO.getCode());
            userShowResultInfoPo.setShowLiCaiReason("若当前账号持有好臻/香港产品，且账号未授权查看好买旗下所有产品,不展示理财分析入口");
            return userShowResultInfoPo;
        }
        // 9:若当前账号持有香港产品，且当前账号需要进行香港资产隔离（即【香港资产隔离状态=是】）
        if (YesOrNoEnum.YES.getCode().equals(response.getHasHkProduct()) && YesOrNoEnum.YES.getCode().equals(acctCustInfo.getIsHkDataQuarantine())) {
            log.info("若当前账号持有香港产品，且当前账号需要进行香港资产隔离（即【香港资产隔离状态=是】）,不展示理财分析入口,hbOneNo={}", hbOneNo);
            userShowResultInfoPo.setShowLiCai(YesOrNoEnum.NO.getCode());
            userShowResultInfoPo.setShowLiCaiReason("若当前账号持有香港产品，且当前账号需要进行香港资产隔离（即香港资产隔离状态=是）,不展示理财分析入口");
            return userShowResultInfoPo;
        }
        return null;
    }

    /**
     * 是否是机构/产品客户
     *
     * @param hbOneNo 一账通
     * @return true:是;false:不是
     */
    private AcctCustInfo queryAcctCustInfo(String hbOneNo) {
        AcctCustInfo acctCustInfo = new AcctCustInfo();
        acctCustInfo.setOrganizationAndFundAcct(false);
        acctCustInfo.setIsDataAuth(YesOrNoEnum.NO.getCode());
        acctCustInfo.setIsHkDataQuarantine(YesOrNoEnum.NO.getCode());
        AccCustInfoBean accCustInfoBean = queryCustInfoOuterService.queryAccCustomerBaseInfo(hbOneNo);
        if (accCustInfoBean == null || StringUtils.isBlank(accCustInfoBean.getInvstType())) {
            log.info("isOrganizationAndFundAcct-查询用户信息为空,直接返回非机构用户,授权/是否香港数据为否");
            return acctCustInfo;
        }
        if (InvstTypeEnum.INST.getCode().equals(accCustInfoBean.getInvstType()) || InvstTypeEnum.PRODUCT.getCode().equals(accCustInfoBean.getInvstType())) {
            acctCustInfo.setOrganizationAndFundAcct(true);
        }
        String isAuth = accCustInfoBean.getDataAuthAgreementSign() == null ? YesOrNoEnum.NO.getCode() : accCustInfoBean.getDataAuthAgreementSign();
        acctCustInfo.setIsDataAuth(isAuth);
        String hkFlag = accCustInfoBean.getHkAssetIsolateFlag() == null ? YesOrNoEnum.NO.getCode() : accCustInfoBean.getHkAssetIsolateFlag();
        acctCustInfo.setIsHkDataQuarantine(hkFlag);
        return acctCustInfo;
    }

}
