package com.howbuy.tms.high.orders.service.common.enums;

public enum FundBuyStatusNotCanbuyCodeEnum {
    // 产品信息不存在
    PRODUCT_NOT_EXIST("00001","产品信息不存在"),
    // 购买的产品的[交易渠道]≠“非柜台”
    PRODUCT_TRADE_CHANNEL_NOT_COUNTER("00002","购买的产品的[交易渠道]≠“非柜台”"),
    // 交易开通配置表中，未开通交易
    PRODUCT_TRADE_CHANNEL_NOT_OPEN("00003","交易开通配置表中，未开通交易"),
    // 客户在高端产品黑名单中
    CUST_IN_HIGH_PRODUCT_BLACK_LIST("00004","客户在高端产品黑名单中"),
    // 客户在直销产品黑名单中
    CUST_IN_DIRECT_PRODUCT_BLACK_LIST("00005","客户在直销产品黑名单中"),
    // 产品为白名单产品 且 客户不在该产品白名单内
    PRODUCT_IN_WHITE_LIST_CUST_NOT_IN("00006","产品为白名单产品 且 客户不在该产品白名单内"),
    // 客户不符合产品的[可购买客户类型]
    CUST_NOT_IN_PRODUCT_CUST_TYPE("00007","客户不符合产品的[可购买客户类型]"),
    // 客户不符合产品的年龄限制
    CUST_NOT_IN_PRODUCT_AGE_LIMIT("00008","客户不符合产品的年龄限制"),
    // 本基金目前不在预约期内
    PRODUCT_NOT_IN_RESERVE_PERIOD("00009","本基金目前不在预约期内"),
    // 当日的【基金状态】≠“可申购赎回”
    PRODUCT_STATUS_NOT_SUBSCRIBE_REDEEM("00010","本基金暂不支持线上交易。如有疑问，可联系您的理财师。"),
    // 产品募集总额已达到上限；
    PRODUCT_REACH_MAX_AMOUNT("00011","产品募集总额已达到上限"),
    // 若产品仅支持追加，但客户未持有该产品
    PRODUCT_ONLY_SUPPORT_ADDITION_CUST_NOT_HOLD("00012","若产品仅支持追加，但客户未持有该产品"),
    // 若产品购买人数已达到上线，但客户未持有该产品
    PRODUCT_REACH_MAX_BUY_NUM_CUST_NOT_HOLD("00013","若产品购买人数已达到上线，但客户未持有该产品"),
    // 客户尚未完成开户
    CUST_NOT_FINISH_HM_ACCOUNT("00014","客户尚未完成开户"),
    // 客户好买账号未激活
    CUST_HM_ACCOUNT_NOT_ACTIVE("00015","客户账号未激活"),
    // 客户账号未绑定银行卡
    CUST_HM_ACCOUNT_NOT_BIND_BANK("00016","客户账号未绑定银行卡"),

    ;


    FundBuyStatusNotCanbuyCodeEnum(String code,String desc){
        this.code = code;
        this.desc = desc;

    }

    private final String code;

    private final String desc;

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
