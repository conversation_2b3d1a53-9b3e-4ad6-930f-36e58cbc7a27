package com.howbuy.tms.high.orders.facade.search.queryacctbalance;

import com.howbuy.tms.high.orders.facade.common.BaseDto;
import lombok.Getter;
import lombok.Setter;

/**
 * @Description:产品持仓状态信息
 * @Author: yun.lu
 * Date: 2025/9/15 10:45
 */
@Getter
@Setter
public class FundBalanceDetailStatusInfoDTO extends BaseDto {
    /**
     * 是否是首次购买,未确认,1:是,0:不是
     */
    private String isFirstBuyUnConfirmed;
    /**
     * 是否是全赎已上报,,1:是,0:不是
     */
    private String isAllRedeemAllSubmit;
    /**
     * 是否是全赎待资金到账,,1:是,0:不是
     */
    private String isAllRedeemWaitRefund;
}
