/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.orders.facade.search.queryfundliquidation;

import com.howbuy.tms.common.enums.busi.DisCodeEnum;
import com.howbuy.tms.high.orders.facade.common.OrderSearchBaseRequest;
import com.howbuy.tms.high.orders.facade.enums.TxCodes;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * @description: 清仓产品详情查询请求参数
 * <AUTHOR>
 * @date 2025/9/4 21:00
 * @since JDK 1.8
 */
@Setter
@Getter
public class QueryFundLiquidationDetailRequest extends OrderSearchBaseRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * @api {dubbo} com.howbuy.tms.high.orders.facade.search.queryfundliquidation.QueryFundLiquidationDetailFacade.execute()
     * @apiVersion 1.0.0
     * @apiGroup QueryFundLiquidationDetailFacadeService
     * @apiName execute
     * @apiDescription 查询清仓产品详情
     * @apiParam (请求参数) {String} mainFundCode 主基金代码
     * @apiParam (请求参数) {String} hbOneNo 一账通号
     * @apiSuccess (返回参数) {String} fundName 基金名称
     * @apiSuccess (返回参数) {String} fundCode 基金代码
     * @apiSuccess (返回参数) {String} unit 单位
     * @apiSuccess (返回参数) {String} clearUpIncome 清仓收益
     * @apiSuccess (返回参数) {String} clearUpRate 清仓后涨跌幅
     * @apiSuccess (返回参数) {String} productType 产品类型
     * @apiSuccess (返回参数) {String} productSubType 产品子类型
     * @apiSuccess (返回参数) {String} totalHoldDays 累计持有天数
     * @apiSuccess (返回参数) {String} accumIncomeRate 累计收益率
     * @apiSuccess (返回参数) {String} incomeStatus 收益计算中标签 1:有计算中标签 0:无计算中标签
     * @apiSuccess (返回参数) {String} cashCollection 回款金额
     * @apiSuccess (返回参数) {String} cashCollectionProgress 回款进度
     * @apiSuccess (返回参数) {String} initInvestCost 初始投资成本
     * @apiSuccess (返回参数) {List} clearDetailList 清仓明细列表
     */

    /**
     * 构造函数，设置默认交易代码
     */
    public QueryFundLiquidationDetailRequest() {
        setTxCode(TxCodes.QUERY_FUND_LIQUIDATION_DETAIL);
        setDisCode(DisCodeEnum.HM.getCode());
    }

    /**
     * 主基金代码
     */
    private String mainFundCode;

    /**
     * 一账通号
     */
    private String hbOneNo;
}
