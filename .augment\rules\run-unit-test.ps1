# 单元测试执行脚本
param(
    [Parameter(Mandatory=$true)]
    [string]$TestClass,

    [Parameter(Mandatory=$false)]
    [switch]$SkipDependencies
)

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    Unit Test Execution Script" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Test Class: $TestClass" -ForegroundColor Yellow
Write-Host ""

# 安装依赖模块
if (-not $SkipDependencies) {
    Write-Host "[1/2] Installing dependencies..." -ForegroundColor Blue

    Write-Host "  Installing DAO module..." -ForegroundColor White
    mvn -pl high-order-center-dao install "-Dfile.encoding=UTF-8" -q
    if ($LASTEXITCODE -ne 0) {
        Write-Host "  ✗ DAO module installation failed" -ForegroundColor Red
        exit 1
    }
    Write-Host "  ✓ DAO module installed" -ForegroundColor Green

    Write-Host "  Installing Client module..." -ForegroundColor White
    mvn -pl high-order-center-client install "-Dfile.encoding=UTF-8" -q
    if ($LASTEXITCODE -ne 0) {
        Write-Host "  ✗ Client module installation failed" -ForegroundColor Red
        exit 1
    }
    Write-Host "  ✓ Client module installed" -ForegroundColor Green
    Write-Host ""
} else {
    Write-Host "[1/2] Skipping dependencies..." -ForegroundColor Yellow
    Write-Host ""
}

# 执行测试
Write-Host "[2/2] Running unit test..." -ForegroundColor Blue
mvn -pl high-order-center-service test "-Dtest=$TestClass" "-Djacoco.skip=true" "-Dfile.encoding=UTF-8"

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan

if ($LASTEXITCODE -eq 0) {
    Write-Host "✓ Test execution successful!" -ForegroundColor Green

    # 显示测试结果摘要
    $reportPath = "high-order-center-service\target\surefire-reports\com.howbuy.tms.high.orders.service.service.queryfundpositiondate.$TestClass.txt"
    if (Test-Path $reportPath) {
        $reportContent = Get-Content $reportPath
        $summaryLine = $reportContent | Where-Object { $_ -match "Tests run:" }
        if ($summaryLine) {
            Write-Host "Test Summary: $summaryLine" -ForegroundColor Green
        }
    }
} else {
    Write-Host "✗ Test execution failed!" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "Usage examples:" -ForegroundColor Cyan
Write-Host "  .\run-unit-test.ps1 QueryFundPositionDateServiceTestM" -ForegroundColor White
Write-Host "  .\run-unit-test.ps1 QueryFundPositionDateServiceTestM -SkipDependencies" -ForegroundColor White
