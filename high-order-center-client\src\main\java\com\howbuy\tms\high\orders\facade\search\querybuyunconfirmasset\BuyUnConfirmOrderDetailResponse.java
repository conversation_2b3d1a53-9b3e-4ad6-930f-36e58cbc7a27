/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.orders.facade.search.querybuyunconfirmasset;

import com.howbuy.tms.high.orders.facade.common.BaseDto;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * @description: 买入待确认订单明细响应
 * <AUTHOR>
 * @date 2025/9/19 21:00
 * @since JDK 1.8
 */
@Getter
@Setter
public class BuyUnConfirmOrderDetailResponse extends BaseDto {


    private static final long serialVersionUID = -7755026470596552276L;
    /**
     * 订单号
     */
    private String dealNo;

    /**
     * 分销渠道
     */
    private String disCode;

    /**
     * 产品编码
     */
    private String fundCode;

    /**
     * 待确认金额:申请净金额-储蓄罐预约冻结金额(人民币)
     */
    private BigDecimal unConfirmAmt;

    /**
     * 待确认金额当前币种
     */
    private BigDecimal currencyUnConfirmAmt;

    /**
     * 中台业务类型
     */
    private String mBusiCode;

    /**
     * 销售类型: 1-直销;2-代销
     */
    private String scaleType;
}
