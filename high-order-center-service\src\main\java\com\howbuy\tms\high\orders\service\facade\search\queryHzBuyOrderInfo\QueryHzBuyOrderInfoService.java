package com.howbuy.tms.high.orders.service.facade.search.queryHzBuyOrderInfo;

import com.alibaba.fastjson.JSON;
import com.howbuy.acccenter.facade.query.querybindcustbankcardlist.CustBankInfoBean;
import com.howbuy.common.bean.BeanUtils;
import com.howbuy.tms.common.constant.ExceptionCodes;
import com.howbuy.tms.common.enums.busi.BusinessCodeEnum;
import com.howbuy.tms.common.enums.busi.DisCodeEnum;
import com.howbuy.tms.common.enums.busi.YesOrNoEnum;
import com.howbuy.tms.common.enums.database.PayStatusEnum;
import com.howbuy.tms.common.message.MessageSource;
import com.howbuy.tms.common.outerservice.acccenter.querycustInfoforprivate.QueryCustInfoForPrivateOuterService;
import com.howbuy.tms.common.outerservice.acccenter.querycustInfoforprivate.QueryCustInfoForPrivateResult;
import com.howbuy.tms.common.outerservice.acccenter.querycustbankcard.QueryCustBankCardContext;
import com.howbuy.tms.common.outerservice.acccenter.querycustbankcard.QueryCustBankCardOuterService;
import com.howbuy.tms.common.outerservice.acccenter.querycustbankcard.QueryCustBankCardResult;
import com.howbuy.tms.common.outerservice.acccenter.querycustbankcard.QueryCustomerBankCardParam;
import com.howbuy.tms.common.outerservice.cc.center.queryhboneno.QueryHbOneNoOuterService;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.QueryHighProductOuterService;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductBaseInfoBean;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductFeeRateBean;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductInfoBean;
import com.howbuy.tms.common.outerservice.interlayer.queryproductinfo.bean.ProductAppointmentInfoBean;
import com.howbuy.tms.common.utils.StringUtils;
import com.howbuy.tms.high.orders.dao.po.DealOrderPo;
import com.howbuy.tms.high.orders.dao.po.HighDealOrderDtlPo;
import com.howbuy.tms.high.orders.facade.search.queryHzBuyOrderInfo.CustomerBankCardInfo;
import com.howbuy.tms.high.orders.facade.search.queryHzBuyOrderInfo.QueryHzBuyOrderInfoFacade;
import com.howbuy.tms.high.orders.facade.search.queryHzBuyOrderInfo.QueryHzBuyOrderInfoRequest;
import com.howbuy.tms.high.orders.facade.search.queryHzBuyOrderInfo.QueryHzBuyOrderInfoResponse;
import com.howbuy.tms.high.orders.service.business.busiprocess.common.HzCreateOrderLogicProcess;
import com.howbuy.tms.high.orders.service.facade.search.queryHzSubscribeAmtInfo.QuerySubscribeAmtParam;
import com.howbuy.tms.high.orders.service.facade.search.queryHzSubscribeAmtInfo.SubscribeAmtInfoDto;
import com.howbuy.tms.high.orders.service.repository.DealOrderRepository;
import com.howbuy.tms.high.orders.service.repository.HighDealOrderDtlRepository;
import com.howbuy.tms.high.orders.service.service.acctbalancebaseinfo.AcctBalanceBaseInfoService;
import com.howbuy.tms.high.orders.service.service.highdealorderdtl.QueryHighDealOrderParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description:查询好臻下单需要的信息接口
 * @Author: yun.lu
 * Date: 2023/11/21 19:11
 */
@DubboService
@Service("queryHzBuyOrderInfoFacade")
@Slf4j
public class QueryHzBuyOrderInfoService implements QueryHzBuyOrderInfoFacade {
    @Autowired
    private HighDealOrderDtlRepository highDealOrderDtlRepository;
    @Autowired
    private DealOrderRepository dealOrderRepository;
    @Autowired
    private QueryCustBankCardOuterService queryCustBankCardOuterService;
    @Autowired
    private QueryHighProductOuterService queryHighProductOuterService;
    @Autowired
    private HzCreateOrderLogicProcess hzCreateOrderLogicProcess;
    @Autowired
    private QueryCustInfoForPrivateOuterService queryCustInfoForPrivateOuterService;
    @Autowired
    private AcctBalanceBaseInfoService acctBalanceBaseInfoService;
    @Autowired
    private QueryHbOneNoOuterService queryHbOneNoOuterService;


    /**
     * @api {DUBBO} com.howbuy.tms.high.orders.facade.search.queryHzBuyOrderInfo.QueryHzBuyOrderInfoFacade.execute(queryHzBuyOrderInfoRequest)
     * @apiName 好臻下单所需订单信息查询接口
     * @apiParam (queryHzBuyOrderInfoRequest) {com.howbuy.tms.high.orders.facade.search.queryHzBuyOrderInfo.QueryHzBuyOrderInfoRequest} queryHzBuyOrderInfoRequest
     * @apiParam (queryHzBuyOrderInfoRequest) {String} queryHzBuyOrderInfoRequest.fundCode 产品编码
     * @apiParam (queryHzBuyOrderInfoRequest) {String} [queryHzBuyOrderInfoRequest.txNo] 事项编号
     * @apiParam (queryHzBuyOrderInfoRequest) {String} [queryHzBuyOrderInfoRequest.userFlag] 用户标志
     * @apiParam (queryHzBuyOrderInfoRequest) {String} [queryHzBuyOrderInfoRequest.platformCode] 平台编码
     * @apiParam (queryHzBuyOrderInfoRequest) {String} [queryHzBuyOrderInfoRequest.accountType] 账户类型
     * @apiParam (queryHzBuyOrderInfoRequest) {String} [queryHzBuyOrderInfoRequest.orgCode] 机构编码
     * @apiParam (queryHzBuyOrderInfoRequest) {String} [queryHzBuyOrderInfoRequest.extUserCode] 对应扩展柜员号
     * @apiParam (queryHzBuyOrderInfoRequest) {String} [queryHzBuyOrderInfoRequest.remark] 备注
     * @apiParamExample {json} Request Example
     * {
     * "fundCode" : "*********",
     * }
     * @apiSuccess (subscribeAmt) {BigDecimal} subscribeAmt 认缴金额(只有分次call才有)
     * @apiSuccess (paidAmt) {BigDecimal} paidAmt 实缴金额
     * @apiSuccess (buyAmt) {BigDecimal} buyAmt 购买金额(购买金额只有非分次call才有)
     * @apiSuccess (buyAmtCanUpdate) {String} buyAmtCanUpdate 购买金额是否可以修改,1:可以;0:不可以
     * @apiSuccess (needCheckAmt) {String} needCheckAmt 是否需要校验限额,1:需要;0:不需要
     * @apiSuccess (subscribeAmtCanUpdate) {String} subscribeAmtCanUpdate 认缴金额是否可以修改,1:可以;0:不可以
     * @apiSuccess (isFirstPay) {String} isFirstPay 是否首次实缴,1:是;0:不是
     * @apiSuccess (bankCardCanUpdate) {String} bankCardCanUpdate 银行卡号是否可以修改,1:可以;0:不可以
     * @apiSuccess (feeRateMethod) {String} feeRateMethod 计算手续费方式,4:按认缴金额,5:按实缴金额
     * @apiSuccess (customerBankCardInfoList) {List} customerBankCardInfoList 银行卡号信息列表
     * @apiSuccessExample {json} Response Example
     * {
     * "subscribeAmt": 1000,
     * "paidAmt": 1000,
     * "buyAmt": 0,
     * "buyAmtCanUpdate" : "0",
     * "needCheckAmt" : "1",
     * "subscribeAmtCanUpdate" : "1",
     * "isFirstPay" : "1",
     * "bankCardCanUpdate" : "0",
     * "feeRateMethod" : "4",
     * "customerBankCardInfoList" : [{
     * "bankCardNo": "603903010355740613",
     * "bankName": "招商银行",
     * "bankCardName": "银行卡1"
     * }]
     * }
     */
    @Override
    public QueryHzBuyOrderInfoResponse execute(QueryHzBuyOrderInfoRequest request) {
        QueryHzBuyOrderInfoResponse response = new QueryHzBuyOrderInfoResponse();
        response.setReturnCode(ExceptionCodes.SUCCESS);
        response.setDescription(MessageSource.getMessageByCode(ExceptionCodes.SUCCESS));
        // 0.一账通校验
        checkRequest(request, response);
        if (!ExceptionCodes.SUCCESS.equals(response.getReturnCode())) {
            log.error("QueryHzSubsAmtInfoFacade-查询认缴金额信息失败,request={}", JSON.toJSONString(request));
            return response;
        }
        // 1.查询认缴金额信息
        Date queryDate = request.getQueryDate() != null ? request.getQueryDate() : new Date();
        QuerySubscribeAmtParam querySubscribeAmtParam = new QuerySubscribeAmtParam();
        querySubscribeAmtParam.setFundCode(request.getFundCode());
        querySubscribeAmtParam.setHbOneNo(request.getHbOneNo());
        querySubscribeAmtParam.setDisCode(DisCodeEnum.HZ.getCode());
        querySubscribeAmtParam.setTxAcctNo(request.getTxAcctNo());
        querySubscribeAmtParam.setQueryDate(queryDate);
        SubscribeAmtInfoDto subscribeAmtInfoDto = hzCreateOrderLogicProcess.buildSubscribeAndBuyAmtInfo(querySubscribeAmtParam);
        BeanUtils.copyProperties(subscribeAmtInfoDto, response);
        // 2.产品信息
        HighProductInfoBean highProductBaseModel = queryHighProductOuterService.getHighProductInfo(request.getFundCode());
        response.setPeDivideCallFlag(highProductBaseModel.getPeDivideCallFlag());
        // 3.当前生效的预约日历
        ProductAppointmentInfoBean productAppointmentInfoBean = queryHighProductOuterService.queryAppointmentInfoByAppointDateWithDeferPurchaseConfig(request.getHbOneNo(), request.getFundCode(), "0", highProductBaseModel.getShareClass(), request.getDisCode(), queryDate);
        // 4.在途订单
        QueryHighDealOrderParam queryHighDealOrderParam = new QueryHighDealOrderParam();
        queryHighDealOrderParam.setTxAcctNo(request.getTxAcctNo());
        queryHighDealOrderParam.setFundCodeList(Collections.singletonList(request.getFundCode()));
        queryHighDealOrderParam.setDisCodeList(Collections.singletonList(DisCodeEnum.HZ.getCode()));
        if (productAppointmentInfoBean != null) {
            queryHighDealOrderParam.setAppointId(productAppointmentInfoBean.getAppointId());
        }
        List<HighDealOrderDtlPo> onWayHighDealOrderDtlPoList = highDealOrderDtlRepository.getOnWayAgentDealDtlList(queryHighDealOrderParam);
        // 5.银行卡号信息
        buildCustomerBankCardInfo(response, request, onWayHighDealOrderDtlPoList);
        // 6.手续费计算方式
        setFeeRateMethod(request, response, highProductBaseModel);
        // 7.是否首次实缴
        String firstBuyFlag = acctBalanceBaseInfoService.getIsFirstBuy(request.getFundCode(), request.getTxAcctNo(), request.getDisCode());
        response.setIsFirstPay(firstBuyFlag);
        return response;
    }

    private void checkRequest(QueryHzBuyOrderInfoRequest request, QueryHzBuyOrderInfoResponse response) {
        if (StringUtils.isBlank(request.getHbOneNo()) && StringUtils.isBlank(request.getTxAcctNo())) {
            response.setReturnCode(ExceptionCodes.PARAMS_ERROR);
            response.setDescription("一账通与交易账号不能同时为空");
            return;
        }
        if (StringUtils.isEmpty(request.getTxAcctNo())) {
            String txAcctNo = queryHbOneNoOuterService.queryTxAcctNoByHbOneNo(request.getHbOneNo());
            request.setTxAcctNo(txAcctNo);
        }
        if (StringUtils.isEmpty(request.getHbOneNo())) {
            String hbOneNo = queryHbOneNoOuterService.queryHbOneNoByTxAcctNo(request.getTxAcctNo());
            request.setHbOneNo(hbOneNo);
        }
        if (StringUtils.isEmpty(request.getTxAcctNo())) {
            response.setReturnCode(ExceptionCodes.PARAMS_ERROR);
            response.setDescription("根据一账通,查不到交易账号");
        }

    }

    /**
     * 设置费率方式
     *
     * @param request              请求入参
     * @param response             返回结果
     * @param highProductBaseModel 产品基础信息
     */
    private void setFeeRateMethod(QueryHzBuyOrderInfoRequest request, QueryHzBuyOrderInfoResponse response, HighProductBaseInfoBean highProductBaseModel) {
        log.info("QueryHzBuyOrderInfoService-setFeeRateMethod,设置费率方式,request={}", JSON.toJSONString(request));
        QueryCustInfoForPrivateResult queryCustInfoForPrivateResult = queryCustInfoForPrivateOuterService.queryCustInfoForPrivate(request.getTxAcctNo(), DisCodeEnum.HZ.getCode());
        HighProductFeeRateBean highProductFeeRateBean = queryHighProductOuterService.getFundFeeRateByAmt(highProductBaseModel.getFundCode(),
                BusinessCodeEnum.SUBSCRIBE.getCode(), queryCustInfoForPrivateResult.getInvstType(), highProductBaseModel.getShareClass(), null);
        if (highProductFeeRateBean != null) {
            response.setFeeRateMethod(highProductFeeRateBean.getGetFeeRateMethod());
            return;
        }
        log.info("QueryHzBuyOrderInfoService-setFeeRateMethod,设置费率方式-查不到产品费率配置,request={}", JSON.toJSONString(request));
    }


    /**
     * 设置用户银行卡信息
     * 校验用户是否存在在途，且已付款的订单
     * 若是，则获取在途订单的关联银行卡信息，包括银行卡名称、卡号、支行名称；
     * 若否，则获取该客户好臻分销下已绑定的银行卡，包括银行卡名称、卡号、支行名称；
     */
    private void buildCustomerBankCardInfo(QueryHzBuyOrderInfoResponse queryHzBuyOrderInfoResponse,
                                           QueryHzBuyOrderInfoRequest request,
                                           List<HighDealOrderDtlPo> onWayHighDealOrderDtlPoList) {
        List<CustomerBankCardInfo> customerBankCardInfoList = new ArrayList<>();
        // 1.校验用户是否存在在途，且已付款的订单,如果存在:则获取在途订单的关联银行卡信息，包括银行卡名称、卡号、支行名称
        if (CollectionUtils.isNotEmpty(onWayHighDealOrderDtlPoList)) {
            List<String> dealNoList = onWayHighDealOrderDtlPoList.stream().map(HighDealOrderDtlPo::getDealNo).distinct().collect(Collectors.toList());
            List<DealOrderPo> dealOrderPoList = dealOrderRepository.queryByDealNoList(dealNoList);
            List<DealOrderPo> paySuccessDealOrderPoList = dealOrderPoList.stream().filter(x -> x.getPayStatus().equals(PayStatusEnum.PAY_SUC.getCode())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(paySuccessDealOrderPoList)) {
                DealOrderPo dealOrderPo = paySuccessDealOrderPoList.get(0);
                QueryCustBankCardContext queryCustBankCardContext = new QueryCustBankCardContext();
                queryCustBankCardContext.setTxAcctNo(dealOrderPo.getTxAcctNo());
                queryCustBankCardContext.setDisCode(dealOrderPo.getDisCode());
                queryCustBankCardContext.setCpAcctNo(dealOrderPo.getCpAcctNo());
                QueryCustBankCardResult queryCustBankCardResult = queryCustBankCardOuterService.queryCudtBankCard(queryCustBankCardContext);
                if (queryCustBankCardResult != null) {
                    CustomerBankCardInfo customerBankCardInfo = new CustomerBankCardInfo();
                    BeanUtils.copyProperties(queryCustBankCardResult, customerBankCardInfo);
                    customerBankCardInfoList.add(customerBankCardInfo);
                }
            }
        }
        queryHzBuyOrderInfoResponse.setBankCardCanUpdate(YesOrNoEnum.YES.getCode());
        if (CollectionUtils.isNotEmpty(customerBankCardInfoList)) {
            queryHzBuyOrderInfoResponse.setCustomerBankCardInfoList(customerBankCardInfoList);
            queryHzBuyOrderInfoResponse.setBankCardCanUpdate(YesOrNoEnum.NO.getCode());
            return;
        }
        // 2.如果没有在途+已付款或者在途没有查询到银行卡信息,则,查询用户+好臻分销下绑定的银行卡信息
        QueryCustomerBankCardParam queryCustomerBankCardParam = new QueryCustomerBankCardParam();
        queryCustomerBankCardParam.setOutletCode(request.getOutletCode());
        queryCustomerBankCardParam.setDisCode(request.getDisCode());
        queryCustomerBankCardParam.setTxAcctNo(request.getTxAcctNo());
        List<CustBankInfoBean> bankInfoBeanList = queryCustBankCardOuterService.queryBindCustomerBankCardList(queryCustomerBankCardParam);
        if (CollectionUtils.isNotEmpty(bankInfoBeanList)) {
            for (CustBankInfoBean custBankInfoBean : bankInfoBeanList) {
                CustomerBankCardInfo customerBankCardInfo = new CustomerBankCardInfo();
                BeanUtils.copyProperties(custBankInfoBean, customerBankCardInfo);
                customerBankCardInfoList.add(customerBankCardInfo);
            }
        }
        queryHzBuyOrderInfoResponse.setCustomerBankCardInfoList(customerBankCardInfoList);
    }


}
