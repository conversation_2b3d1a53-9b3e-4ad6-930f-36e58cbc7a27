package com.howbuy.tms.high.orders.service.service.acctbalancebaseinfo.bean;

import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import com.howbuy.tms.common.enums.busi.YesOrNoEnum;
import com.howbuy.tms.common.validate.MyValidation;
import com.howbuy.tms.high.orders.facade.common.BaseDto;
import lombok.Data;

import java.util.List;

/**
 * @Description:查询持仓入参
 * @Author: yun.lu
 * Date: 2025/8/28 14:06
 */
@Data
public class QueryBalanceParam extends BaseDto {
    /**
     * 交易账号
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "交易账号", isRequired = true)
    private String txAcctNo;
    /**
     * 一账通
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "一账通", isRequired = true)
    private String hboneNo;
    /**
     * 基金代码
     */
    private String fundCode;
    /**
     * 分销渠道
     */
    private List<String> disCodeList;

}
