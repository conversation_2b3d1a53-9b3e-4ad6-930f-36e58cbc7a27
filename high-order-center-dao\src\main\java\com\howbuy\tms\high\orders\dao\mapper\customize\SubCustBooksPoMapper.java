package com.howbuy.tms.high.orders.dao.mapper.customize;

import com.howbuy.tms.high.orders.dao.mapper.SubCustBooksPoAutoMapper;
import com.howbuy.tms.high.orders.dao.po.SubCustBooksPo;

import java.util.List;

import com.howbuy.tms.high.orders.dao.vo.ConfirmBalanceVo;
import com.howbuy.tms.high.orders.dao.vo.SubCustBooksVo;
import org.apache.ibatis.annotations.Param;

/**
 * 去O
 */
public interface SubCustBooksPoMapper extends SubCustBooksPoAutoMapper {
    
    /**
     * selectSubCustBooksByOpenRedeDt:(查询子账本表(开放赎回日期维度))
     * 
     * @param disCodeList
     * @param txAcctNo
     * @param fundCode
     * @param cpAcctNo
     * @param openRedeDt
     * @return
     * <AUTHOR>
     * @date 2017年7月26日 下午8:54:53
     */
    List<SubCustBooksPo> selectSubCustBooksByOpenRedeDt(@Param("disCodeList") List<String> disCodeList, @Param("txAcctNo") String txAcctNo, @Param("fundCode") String fundCode,
            @Param("cpAcctNo") String cpAcctNo, @Param("openRedeDt") String openRedeDt);
    
    /**
     * 
     * selectSubCustBooksByProductCode:(查询子账本表)
     * @param disCodeList
     * @param txAcctNo
     * @param fundCode
     * @param openRedeDt
     * @return
     * <AUTHOR>
     * @date 2018年7月5日 下午2:56:51
     */
    List<SubCustBooksPo> selectSubCustBooks(@Param("disCodeList") List<String> disCodeList, @Param("txAcctNo") String txAcctNo, @Param("fundCode") String fundCode,  @Param("openRedeDt") String openRedeDt,@Param("balanceStatus") String balanceStatus);
    
    /**
     * 查询专户持仓明细
     * @param disCode
     * @param txAcctNo
     * @param productCode
     * @param cpAcctNo
     * @return
     */
    List<SubCustBooksPo> selectZhBalanceDtl(@Param("disCode") String disCode, @Param("txAcctNo") String txAcctNo, 
            @Param("productCode") String productCode, @Param("cpAcctNo") String cpAcctNo);
     
    /**
     * 查询客户子账本
     * @param disCodeList
     * @param txAcctNo
     * @param fundCode
     * @param cpAcctNo
     * @return
     */
    List<SubCustBooksPo> selectSubCustBooksByTxAcctNo(@Param("disCodeList") List<String> disCodeList, @Param("txAcctNo") String txAcctNo,
            @Param("fundCode") String fundCode, @Param("cpAcctNo") String cpAcctNo);

    /**
     * 查询子账本,按照确认日期日期汇总
     * @param disCodeList 分销渠道
     * @param txAcctNo 交易账号
     * @param fundCode 产品编码
     * @param balanceStatus  持仓状态,0:不持仓,1:持仓,,2:全部
     * @return
     */
    List<SubCustBooksPo> selectSubCustBookSumByAckDt(@Param("list") List<String> disCodeList, @Param("txAcctNo") String txAcctNo, @Param("fundCode") String fundCode, @Param("balanceStatus")String balanceStatus);
    /**
     * 查询没有子账本的专户持仓明细
     * @param disCodeList
     * @param txAcctNo
     * @param productCode
     * @param cpAcctNo
     * @return
     */
    List<SubCustBooksPo> selectZhBalanceDtlWithOutSubBook(@Param("disCodeList") List<String> disCodeList, @Param("txAcctNo") String txAcctNo, @Param("productCode") String productCode, @Param("cpAcctNo") String cpAcctNo);

    List<SubCustBooksVo> selectAcctSubCustBooks(@Param("txAcctNo")String txAcctNo, @Param("fundCode")String fundCode);

    List<ConfirmBalanceVo> selectAcctSubCustBooksByFundCodeList(@Param("txAcctNo")String txAcctNo, @Param("fundCodeList")List<String> fundCodeList);

    List<String> queryAllBalanceVolFund();
}