/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.orders.facade.search.queryfundliquidation;

import com.howbuy.tms.high.orders.facade.common.OrderSearchBaseRequest;
import com.howbuy.tms.high.orders.facade.enums.TxCodes;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * @description: 清仓产品列表查询请求参数
 * <AUTHOR>
 * @date 2025/9/4 20:00
 * @since JDK 1.8
 */
@Setter
@Getter
public class QueryFundLiquidationListRequest extends OrderSearchBaseRequest implements Serializable {


    private static final long serialVersionUID = -960656898702661369L;

    /**
     * 构造函数，设置默认交易代码
     */
    public QueryFundLiquidationListRequest() {
        setTxCode(TxCodes.QUERY_FUND_LIQUIDATION_LIST);
    }


    /**
     * 分销渠道编码
     */
    private List<String> disCodeList;

    /**
     * 是否查询海外 1 是 0 否
     */
    private String searchOverseas;
}
