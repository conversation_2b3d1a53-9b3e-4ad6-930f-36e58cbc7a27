package com.howbuy.tms.high.orders.dao.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description:待回款订单明细
 * @Author: yun.lu
 * Date: 2025/8/21 18:30
 */
@Data
public class RefundDealOrderVo {
    /**
     * 订单号
     */
    private String dealNo;
    /**
     * 是否合并上报,1:是,0:不是
     */
    private String mergeSubmitFlag;
    /**
     * 主订单号
     */
    private String mainDealOrderNo;
    /**
     * 交易账号
     */
    private String txAcctNo;
    /**
     * 基金代码
     */
    private String fundCode;
    /**
     * ta信息
     */
    private String taCode;
    /**
     * 中台业务代码
     */
    private String mBusiCode;
    /**
     * 待回款金额
     */
    private BigDecimal refundAmt;

    /**
     * 确认日期
     */
    private String ackDt;

    /**
     * 赎回去向,0-银行卡,1-储蓄罐
     */
    private String redeemDirection;


}
