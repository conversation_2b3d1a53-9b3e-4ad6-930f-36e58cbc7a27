/**
 * Copyright (c) 2020, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.tms.high.orders.facade.search.querysupsignagreementlist;

import com.howbuy.tms.high.orders.facade.enums.TxCodes ;
import com.howbuy.tms.high.orders.facade.common.OrderSearchBaseRequest;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 查询待补签协议列表
 * <AUTHOR>
 * @date 2020/12/9 15:23
 * @since JDK 1.8
 */
@Getter
@Setter
public class QuerySupSignAgreementListRequest extends OrderSearchBaseRequest {

    private static final long serialVersionUID = 1048308943639331903L;
    /**
     * 是否好臻数据授权
     */
    private String isDataAuth;

    /**
     * 是否过滤好臻数据,1:过滤,0:不过滤
     */
    private String filterHz;


    /**
     * @api {DUBBO} com.howbuy.tms.high.orders.facade.search.querysupsignagreementlist.QuerySupSignAgreementListFacade.execute() 查询待补签协议列表
     * @apiGroup high-order-center
     * @apiDescription 查询待补签协议列表
     *
     * @apiUse orderBaseRequest
     * @apiUse orderSearchBaseRequest
     *
     * @apiParamExample {json} Request Example
     * dubbo com.howbuy.tms.high.orders.facade.search.querysupsignagreementlist.QuerySupSignAgreementListRequest
     *
     * @apiUse orderBaseResponse
     * @apiUse orderSearchBaseResponse
     *
     * @apiSuccess {String} fundList 需补签协议的基金列表
     * @apiSuccess (fundList) {String} fundCode 基金代码
     * @apiSuccess (fundList) {String} fundName 基金名称
     * @apiSuccess (fundList) {String} taCode ta代码
     * @apiSuccess (fundList) {String} mobile 手机号，bankCode不为空时为银行预留手机，否则为一账通绑定手机
     * @apiSuccess (fundList) {String} bankCode 银行编号
     * @apiSuccess (fundList) {String} custBankId 客户银行卡编号
     * @apiSuccess (fundList) {Object} agreementList 待补签协议列表
     * @apiSuccess (agreementList) {String} agreementCode 协议代码
     * @apiSuccess (agreementList) {String} agreementName 协议名称
     * @apiSuccess (agreementList) {String} signEndDtm 签署截止时间
     * @apiSuccess (agreementList) {String} signReason 补签原因
     * @apiSuccessExample {json} Response Example
     * dubbo com.howbuy.tms.high.orders.facade.search.querysupsignagreementlist.QuerySupSignAgreementListResponse
     *
     */
    public QuerySupSignAgreementListRequest() {
        setTxCode(TxCodes.QUERY_SUP_SIGN_AGREEMENT_LIST);
    }

}