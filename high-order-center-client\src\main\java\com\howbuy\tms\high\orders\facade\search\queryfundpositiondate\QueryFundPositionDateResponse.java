/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT Co., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.orders.facade.search.queryfundpositiondate;

import com.howbuy.tms.high.orders.facade.common.OrderSearchBaseResponse;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 基金产品持仓/清仓日期查询响应结果
 * @date 2025/9/4 22:05
 * @since JDK 1.8
 */
@Setter
@Getter
public class QueryFundPositionDateResponse extends OrderSearchBaseResponse implements Serializable {


    private static final long serialVersionUID = 1902808127503693427L;
    /**
     * 持仓日期信息列表
     */
    private List<FundDateInfo> holdPositionDateList;

    /**
     * 清仓日期信息列表
     */
    private List<FundDateInfo> liquidationPositionDateList;

    /**
     * 基金日期信息
     */
    @Setter
    @Getter
    public static class FundDateInfo implements Serializable {


        private static final long serialVersionUID = 7189092365979744221L;
        /**
         * 基金编码
         */
        private String fundCode;

        /**
         * 首次持仓日期
         * 格式：yyyyMMdd
         */
        private String firstHoldDate;

        /**
         * 清仓日期
         * 格式：yyyyMMdd
         */
        private String liquidationDate;

        /**
         * 首次持有的订单
         */
        private String firstHoldDealNo;

        /**
         * 清仓的订单号
         */
        private String liquidationDealNo;
    }
}
