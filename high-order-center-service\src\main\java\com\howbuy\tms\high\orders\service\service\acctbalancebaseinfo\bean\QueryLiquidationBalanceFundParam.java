package com.howbuy.tms.high.orders.service.service.acctbalancebaseinfo.bean;

import com.howbuy.tms.high.orders.facade.common.BaseDto;
import lombok.Data;

import java.util.List;

/**
 * @Description: 查询清仓持仓请求参数
 * @Author: AI Assistant  
 * @Date: 2025/09/12
 */
@Data
public class QueryLiquidationBalanceFundParam extends BaseDto {
    
    /**
     * 交易账号
     */
    private String txAcctNo;
    
    /**
     * 基金编码
     */
    private String fundCode;
    
    /**
     * 分销渠道列表
     */
    private List<String> disCodeList;
    
    /**
     * 一账通账号(从交易账号转换获取)
     */
    private String hbOneNo;
}