package com.howbuy.tms.high.orders.service.service.ownershiprightorder;

import com.howbuy.tms.common.enums.busi.OwnershipTransferIdentityEnum;
import lombok.Data;
import org.apache.dubbo.common.utils.CollectionUtils;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Description:股权商品维度的订单信息
 * @Author: yun.lu
 * Date: 2023/5/26 9:37
 */
@Data
public class ProductOrderInfo implements OwnershipRightOrder {

    private String productCode;

    private List<OwnershipOrderInfo> orderInfoList;


    /**
     * 获取成本
     *
     * @return 成本
     */
    @Override
    public BigDecimal getNetBuyAmount() {
        if (CollectionUtils.isNotEmpty(orderInfoList)) {
            BigDecimal netBuyAmount = orderInfoList.stream().map(OwnershipOrderInfo::getNetBuyAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            // 如果总成本小于0,就取值为0
            return BigDecimal.ZERO.compareTo(netBuyAmount) > 0 ? BigDecimal.ZERO : netBuyAmount;
        }
        return BigDecimal.ZERO;
    }

    /**
     * 股权转让标识
     * 若仅存在受让：【股权转让标识】=“受让”
     * 若仅存在转让：【股权装让标识】=“转让”
     * 若同时存在受让及转让：【股权转让标识】=“受让和转让”
     * 若上述交易记录均不存在，则【股权转让标识】=为“无
     */
    @Override
    public String getOwnershipTransferIdentity() {
        // 1.判空
        if (CollectionUtils.isEmpty(orderInfoList)) {
            return null;
        }
        // 2.遍历判断转让标识
        boolean hasTransferIn = false;
        boolean hasTransferOut = false;
        for (OwnershipOrderInfo ownershipOrderInfo : orderInfoList) {
            if (OwnershipTransferIdentityEnum.TRANSFER_IN.getType().equals(ownershipOrderInfo.getOwnershipTransferIdentity())) {
                hasTransferIn = true;
            } else if (OwnershipTransferIdentityEnum.TRANSFER_OUT.getType().equals(ownershipOrderInfo.getOwnershipTransferIdentity())) {
                hasTransferOut = true;
            }
        }
        // 3.根据结果返回标识结果
        if (hasTransferIn && hasTransferOut) {
            return OwnershipTransferIdentityEnum.TRANSFER_OUT_AND_IN.getType();
        } else if (hasTransferIn) {
            return OwnershipTransferIdentityEnum.TRANSFER_IN.getType();
        } else if (hasTransferOut) {
            return OwnershipTransferIdentityEnum.TRANSFER_OUT.getType();
        }
        return OwnershipTransferIdentityEnum.NO_TRANSFER.getType();
    }


}
