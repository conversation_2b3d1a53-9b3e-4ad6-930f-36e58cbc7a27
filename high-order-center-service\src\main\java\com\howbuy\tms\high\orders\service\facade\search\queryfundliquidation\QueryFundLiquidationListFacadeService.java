/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.orders.service.facade.search.queryfundliquidation;

import com.alibaba.fastjson2.JSON;
import com.howbuy.tms.common.constant.ExceptionCodes;
import com.howbuy.tms.common.enums.busi.DisCodeEnum;
import com.howbuy.tms.common.enums.busi.YesOrNoEnum;
import com.howbuy.tms.common.exception.BusinessException;
import com.howbuy.tms.common.outerservice.asset.response.QueryFundLiquidationListResponseDTO;
import com.howbuy.tms.common.outerservice.cc.asset.fundLiquidation.FundLiquidationOuterService;
import com.howbuy.tms.common.outerservice.cc.center.queryhboneno.QueryHbOneNoOuterService;
import com.howbuy.tms.common.outerservice.cc.request.QueryFundLiquidationListRequestDTO;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.QueryHighProductOuterService;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductDBInfoBean;
import com.howbuy.tms.high.orders.facade.search.queryfundliquidation.QueryFundLiquidationListFacade;
import com.howbuy.tms.high.orders.facade.search.queryfundliquidation.QueryFundLiquidationListRequest;
import com.howbuy.tms.high.orders.facade.search.queryfundliquidation.QueryFundLiquidationListResponse;
import com.howbuy.tms.high.orders.service.business.queryliquidation.QueryLiquidationService;
import com.howbuy.tms.high.orders.service.common.utils.TradeUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description: 清仓产品列表查询服务实现
 * @date 2025/9/4 20:15
 * @since JDK 1.8
 */
@DubboService
@Service("queryFundLiquidationListFacade")
@Slf4j
public class QueryFundLiquidationListFacadeService implements QueryFundLiquidationListFacade {

    @Resource
    private FundLiquidationOuterService fundLiquidationOuterService;

    @Resource
    private QueryHighProductOuterService queryHighProductOuterService;

    @Resource
    private QueryLiquidationService queryLiquidationService;

    @Resource
    private QueryHbOneNoOuterService queryHbOneNoOuterService;

    /**
     * 渠道编码常量
     */
    private static final String CHANNEL_HOWBUY = "1";      // 好买分销
    private static final String CHANNEL_HAOZHEN = "2";     // 好臻分销
    private static final String CHANNEL_OVERSEAS = "3";    // 好买香港分销

    private static final String Yes = "Y";

    @Override
    public QueryFundLiquidationListResponse execute(QueryFundLiquidationListRequest request) {
        log.info("查询清仓产品列表开始, request: {}", request);

        QueryFundLiquidationListResponse response = new QueryFundLiquidationListResponse();

        try {
            // 参数校验
            if (StringUtils.isBlank(request.getHbOneNo())) {
                throw new BusinessException(ExceptionCodes.PARAMS_IS_EMPTY, "参数错误,一账通号不能为空");
            }
            // 获取交易账号
            String txAcctNo = queryHbOneNoOuterService.queryTxAcctNoByHbOneNo(request.getHbOneNo());

            // 获取不同渠道的清仓产品基金代码
            List<String> fundCodeList = getClearanceFundCodeList(request.getDisCodeList(), request.getSearchOverseas(), request.getHbOneNo(), txAcctNo);

            if (CollectionUtils.isEmpty(fundCodeList)) {
                log.info("未找到清仓产品基金代码, params: {}", JSON.toJSONString(request));
                response.setFundLiquidationList(new ArrayList<>());
                return response;
            }

            // 调用资产中心接口查询清仓产品信息
            QueryFundLiquidationListRequestDTO assetRequest = new QueryFundLiquidationListRequestDTO();
            assetRequest.setHboneNo(request.getHbOneNo());
            assetRequest.setFundCodes(fundCodeList);
            assetRequest.setOnlyClearFlag(Yes); // 仅查询清仓产品

            QueryFundLiquidationListResponseDTO assetResponse = fundLiquidationOuterService.queryFundLiquidationList(assetRequest);

            if (assetResponse == null || CollectionUtils.isEmpty(assetResponse.getProductList())) {
                log.info("资产中心返回清仓产品列表为空, hbOneNo: {}", request.getHbOneNo());
                response.setFundLiquidationList(new ArrayList<>());
                return response;
            }

            // 批量查询基金信息
            List<String> responseFundCodes = assetResponse.getProductList().stream()
                    .map(QueryFundLiquidationListResponseDTO.LiquidationProduct::getFundCode)
                    .collect(Collectors.toList());

            Map<String, HighProductDBInfoBean> highProductDBInfoMap = queryHighProductOuterService.getHighProductDBInfoMap(responseFundCodes);

            // 封装返回结果
            List<QueryFundLiquidationListResponse.FundLiquidationInfo> resultList = buildFundLiquidationList(
                    assetResponse.getProductList(), highProductDBInfoMap);

            response.setFundLiquidationList(resultList);

            log.info("查询清仓产品列表成功, hbOneNo: {}, 返回数量: {}", request.getHbOneNo(), resultList.size());

        } catch (Exception e) {
            log.error("查询清仓产品列表异常, request: {}", request, e);
        }

        return response;
    }

    /**
     * 获取不同渠道的清仓产品基金代码
     */
    private List<String> getClearanceFundCodeList(List<String> disCodeList, String searchOverseas, String hbOneNo, String txAcctNo) {
        List<String> allFundCodes = queryLiquidationService.queryAllChannelLiquidationFundCodeList(hbOneNo, txAcctNo, disCodeList, searchOverseas);
        return allFundCodes.stream().distinct().collect(Collectors.toList());
    }


    /**
     * 根据渠道获取清仓产品基金代码
     */
    private List<String> getClearanceFundCodesByChannel(String channelCode, String hbOneNo, String txAcctNo) {
        switch (channelCode) {
            case CHANNEL_HOWBUY:
                // 好买分销渠道
                log.info("好买分销渠道清仓产品查询待实现, channelCode: {}, hbOneNo: {}", channelCode, hbOneNo);
                // 2. 获取清仓的代码信息
                return queryLiquidationService.queryLiquidationFundCodes(txAcctNo, hbOneNo, Collections.singletonList(DisCodeEnum.HM.getCode()));

            case CHANNEL_HAOZHEN:
                log.info("好臻分销渠道清仓产品查询待实现, channelCode: {}, hbOneNo: {}", channelCode, hbOneNo);
                // 2. 获取清仓的代码信息
                return queryLiquidationService.queryLiquidationFundCodes(txAcctNo, hbOneNo, Collections.singletonList(DisCodeEnum.HZ.getCode()));
            case CHANNEL_OVERSEAS:
                // 海外分销渠道：查询海外的用户清仓基金编码接口
                return queryLiquidationService.getHkClearanceFundCodeList(hbOneNo);

            default:
                log.warn("不支持的渠道编码: {}", channelCode);
                return new ArrayList<>();
        }
    }


    /**
     * 构建清仓产品列表
     */
    private List<QueryFundLiquidationListResponse.FundLiquidationInfo> buildFundLiquidationList(
            List<QueryFundLiquidationListResponseDTO.LiquidationProduct> productList,
            Map<String, HighProductDBInfoBean> productInfoMap) {

        List<QueryFundLiquidationListResponse.FundLiquidationInfo> resultList = new ArrayList<>();

        for (QueryFundLiquidationListResponseDTO.LiquidationProduct product : productList) {
            QueryFundLiquidationListResponse.FundLiquidationInfo info = new QueryFundLiquidationListResponse.FundLiquidationInfo();
            // 基金代码
            info.setFundCode(product.getFundCode());
            // 基金名称
            HighProductDBInfoBean productInfo = productInfoMap.get(product.getFundCode());
            if (null == productInfo) {
                log.warn("buildFundLiquidationList>>>没有找到基金信息, 基金代码: {}", product.getFundCode());
                continue;
            }
            info.setFundName(productInfo.getFundAttr());
            // 产品收益：净值型产品展示累计收益，非净值型产品展示累计回款
            BigDecimal assetValue = getProductAssetValue(product, productInfo);
            info.setProductAsset(assetValue);
            // 是否是净值型产品,1:是,0:否
            if (TradeUtils.isNavTypeFund(productInfo.getStandardFixedIncomeFlag(), productInfo.getFundSubType(), productInfo.getFundCode())) {
                info.setIsNavProduct(YesOrNoEnum.YES.getCode());
            } else {
                info.setIsNavProduct(YesOrNoEnum.NO.getCode());
            }
            // 累计持有天数
            info.setTotalDays(product.getTotalHoldDays() == null ? null : product.getTotalHoldDays().toString());

            // 单位默认是元
            info.setUnit("元");

            resultList.add(info);
        }

        return resultList;
    }

    /**
     * 获取产品收益值
     * 净值型产品展示累计收益，非净值型产品展示累计回款
     */
    private BigDecimal getProductAssetValue(QueryFundLiquidationListResponseDTO.LiquidationProduct product,
                                            HighProductDBInfoBean productInfo) {
        // 判断是否为净值型产品
        boolean isNetValueProduct = TradeUtils.isNavTypeFund(productInfo.getStandardFixedIncomeFlag(), productInfo.getFundSubType(), productInfo.getFundCode());

        if (isNetValueProduct) {
            // 净值型产品展示累计收益
            return product.getAccumIncomeExFee();
        } else {
            // 非净值型产品展示累计回款
            return product.getAccumCollection();
        }
    }


}
