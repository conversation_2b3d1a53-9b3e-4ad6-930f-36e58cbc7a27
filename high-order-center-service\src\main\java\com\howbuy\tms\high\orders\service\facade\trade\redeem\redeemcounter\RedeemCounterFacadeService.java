/**
 * Copyright (c) 2017, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.orders.service.facade.trade.redeem.redeemcounter;

import com.howbuy.tms.common.enums.database.InvstTypeEnum;
import com.howbuy.tms.common.idempotent.Idempotent;
import com.howbuy.tms.high.orders.facade.trade.redeem.redeemcounter.RedeemCounterFacade;
import com.howbuy.tms.high.orders.facade.trade.redeem.redeemcounter.RedeemCounterRequest;
import com.howbuy.tms.high.orders.facade.trade.redeem.redeemcounter.RedeemCounterResponse;
import com.howbuy.tms.high.orders.service.business.busiprocess.RedeemCounterBusiProcess;
import com.howbuy.tms.high.orders.service.business.busiprocess.common.AbstractDisCodeCreateOrderLogicProcess;
import com.howbuy.tms.high.orders.service.business.factory.createOrder.DisCodeCreateOrderLogicFactory;
import com.howbuy.tms.high.orders.service.business.ordercreater.OrderCreateBean;
import com.howbuy.tms.high.orders.service.business.ordercreater.OrderCreateContext;
import com.howbuy.tms.high.orders.service.facade.trade.redeem.BaseRedeemService;
import com.howbuy.tms.high.orders.service.repository.DealCompositeRepository;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Description:柜台基金赎回接口实现
 *
 * @reason:
 * <AUTHOR>
 * @date 2017年3月17日 下午1:42:32
 * @since JDK 1.7
 */
@DubboService
@Service("redeemCounterFacade")
public class RedeemCounterFacadeService extends BaseRedeemService implements RedeemCounterFacade {

    @Autowired
    private DisCodeCreateOrderLogicFactory disCodeCreateOrderLogicFactory;

    @Autowired
    private DealCompositeRepository dealCompositeRepository;

    @Autowired
    private RedeemCounterBusiProcess redeemCounterBusiProcess;

    @Override
    @Idempotent
    public RedeemCounterResponse execute(RedeemCounterRequest request) {
        // 验证参数
        validateRedeemListSize(request);

        OrderCreateContext context = new OrderCreateContext();
        combineRequestInfo(request, context);
        // 柜台特殊字段
        context.getRedeemList().forEach(redeemBean -> {
            redeemBean.setUnusualTransType(request.getUnusualTransType());
            redeemBean.setRepurchaseProtocolNo(request.getRepurchaseProtocolNo());
        });

        // 业务处理
        redeemCounterBusiProcess.process(request, context);

        // 创建订单
        AbstractDisCodeCreateOrderLogicProcess disCodeCreateOrderCheckLogicProcess = disCodeCreateOrderLogicFactory.getDisCodeCreateOrderCheckLogicProcess(request.getDisCode());
        List<OrderCreateBean> orderList = disCodeCreateOrderCheckLogicProcess.createMergeOrders(context);

        // 保存订单相关信息
        dealCompositeRepository.saveMergeOrders(orderList);

        // 发送订单实时信息
        sendDealActualMsg(orderList.get(0));

        // 发送APP站内信消息
        sendAppInBoxCreateOrderMsg(orderList.get(0));

        // 发送柜台机构下单消息
        String invstType = orderList.get(0).getOrderPo().getInvstType();
        if (InvstTypeEnum.INST.getCode().equals(invstType) || InvstTypeEnum.PRODUCT.getCode().equals(invstType)) {
            orderList.forEach(order -> redeemCounterBusiProcess.sendCounterOrderMessage(order.getOrderPo().getExternalDealNo(),
                    order.getOrderPo().getDealNo(), request.getTxAcctNo(), request.getDisCode()));
        }

        // 创建返回对象
        RedeemCounterResponse response = new RedeemCounterResponse();
        return createResponse(request.getRedeemCapitalFlag(), context, orderList.get(0), response);
    }

}
