package com.howbuy.tms.high.orders.service.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

/**
 * @Description:通用值配置,基于配置中心配置
 * @Author: yun.lu
 * Date: 2024/12/11 19:08
 */
@Configuration
@RefreshScope
@Component
public class CommonValueConfig {

    /**
     * 危机产品
     */
    @Value("${not.count.assert.fund}")
    private String notCountAssertFund;

    /**
     * 新方程管理人code
     */
    @Value("${xin.fang.cheng.fund.man.code}")
    private String xinFangeChengFundManCode;

    public String getNotCountAssertFund() {
        return notCountAssertFund;
    }

    public void setNotCountAssertFund(String notCountAssertFund) {
        this.notCountAssertFund = notCountAssertFund;
    }

    public String getXinFangeChengFundManCode() {
        return xinFangeChengFundManCode;
    }

    public void setXinFangeChengFundManCode(String xinFangeChengFundManCode) {
        this.xinFangeChengFundManCode = xinFangeChengFundManCode;
    }
}
