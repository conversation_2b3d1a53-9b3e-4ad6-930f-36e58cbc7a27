/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.orders.service.common.enums.queryfundliquidation;

/**
 * @description: 数据渠道枚举
 * <AUTHOR>
 * @date 2025/9/11
 * @since JDK 1.8
 */
public enum DataChannelEnum {

    /**
     * 好买渠道
     */
    HOWBUY("1", "好买"),

    /**
     * 好甄渠道
     */
    HAOZHEN("2", "好甄"),

    /**
     * 海外渠道
     */
    OVERSEAS("3", "海外");

    /**
     * 渠道代码
     */
    private final String code;

    /**
     * 渠道名称
     */
    private final String name;

    DataChannelEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    /**
     * 根据代码获取枚举
     *
     * @param code 渠道代码
     * @return 数据渠道枚举
     */
    public static DataChannelEnum fromCode(String code) {
        for (DataChannelEnum channel : DataChannelEnum.values()) {
            if (channel.getCode().equals(code)) {
                return channel;
            }
        }
        return null;
    }

    /**
     * 检查代码是否有效
     *
     * @param code 渠道代码
     * @return 是否有效
     */
    public static boolean isValidCode(String code) {
        return fromCode(code) != null;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}