package com.howbuy.tms.high.orders.service.business.factory.fundBuyStatus;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description:产品购买状态逻辑处理工厂类
 * @Author: yun.lu
 * Date: 2023/10/30 17:32
 */
@Service
public class FundBuyStatusPageSourceFactory {


    @Autowired
    private List<AbstractFundBuyStatusChannelSourceService> abstractFundBuyStatusLogicServiceList;
    @Autowired
    private CommonBuyFundStatusChannelResourceService commonBuyFundStatusChannelResourceService;


    public AbstractFundBuyStatusChannelSourceService getFundBuyStatusLogicService(String disCode,String channelSource) {
        if(StringUtils.isBlank(channelSource)){
            // 默认使用好买处理类
            return commonBuyFundStatusChannelResourceService;
        }
        for (AbstractFundBuyStatusChannelSourceService fundBuyStatusLogicService : abstractFundBuyStatusLogicServiceList) {
            if (fundBuyStatusLogicService.getChannelSource(disCode,channelSource)) {
                return fundBuyStatusLogicService;
            }
        }
        return commonBuyFundStatusChannelResourceService;
    }
}
