/**
 * Copyright (c) 2021, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.tms.high.orders.service.facade.trade.subsorpur.subsorpurmergeweb;

import com.howbuy.tms.common.idempotent.Idempotent;
import com.howbuy.tms.high.orders.dao.po.PaymentOrderPo;
import com.howbuy.tms.high.orders.facade.trade.subsorpur.subsorpurmergeweb.SubsOrPurMergeWebFacade;
import com.howbuy.tms.high.orders.facade.trade.subsorpur.subsorpurmergeweb.SubsOrPurMergeWebRequest;
import com.howbuy.tms.high.orders.facade.trade.subsorpur.subsorpurmergeweb.SubsOrPurMergeWebResponse;
import com.howbuy.tms.high.orders.service.business.busiprocess.CustEsignatureBusiProcess;
import com.howbuy.tms.high.orders.service.business.busiprocess.SubsOrPurWebBusiProcess;
import com.howbuy.tms.high.orders.service.business.busiprocess.common.AbstractDisCodeCreateOrderLogicProcess;
import com.howbuy.tms.high.orders.service.business.factory.createOrder.DisCodeCreateOrderLogicFactory;
import com.howbuy.tms.high.orders.service.business.ordercreater.OrderCreateBean;
import com.howbuy.tms.high.orders.service.business.ordercreater.OrderCreateContext;
import com.howbuy.tms.high.orders.service.facade.trade.subsorpur.BaseSubsOrPurService;
import com.howbuy.tms.high.orders.service.repository.DealCompositeRepository;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 网上合并支付认申购接口
 *
 * <AUTHOR>
 * @date 2021/5/25 17:04
 * @since JDK 1.8
 */
@DubboService
@Service("subsOrPurMergeWebFacade")
public class SubsOrPurMergeWebFacadeService extends BaseSubsOrPurService implements SubsOrPurMergeWebFacade {

    @Autowired
    private DisCodeCreateOrderLogicFactory disCodeCreateOrderLogicFactory;

    @Autowired
    private DealCompositeRepository dealCompositeRepository;

    @Autowired
    private SubsOrPurWebBusiProcess subsOrPurWebBusiProcess;

    @Autowired
    private CustEsignatureBusiProcess custEsignatureBusiProcess;

    @Override
    @Idempotent
    public SubsOrPurMergeWebResponse execute(SubsOrPurMergeWebRequest request) {
        OrderCreateContext context = new OrderCreateContext();
        // 1.检查一些除机构外必须的参数
        checkParamsExceptInst(request);
        // 2.检查参数PayList
        validatePayList(request);

        // 3.整合请求参数
        combineRequestInfo(request, context);

        // 4.业务处理
        subsOrPurWebBusiProcess.process(request, context, request.getTxPwd());

        // 5.创建订单
        AbstractDisCodeCreateOrderLogicProcess disCodeCreateOrderCheckLogicProcess = disCodeCreateOrderLogicFactory.getDisCodeCreateOrderCheckLogicProcess(request.getDisCode());
        List<OrderCreateBean> orderList = disCodeCreateOrderCheckLogicProcess.createMergeOrders(context);

        // 6.合并支付订单校验
        subMergeValidate(orderList);

        // 7.保存订单相关信息
        dealCompositeRepository.saveMergeOrders(orderList);

        // 8.发送支付消息
        List<PaymentOrderPo> paymentList = new ArrayList<>(orderList.size());
        orderList.forEach(order -> paymentList.add(order.getPaymentOrderPo()));
        sendPayMessage(paymentList);

        // 9.发送订单实时信息
        sendDealActualMsg(orderList.get(0));
        // 发送app站内信消息
        sendAppInBoxCreateOrderMsg(orderList.get(0));

        // 10.电子签名处理
        custEsignatureBusiProcess.process(context, orderList.get(0).getOrderPo());

        // 11.通知生成投资经历证明
        sendCustInvestFileMsg(request.getTxAcctNo());

        // 12.创建返回对象
        SubsOrPurMergeWebResponse response = new SubsOrPurMergeWebResponse();
        if (CollectionUtils.isNotEmpty(orderList) && CollectionUtils.isNotEmpty(orderList.get(0).getOrderDtlList())) {
            response.setmBusiCode(orderList.get(0).getOrderDtlList().get(0).getmBusiCode());
        }
        return createResponse(context, orderList.get(0), response);
    }

}