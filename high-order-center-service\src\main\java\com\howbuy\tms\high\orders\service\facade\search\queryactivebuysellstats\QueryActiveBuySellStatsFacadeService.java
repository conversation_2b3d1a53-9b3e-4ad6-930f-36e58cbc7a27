/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.tms.high.orders.service.facade.search.queryactivebuysellstats;

import com.howbuy.tms.high.orders.facade.search.queryactivebuysellstats.QueryActiveBuySellStatsFacade;
import com.howbuy.tms.high.orders.facade.search.queryactivebuysellstats.QueryActiveBuySellStatsRequest;
import com.howbuy.tms.high.orders.facade.search.queryactivebuysellstats.QueryActiveBuySellStatsResponse;
import com.howbuy.tms.high.orders.service.service.queryactivebuysellstats.QueryActiveBuySellStatsService;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @description: 查询主动买卖统计数据接口实现
 * <AUTHOR>
 * @date 2025/9/15 20:30
 * @since JDK 1.8
 */
@DubboService
@Component
public class QueryActiveBuySellStatsFacadeService implements QueryActiveBuySellStatsFacade {

    private static final Logger logger = LogManager.getLogger(QueryActiveBuySellStatsFacadeService.class);

    @Autowired
    private QueryActiveBuySellStatsService queryActiveBuySellStatsService;

    @Override
    public QueryActiveBuySellStatsResponse execute(QueryActiveBuySellStatsRequest request) {
        return queryActiveBuySellStatsService.queryActiveBuySellStats(request);
    }
}
