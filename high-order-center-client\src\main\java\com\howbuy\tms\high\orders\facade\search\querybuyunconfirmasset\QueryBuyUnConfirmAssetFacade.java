/**
 * Copyright (c) 2025, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.orders.facade.search.querybuyunconfirmasset;

import com.howbuy.tms.common.client.BaseFacade;

/**
 * @api {DUBBO} com.howbuy.tms.high.orders.facade.search.querybuyunconfirmasset.QueryBuyUnConfirmAssetFacade.execute(QueryBuyUnConfirmAssetRequest request)
 * @apiVersion 1.0.0
 * @apiGroup QueryBuyUnConfirmAssetFacade
 * @apiName execute
 * @apiDescription 查询买入待确认资产接口，查询客户的买入待确认订单信息
 *
 * @apiParam (请求参数) {String} disCode 分销机构代码
 * @apiParam (请求参数) {String} outletCode 网点代码
 * @apiParam (请求参数) {String} [appDt] 申请日期(yyyyMMdd)
 * @apiParam (请求参数) {String} [appTm] 申请时间(HHMMSS)
 * @apiParam (请求参数) {String} operIp 交易Ip
 * @apiParam (请求参数) {String} [txCode] 交易码(有默认值，无需设置)
 * @apiParam (请求参数) {String} txChannel 交易渠道<br>1-柜台;2-网站;3-电话;4-Wap;5-App
 * @apiParam (请求参数) {String} dataTrack 数据跟踪
 * @apiParam (请求参数) {String} [txAcctNo] 交易账号
 * @apiParam (请求参数) {String} hbOneNo 一账通账号(必填)
 *
 * @apiSuccess (返回参数) {String} returnCode 返回码
 * @apiSuccess (返回参数) {String} description 返回描述
 * @apiSuccess (返回参数) {BigDecimal} totalUnConfirmAmt 总待确认金额(人民币)
 * @apiSuccess (返回参数) {BigDecimal} totalCurrencyUnConfirmAmt 总待确认金额当前币种
 * @apiSuccess (返回参数) {List} orderDetailList 订单明细列表
 * @apiSuccess (返回参数) {String} orderDetailList.dealNo 订单号
 * @apiSuccess (返回参数) {String} orderDetailList.disCode 分销渠道
 * @apiSuccess (返回参数) {String} orderDetailList.fundCode 产品编码
 * @apiSuccess (返回参数) {BigDecimal} orderDetailList.unConfirmAmt 待确认金额(人民币)
 * @apiSuccess (返回参数) {BigDecimal} orderDetailList.currencyUnConfirmAmt 待确认金额当前币种
 * @apiSuccess (返回参数) {String} orderDetailList.mBusiCode 中台业务类型
 * @apiSuccess (返回参数) {String} orderDetailList.scaleType 销售类型(1-直销;2-代销)
 *
 * @description: 查询买入待确认资产接口，支持查询代销和直销的买入待确认订单
 * <AUTHOR>
 * @date 2025/9/19 21:00
 * @since JDK 1.8
 */
public interface QueryBuyUnConfirmAssetFacade extends BaseFacade<QueryBuyUnConfirmAssetRequest, QueryBuyUnConfirmAssetResponse> {

}
