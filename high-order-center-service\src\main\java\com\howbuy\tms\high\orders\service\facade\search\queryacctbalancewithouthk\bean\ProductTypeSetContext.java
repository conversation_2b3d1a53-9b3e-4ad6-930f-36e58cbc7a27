package com.howbuy.tms.high.orders.service.facade.search.queryacctbalancewithouthk.bean;

import lombok.Getter;
import lombok.Setter;

import java.util.HashSet;
import java.util.Set;

/**
 * 产品代码分类集合
 */
@Getter
@Setter
public class ProductTypeSetContext {
    private final Set<String> incomeFundCodeSet = new HashSet<>();
    private final Set<String> guQuanFundCodeSet = new HashSet<>();
    private final Set<String> agentFractionateCallFundCodeSet = new HashSet<>();
    private final Set<String> directFractionateCallFundCodeSet = new HashSet<>();
    private final Set<String> fixedIncomeFundCodeSet = new HashSet<>();
    private final Set<String> qxFundCodeSet = new HashSet<>();
    private final Set<String> navTypeFundCodeSet = new HashSet<>();

}