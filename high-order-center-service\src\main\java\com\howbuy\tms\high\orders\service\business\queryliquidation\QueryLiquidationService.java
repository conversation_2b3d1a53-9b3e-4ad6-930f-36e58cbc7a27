package com.howbuy.tms.high.orders.service.business.queryliquidation;

import com.alibaba.fastjson2.JSON;
import com.howbuy.tms.common.enums.busi.YesOrNoEnum;
import com.howbuy.tms.common.outerservice.dtms.QueryCustFundClearanceOuterService;
import com.howbuy.tms.common.outerservice.dtms.request.QueryCustFundClearanceRequestDTO;
import com.howbuy.tms.common.outerservice.dtms.response.QueryCustFundClearanceResponseDTO;
import com.howbuy.tms.high.orders.service.service.acctbalancebaseinfo.AcctBalanceBaseInfoService;
import com.howbuy.tms.high.orders.service.service.acctbalancebaseinfo.bean.QueryBalanceParam;
import com.howbuy.tms.high.orders.service.service.acctbalancebaseinfo.bean.QueryLiquidationBalanceFundInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description:查询清仓产品信息
 * @Author: yun.lu
 * Date: 2025/9/28 14:29
 */
@Slf4j
@Service
public class QueryLiquidationService {
    @Resource
    private QueryCustFundClearanceOuterService queryCustFundClearanceOuterService;

    @Resource
    private AcctBalanceBaseInfoService acctBalanceBaseInfoService;

    /**
     * @param hbOneNo
     * @param txAcctNo
     * @return void
     * @description: 查询所有渠道的清仓产品基金代码
     * @author: jinqing.rao
     * @date: 2025/9/16 17:04
     * @since JDK 1.8
     */
    public List<String> queryAllChannelLiquidationFundCodeList(String hbOneNo, String txAcctNo, List<String> disCodeList, String searchOverseas) {
        List<String> allFundCodes = new ArrayList<>();
        // 好买清仓的代码信息
        List<String> hMfundCodeList = queryLiquidationFundCodes(txAcctNo, hbOneNo, disCodeList);
        // 海外清仓代码信息
        List<String> hkClearanceFundCodeList = new ArrayList<>();
        if (StringUtils.isNotBlank(searchOverseas) && YesOrNoEnum.YES.getCode().equals(searchOverseas)) {
            hkClearanceFundCodeList = getHkClearanceFundCodeList(hbOneNo);
        }

        if (CollectionUtils.isNotEmpty(hMfundCodeList)) {
            allFundCodes.addAll(hMfundCodeList);
        }

        if (CollectionUtils.isNotEmpty(hkClearanceFundCodeList)) {
            allFundCodes.addAll(hkClearanceFundCodeList);
        }
        return allFundCodes;
    }

    /**
     * 查询清仓基金编码列表
     *
     * @param txAcctNo    交易账号
     * @param hbOneNo     一账通号
     * @param disCodeList 基金编码
     * @return 清仓基金编码列表
     */
    public List<String> queryLiquidationFundCodes(String txAcctNo, String hbOneNo, List<String> disCodeList) {
        List<String> liquidationFundCodes = new ArrayList<>();

        try {
            // 构建查询参数
            QueryBalanceParam queryParam = new QueryBalanceParam();
            queryParam.setTxAcctNo(txAcctNo);
            queryParam.setHboneNo(hbOneNo);

            // 设置分销渠道列表（好买和好甄）
            queryParam.setDisCodeList(disCodeList);

            // 调用清仓查询接口
            List<QueryLiquidationBalanceFundInfo> liquidationBalanceList =
                    acctBalanceBaseInfoService.queryLiquidationBalanceFundInfo(queryParam);

            if (CollectionUtils.isNotEmpty(liquidationBalanceList)) {
                liquidationFundCodes = liquidationBalanceList.stream()
                        .map(QueryLiquidationBalanceFundInfo::getFundCode)
                        .distinct()
                        .collect(Collectors.toList());
            }

            log.info("查询清仓基金编码完成, txAcctNo: {}, hbOneNo: {}, disCode: {}, 清仓基金数量: {}",
                    txAcctNo, hbOneNo, JSON.toJSONString(disCodeList), liquidationFundCodes.size());

        } catch (Exception e) {
            log.error("查询清仓基金编码异常, txAcctNo: {}, hbOneNo: {}, disCode: {}, error: {}",
                    txAcctNo, hbOneNo, JSON.toJSONString(disCodeList), e.getMessage(), e);
        }

        return liquidationFundCodes;
    }

    /**
     * @param hbOneNo
     * @return java.util.List<java.lang.String>
     * @description: 获取海外清仓代码
     * @author: jinqing.rao
     * @date: 2025/9/16 17:01
     * @since JDK 1.8
     */
    public List<String> getHkClearanceFundCodeList(String hbOneNo) {
        QueryCustFundClearanceRequestDTO queryCustFundClearanceRequestDTO = new QueryCustFundClearanceRequestDTO();
        queryCustFundClearanceRequestDTO.setHboneNo(hbOneNo);
        QueryCustFundClearanceResponseDTO queryCustFundClearanceResponseDTO = queryCustFundClearanceOuterService.queryCustFundClearance(queryCustFundClearanceRequestDTO);
        return queryCustFundClearanceResponseDTO.getClearanceFundList();
    }
}
