package com.howbuy.tms.high.orders.service.facade.search.queryfeeinfo;

import com.alibaba.fastjson.JSON;
import com.howbuy.common.date.DateUtil;
import com.howbuy.tms.common.constant.ExceptionCodes;
import com.howbuy.tms.common.enums.busi.*;
import com.howbuy.tms.common.exception.BusinessException;
import com.howbuy.tms.common.outerservice.acccenter.queryallcustinfo.QueryAllCustInfoOuterService;
import com.howbuy.tms.common.outerservice.acccenter.querycustInfo.QueryCustInfoResult;
import com.howbuy.tms.common.outerservice.crm.td.queryprebookdtl.QueryPreBookDtlOuterResult;
import com.howbuy.tms.common.outerservice.crm.td.queryprebookdtl.QueryPreBookDtlOuterService;
import com.howbuy.tms.common.outerservice.interlayer.hzFundAmtLockCfg.HzFundAmtLockCfgService;
import com.howbuy.tms.common.outerservice.interlayer.hzFundAmtLockCfg.bean.HzFundAmtLockCfgDto;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.QueryHighProductOuterService;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductBaseInfoBean;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductFeeRateBean;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductInfoBean;
import com.howbuy.tms.common.outerservice.interlayer.queryproductinfo.bean.ProductAppointmentInfoBean;
import com.howbuy.tms.common.utils.StringUtils;
import com.howbuy.tms.high.orders.dao.po.HighDealOrderDtlPo;
import com.howbuy.tms.high.orders.facade.search.queryfeeinfo.QueryFeeInfoFacade;
import com.howbuy.tms.high.orders.facade.search.queryfeeinfo.QueryFeeInfoFacadeRequest;
import com.howbuy.tms.high.orders.facade.search.queryfeeinfo.QueryFeeInfoFacadeResponse;
import com.howbuy.tms.high.orders.service.business.busiprocess.common.AbstractSubsOrPurLogicProcess;
import com.howbuy.tms.high.orders.service.business.busiprocess.common.HzCreateOrderLogicProcess;
import com.howbuy.tms.high.orders.service.business.discountrateandfee.BuyFeeDto;
import com.howbuy.tms.high.orders.service.business.discountrateandfee.CalBuyFeeParam;
import com.howbuy.tms.high.orders.service.business.factory.createOrder.SubsOrPurLogicProcessFactory;
import com.howbuy.tms.high.orders.service.common.enums.HzDiscountEffectEnum;
import com.howbuy.tms.high.orders.service.repository.HighDealOrderDtlRepository;
import com.howbuy.tms.high.orders.service.service.acctbalancebaseinfo.AcctBalanceBaseInfoService;
import com.howbuy.tms.high.orders.service.service.acctbalancebaseinfo.bean.AcctBalanceBaseInfo;
import com.howbuy.tms.high.orders.service.service.acctbalancebaseinfo.bean.QueryAcctBalanceBaseParam;
import com.howbuy.tms.high.orders.service.service.highdealorderdtl.QueryHighDealOrderParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * @Description:查询费用信息接口实现
 * @Author: yun.lu
 * Date: 2023/12/27 14:54
 */
@DubboService
@Service("queryFeeInfoFacade")
@Slf4j
public class QueryFeeInfoFacadeService implements QueryFeeInfoFacade {
    @Autowired
    private SubsOrPurLogicProcessFactory subsOrPurLogicProcessFactory;
    @Autowired
    private QueryHighProductOuterService queryHighProductOuterService;
    @Autowired
    private QueryPreBookDtlOuterService queryPreBookDtlOuterService;
    @Autowired
    private QueryAllCustInfoOuterService queryAllCustInfoOuterService;
    @Autowired
    private AcctBalanceBaseInfoService acctBalanceBaseInfoService;
    @Autowired
    private HzCreateOrderLogicProcess hzCreateOrderLogicProcess;
    @Autowired
    private HzFundAmtLockCfgService hzFundAmtLockCfgService;
    @Autowired
    private HighDealOrderDtlRepository highDealOrderDtlRepository;


    /**
     * @api {DUBBO} com.howbuy.tms.high.orders.facade.search.queryfeeinfo.QueryFeeInfoFacade.execute(QueryFeeInfoFacadeRequest request)
     * @apiVersion 1.0.0
     * @apiGroup QueryFeeInfoFacadeService
     * @apiName execute
     * @apiDescription 查询费用信息接口实现
     * @apiParam (请求参数) {String} fundCode 基金代码
     * @apiParam (请求参数) {Number} subsAmt 认缴金额
     * @apiParam (请求参数) {Number} paidAmt 实缴金额/净申请金额
     * @apiParam (请求参数) {String} paymentType 支付方式：01-自划款；04-银行卡代扣；06-储蓄罐支付
     * @apiParam (请求参数) {String} isFirstPay 是否首次实缴,1:是;0:不是
     * @apiParam (请求参数) {String} appointmentDealNo 预约订单号
     * @apiParam (请求参数) {Number} discountRate OP手动输入的折扣
     * @apiParam (请求参数) {String} bankCode 银行编码
     * @apiParam (请求参数) {String} txChannelCode 交易渠道
     * @apiParam (请求参数) {String} mBusinessCode 中台交易编码
     * @apiParam (请求参数) {Number} queryDate 当前日期
     * @apiParam (请求参数) {String} txAcctNo 交易账号
     * @apiParam (请求参数) {String} hbOneNo 一账通账号
     * @apiParam (请求参数) {String} disCode 分销机构代码
     * @apiParam (请求参数) {String} outletCode 网点代码
     * @apiParam (请求参数) {String} appDt 申请日期
     * @apiParam (请求参数) {String} appTm 申请时间
     * @apiParam (请求参数) {Number} pageNo 页码
     * @apiParam (请求参数) {Number} pageSize 每页记录数
     * @apiParam (请求参数) {String} operIp 交易IP
     * @apiParam (请求参数) {String} txCode 交易码
     * @apiParam (请求参数) {String} txChannel 交易渠道 1-柜台,2-网站,3-电话,4-Wap,5-App,6-机构
     * @apiParam (请求参数) {String} dataTrack 数据跟踪
     * @apiParam (请求参数) {String} subOutletCode 子网点代码
     * @apiParamExample 请求参数示例
     * discountRate=5877.************&bankCode=b8S9xAz&hbOneNo=laxWPeQu&appointmentDealNo=HTeoye&pageSize=6057&paidAmt=2461.*************&disCode=i&txChannel=i18pAlxuN&mBusinessCode=pEc3T8J&paymentType=SNFCo9kD&txChannelCode=jRDa6UhTp&appTm=xz0Yl5wVU&isFirstPay=N9N9xQQLt&queryDate=*************&fundCode=4okodG&subOutletCode=UoZdF&pageNo=5557&operIp=WJP&subsAmt=2638.************&txAcctNo=5Mvh&appDt=IwHlWvZ&dataTrack=EPn&txCode=3S95qs&outletCode=CFUV
     * @apiSuccess (响应结果) {Number} fee 费用
     * @apiSuccess (响应结果) {Number} discountRate 最终折扣
     * @apiSuccess (响应结果) {Number} originalFeeRate 原始费率
     * @apiSuccess (响应结果) {Number} originalFee 原始费用
     * @apiSuccess (响应结果) {Number} actualPayAmt 实际支付金额
     * @apiSuccess (响应结果) {Number} paidAmt 实缴金额
     * @apiSuccess (响应结果) {Number} appointAmt 预约金额
     * @apiSuccess (响应结果) {Number} appointDiscount 预约折扣
     * @apiSuccess (响应结果) {String} disCountEffect 折扣是否生效,1:生效,0:不生效
     * @apiSuccess (响应结果) {String} paidAmtLessThanNeedPaidAmt 实缴金额是否小于需要支付金额,1:小于,0:大于等于
     * @apiSuccess (响应结果) {String} returnCode 返回码
     * @apiSuccess (响应结果) {String} description 描述
     * @apiSuccess (响应结果) {Number} totalCount 总记录数
     * @apiSuccess (响应结果) {Number} totalPage 总页数
     * @apiSuccess (响应结果) {Number} pageNo 当前页
     * @apiSuccessExample 响应结果示例
     * {"discountRate":233.7158785598692,"originalFee":6101.114425841864,"appointDiscount":8345.515422575765,"totalPage":3320,"fee":4142.130707272971,"description":"HXd","paidAmt":285.88836235471706,"appointAmt":104.09602255865735,"totalCount":5075,"returnCode":"6WY","disCountEffect":"gLSdGJ2N","pageNo":6962,"paidAmtLessThanNeedPaidAmt":"SyMbINtOH","actualPayAmt":3729.0703990678476,"originalFeeRate":3514.6363623500565}
     */
    @Override
    public QueryFeeInfoFacadeResponse execute(QueryFeeInfoFacadeRequest request) {
        // 1.查询产品基础信息
        HighProductInfoBean highProductBaseInfo = queryHighProductOuterService.getHighProductInfo(request.getFundCode());
        // 2.查询预约信息
        QueryPreBookDtlOuterResult queryPreBookDtlOuterResult = queryPreBookDtlOuterService.queryPreBook(request.getAppointmentDealNo());
        // 3.查询客户信息
        QueryCustInfoResult queryCustInfoResult = queryAllCustInfoOuterService.queryCustInfo(request.getTxAcctNo(), request.getDisCode());
        // 4.获取预约日历
        Date queryDate = request.getQueryDate() != null ? request.getQueryDate() : new Date();
        ProductAppointmentInfoBean productAppointmentInfoBean = queryHighProductOuterService.queryAppointmentInfoByAppointDateWithDeferPurchaseConfig(
                request.getHbOneNo(), highProductBaseInfo.getFundCode(), "0", highProductBaseInfo.getShareClass(), request.getDisCode(), queryDate);
        // 5.获取净申请金额
        BigDecimal netApplyAmt = getNetApplyAmt(request, productAppointmentInfoBean, highProductBaseInfo);
        if (netApplyAmt == null) {
            throw new BusinessException(ExceptionCodes.PARAMS_ERROR, "获取不到净申请金额");
        }
        // 6.获取计算费用的金额
        BigDecimal feeBaseAmt = null;
        String feeRateBusiCode = null;
        BigDecimal appointAmt = null;
        HighProductFeeRateBean highProductFeeRateBean = null;
        if (DisCodeEnum.HZ.getCode().equals(request.getDisCode())) {
            feeRateBusiCode = BusinessCodeEnum.SUBSCRIBE.getCode();
            // 获取基金费率
            highProductFeeRateBean = queryHighProductOuterService.getFundFeeRateByAmt(request.getFundCode(),
                    feeRateBusiCode, queryCustInfoResult.getInvstType(), highProductBaseInfo.getShareClass(), request.getSubsAmt() == null ? netApplyAmt : request.getSubsAmt());
            feeBaseAmt = getHzFeeBaseAmt(request.getSubsAmt(), netApplyAmt, highProductFeeRateBean.getGetFeeRateMethod());
            appointAmt = getHzAppointAmt(highProductBaseInfo, queryPreBookDtlOuterResult, highProductFeeRateBean);

        } else {
            BusinessCodeEnum businessCodeEnum = BusinessCodeEnum.getByMCode(request.getmBusinessCode());
            feeRateBusiCode = businessCodeEnum != null ? businessCodeEnum.getCode() : null;
            // 获取基金费率
            highProductFeeRateBean = queryHighProductOuterService.getFundFeeRateByAmt(request.getFundCode(),
                    feeRateBusiCode, queryCustInfoResult.getInvstType(), highProductBaseInfo.getShareClass(), netApplyAmt);
            feeBaseAmt = netApplyAmt;
            appointAmt = queryPreBookDtlOuterResult != null ? queryPreBookDtlOuterResult.getAckAmt() : null;
        }
        // 7.计算费用
        CalBuyFeeParam calBuyFeeParam = new CalBuyFeeParam();
        calBuyFeeParam.setFeeBaseAmt(feeBaseAmt);
        calBuyFeeParam.setDisCode(request.getDisCode());
        calBuyFeeParam.setBusiCode(getBusiCode(highProductBaseInfo));
        calBuyFeeParam.setFeeRateBusiCode(feeRateBusiCode);
        calBuyFeeParam.setFeeCalMode(highProductBaseInfo.getFeeCalMode());
        calBuyFeeParam.setGetFeeRateMethod(highProductFeeRateBean.getGetFeeRateMethod());
        calBuyFeeParam.setShareClass(highProductBaseInfo.getShareClass());
        calBuyFeeParam.setPaymentType(request.getPaymentType());
        calBuyFeeParam.setInvstType(queryCustInfoResult.getInvstType());
        calBuyFeeParam.setConstantFee(highProductFeeRateBean.getConstantFee());
        calBuyFeeParam.setFundCode(request.getFundCode());
        calBuyFeeParam.setIsFirstPay(getIsFirstPay(request));
        calBuyFeeParam.setAppointmentType(queryPreBookDtlOuterResult == null ? AppointmentTypeEnum.SYSTEM.getCode() : AppointmentTypeEnum.ADVISER.getCode());
        calBuyFeeParam.setAppointmentAmt(appointAmt);
        calBuyFeeParam.setAppointmentDiscount(queryPreBookDtlOuterResult != null ? queryPreBookDtlOuterResult.getDiscountRate() : null);
        calBuyFeeParam.setDiscountUseType(queryPreBookDtlOuterResult != null ? queryPreBookDtlOuterResult.getDiscountUseType() : null);
        calBuyFeeParam.setDiscountAmt(queryPreBookDtlOuterResult != null ? queryPreBookDtlOuterResult.getDiscountAmt() : null);
        calBuyFeeParam.setDiscountRate(request.getDiscountRate());
        calBuyFeeParam.setBankCode(request.getBankCode());
        AbstractSubsOrPurLogicProcess subsOrPurLogicProcess = subsOrPurLogicProcessFactory.getSubsOrPurLogicProcess(request.getTxChannelCode());

        // 9.构建返回信息
        BuyFeeDto buyFeeDto = subsOrPurLogicProcess.getFeeAmt(calBuyFeeParam);
        QueryFeeInfoFacadeResponse response = new QueryFeeInfoFacadeResponse();
        BeanUtils.copyProperties(buyFeeDto, response);
        response.setOriginalFeeRate(highProductFeeRateBean.getFeeRate());
        // 9.1.原应付费用
        if (buyFeeDto.getOriginalFee() != null && BigDecimal.ZERO.compareTo(buyFeeDto.getOriginalFee()) < 0) {
            response.setOriginalFee(buyFeeDto.getOriginalFee().setScale(2, BigDecimal.ROUND_DOWN));
        } else {
            response.setOriginalFee(BigDecimal.ZERO);
        }
        // 9.2.应付金额
        response.setActualPayAmt(netApplyAmt.add(buyFeeDto.getFee()));
        // 9.3.实缴金额
        response.setPaidAmt(netApplyAmt);
        // 9.4.预约金额
        response.setAppointAmt(appointAmt);
        // 9.5.预约折扣
        response.setAppointDiscount(calBuyFeeParam.getAppointmentDiscount());
        // 9.6.折扣是否生效
        response.setDisCountEffect(getDisCountEffect(highProductFeeRateBean.getGetFeeRateMethod(), netApplyAmt, request.getSubsAmt(), queryPreBookDtlOuterResult, buyFeeDto, appointAmt));
        // 9.7.最终折扣
        response.setDiscountRate(buyFeeDto.getDiscountRate());
        // 9.8.实缴金额是否小于认缴乘以比例金额
        if (request.getSubsAmt() != null) {
            if (productAppointmentInfoBean != null && productAppointmentInfoBean.getPayRatio() != null && request.getSubsAmt().multiply(productAppointmentInfoBean.getPayRatio()).compareTo(netApplyAmt) > 0) {
                response.setPaidAmtLessThanNeedPaidAmt(YesOrNoEnum.YES.getCode());
            } else {
                response.setPaidAmtLessThanNeedPaidAmt(YesOrNoEnum.NO.getCode());
            }
        }
        response.setReturnCode(ExceptionCodes.SUCCESS);
        return response;
    }

    /**
     * 获取好臻预约金额
     */
    private BigDecimal getHzAppointAmt(HighProductInfoBean highProductBaseInfo, QueryPreBookDtlOuterResult queryPreBookDtlOuterResult, HighProductFeeRateBean highProductFeeRateBean) {
        BigDecimal appointAmt;
        if (YesOrNoEnum.YES.getCode().equals(highProductBaseInfo.getPeDivideCallFlag())) {
            if (GetFeeRateMethodEnum.SUBS.getCode().equals(highProductFeeRateBean.getGetFeeRateMethod())) {
                appointAmt = queryPreBookDtlOuterResult != null ? queryPreBookDtlOuterResult.getSubsAmt() : null;
            } else {
                appointAmt = queryPreBookDtlOuterResult != null ? queryPreBookDtlOuterResult.getAckAmt() : null;
            }
        } else {
            appointAmt = queryPreBookDtlOuterResult != null ? queryPreBookDtlOuterResult.getAckAmt() : null;
        }
        return appointAmt;
    }


    private String getIsFirstPay(QueryFeeInfoFacadeRequest request) {
        if (!StringUtils.isBlank(request.getIsFirstPay())) {
            return request.getIsFirstPay();
        }
        ;
        return acctBalanceBaseInfoService.getIsFirstBuy(request.getFundCode(), request.getTxAcctNo(), request.getDisCode());
    }

    /**
     * 折扣是否生效是否生效
     *
     * @param feeRateMethod              计算手续费方式,4:按认缴金额,5:按实缴金额
     * @param paidAmt                    实缴金额
     * @param subsAmt                    认缴金额
     * @param queryPreBookDtlOuterResult 预约单信息
     * @return 折扣是否生效, 1:生效,0:存在有效折扣,但是折扣不生效;2:其他场景
     */
    public String getDisCountEffect(String feeRateMethod, BigDecimal paidAmt, BigDecimal subsAmt, QueryPreBookDtlOuterResult queryPreBookDtlOuterResult, BuyFeeDto buyFeeDto, BigDecimal appointAmt) {
        log.info("QueryFeeInfoFacadeService-getDisCountEffect,折扣是否生效是否生效,feeRateMethod={},paidAmt={},subsAmt={},queryPreBookDtlOuterResult={},buyFeeDto={}", feeRateMethod, paidAmt, subsAmt, JSON.toJSONString(queryPreBookDtlOuterResult), JSON.toJSONString(buyFeeDto));
        if (queryPreBookDtlOuterResult == null) {
            log.info("QueryFeeInfoFacadeService-getDisCountEffect,没有预约单,没有生效的折扣,为其他场景");
            return HzDiscountEffectEnum.OTHER_SCENARIO.getCode();
        }
        if (queryPreBookDtlOuterResult.getDiscountRate() == null || queryPreBookDtlOuterResult.getDiscountRate().compareTo(BigDecimal.ZERO) < 0 || queryPreBookDtlOuterResult.getDiscountRate().compareTo(BigDecimal.ONE) >= 0) {
            log.info("QueryFeeInfoFacadeService-getDisCountEffect,预约折扣,为null或者不在0-1的范围内,为其他场景,preDiscountRate={}", queryPreBookDtlOuterResult.getDiscountRate());
            return HzDiscountEffectEnum.OTHER_SCENARIO.getCode();
        }
        if (buyFeeDto.getFee() == null || BigDecimal.ZERO.compareTo(buyFeeDto.getFee()) == 0) {
            log.info("QueryFeeInfoFacadeService-getDisCountEffect,手续费为0,或者为null,为其他场景");
            return HzDiscountEffectEnum.OTHER_SCENARIO.getCode();
        }
        if (buyFeeDto.getDiscountRate() == null || buyFeeDto.getDiscountRate().compareTo(queryPreBookDtlOuterResult.getDiscountRate()) != 0) {
            log.info("QueryFeeInfoFacadeService-getDisCountEffect,最终折扣与预约折扣不相等,就是存在有效折扣,但是不生效,discountRate={},preDiscountRate={}", buyFeeDto.getDiscountRate(), queryPreBookDtlOuterResult.getDiscountRate());
            return HzDiscountEffectEnum.EXIST_DISCOUNT_NOT_EFFECT.getCode();
        }
        // 2.若【手续费基数类型】=认缴
        // 存在有效折扣：输入的认缴金额≥预约单的【认缴金额】
        if (GetFeeRateMethodEnum.SUBS.getCode().equals(feeRateMethod)) {
            if (subsAmt == null || appointAmt == null || subsAmt.compareTo(appointAmt) < 0) {
                log.info("QueryFeeInfoFacadeService-getDisCountEffect,手续费基数类型=认缴,认缴金额与预约单认缴不相等,或者至少有一方为null,存在有效折扣,但是不生效");
                return HzDiscountEffectEnum.EXIST_DISCOUNT_NOT_EFFECT.getCode();
            }
            log.info("QueryFeeInfoFacadeService-getDisCountEffect,手续费基数类型=认缴,存在有效折扣");
            return HzDiscountEffectEnum.DISCOUNT_EFFECT.getCode();
        }

        // 3.若【手续费基数类型】=实缴
        // 存在有效折扣：输入的实缴金额≥预约实缴的【购买金额】
        else if (GetFeeRateMethodEnum.PAID.getCode().equals(feeRateMethod)) {
            if (paidAmt == null || appointAmt == null || paidAmt.compareTo(appointAmt) < 0) {
                log.info("QueryFeeInfoFacadeService-getDisCountEffect,手续费基数类型=实缴,实缴金额与预约单实缴不相等,或者至少有一方为null,存在有效折扣,但是不生效");
                return HzDiscountEffectEnum.EXIST_DISCOUNT_NOT_EFFECT.getCode();
            }
            log.info("QueryFeeInfoFacadeService-getDisCountEffect,手续费基数类型=实缴,存在有效折扣");
            return HzDiscountEffectEnum.DISCOUNT_EFFECT.getCode();
        }
        log.error("QueryFeeInfoFacadeService-getDisCountEffect,不知道什么场景,直接返回其他场景,feeRateMethod={},paidAmt={},subsAmt={},queryPreBookDtlOuterResult={},buyFeeDto={}", feeRateMethod, paidAmt, subsAmt, JSON.toJSONString(queryPreBookDtlOuterResult), JSON.toJSONString(buyFeeDto));
        return HzDiscountEffectEnum.OTHER_SCENARIO.getCode();
    }

    /**
     * 获取净申请金额
     *
     * @param request                    请求
     * @param productAppointmentInfoBean 预约日历信息
     * @return 实缴金额
     */
    private BigDecimal getNetApplyAmt(QueryFeeInfoFacadeRequest request, ProductAppointmentInfoBean productAppointmentInfoBean, HighProductInfoBean highProductBaseInfo) {
        log.info("QueryFeeInfoFacadeService-getPaidAmt,获取净申请金额,request={},productAppointmentInfoBean={}", JSON.toJSONString(request), JSON.toJSONString(productAppointmentInfoBean));
        if (!DisCodeEnum.HZ.getCode().equals(request.getDisCode())) {
            log.info("QueryFeeInfoFacadeService-getPaidAmt,非好臻的,净申请金额直接取值入参,paidAmt={}", request.getPaidAmt());
            return request.getPaidAmt();
        }
        // 1.如果有实缴,就直接返回实缴
        if (request.getPaidAmt() != null && request.getPaidAmt().compareTo(BigDecimal.ZERO) > 0) {
            log.info("QueryFeeInfoFacadeService-getPaidAmt,获取净申请金额,paidAmt={},request={},productAppointmentInfoBean={}", request.getPaidAmt(), JSON.toJSONString(request), JSON.toJSONString(productAppointmentInfoBean));
            return request.getPaidAmt();
        }
        // 2.如果认缴金额为空,也不能计算实缴
        if (request.getSubsAmt() == null) {
            log.warn("QueryFeeInfoFacadeService-getPaidAmt,获取净申请金额,也没有认缴金额,无法计算实缴金额,request={}", JSON.toJSONString(request));
            return null;
        }
        // 3.如果是分次call,认缴金额等于实缴金额
        if (highProductBaseInfo.getPeDivideCallFlag() != null && YesOrNoEnum.NO.getCode().equals(highProductBaseInfo.getPeDivideCallFlag())) {
            log.warn("QueryFeeInfoFacadeService-getPaidAmt,如果是非分次call,认缴金额等于实缴金额等于获取净申请金额,request={}", JSON.toJSONString(request));
            return request.getSubsAmt();
        }

        // 4.如果传参没有,就需用认缴按比例计算实缴
        BigDecimal paidAmt = getPaidAmt(request, productAppointmentInfoBean);
        log.info("QueryFeeInfoFacadeService-getPaidAmt,获取获取净申请金额,结束,paidAmt={},subsAmt={},payRatio={}", paidAmt, request.getSubsAmt(), productAppointmentInfoBean.getPayRatio());
        return paidAmt;
    }

    private BigDecimal getPaidAmt(QueryFeeInfoFacadeRequest request, ProductAppointmentInfoBean productAppointmentInfoBean) {
        // 3.确认持仓
        QueryAcctBalanceBaseParam param = new QueryAcctBalanceBaseParam();
        param.setTxAcctNo(request.getTxAcctNo());
        param.setFundCodeList(Collections.singletonList(request.getFundCode()));
        param.setDisCodeList(Collections.singletonList(DisCodeEnum.HZ.getCode()));
        param.setIncludeDirect(false);
        List<AcctBalanceBaseInfo> confirmBalanceBaseInfoList = acctBalanceBaseInfoService.queryConfirmBalanceBaseInfo(param);
        // 4.查询生效的好臻金额锁定配置
        List<HzFundAmtLockCfgDto> hzFundAmtLockCfgDtoList = new ArrayList<>();
        if (productAppointmentInfoBean != null) {
            log.info("预约日历非空,查询好臻金额锁定配置");
            hzFundAmtLockCfgDtoList = hzFundAmtLockCfgService.queryValidHzFundAmtLockCfg(request.getHbOneNo(), request.getFundCode(), productAppointmentInfoBean.getAppointId());
        }
        // 5.在途订单
        QueryHighDealOrderParam dealOrderParam = new QueryHighDealOrderParam();
        dealOrderParam.setTxAcctNo(request.getTxAcctNo());
        dealOrderParam.setFundCodeList(Collections.singletonList(request.getFundCode()));
        dealOrderParam.setDisCodeList(Collections.singletonList(DisCodeEnum.HZ.getCode()));
        if (productAppointmentInfoBean != null) {
            dealOrderParam.setAppointId(productAppointmentInfoBean.getAppointId());
        }
        List<HighDealOrderDtlPo> onWayHighDealOrderDtlPoList = highDealOrderDtlRepository.getOnWayAgentDealDtlList(dealOrderParam);
        return hzCreateOrderLogicProcess.getPaidAmt(request.getSubsAmt(), onWayHighDealOrderDtlPoList, confirmBalanceBaseInfoList, hzFundAmtLockCfgDtoList, productAppointmentInfoBean);
    }

    /**
     * 获取费用计算基础金额
     *
     * @param subsAmt       认缴金额
     * @param paidAmt       实缴金额
     * @param feeRateMethod 计算手续费方式,4:按认缴金额,5:按实缴金额
     * @return 净申请金额
     */
    private BigDecimal getHzFeeBaseAmt(BigDecimal subsAmt, BigDecimal paidAmt, String feeRateMethod) {
        log.info("QueryFeeInfoFacadeService-getHzFeeBaseAmt,获取费用计算基础金额,subsAmt={},paidAmt={},feeRateMethod={}", subsAmt, paidAmt, feeRateMethod);
        if (feeRateMethod == null) {
            log.info("QueryFeeInfoFacadeService-getHzFeeBaseAmt,没有手续费方式,默认用paidAmt,paidAmt={},feeRateMethod={}", paidAmt, feeRateMethod);
            return paidAmt;
        }
        if (GetFeeRateMethodEnum.SUBS.getCode().equals(feeRateMethod)) {
            log.info("QueryFeeInfoFacadeService-getHzFeeBaseAmt,费用计算基础金额为认缴金额,subsAmt={},feeRateMethod={}", subsAmt, feeRateMethod);
            return subsAmt;
        }
        if (GetFeeRateMethodEnum.PAID.getCode().equals(feeRateMethod)) {
            log.info("QueryFeeInfoFacadeService-getHzFeeBaseAmt,费用计算基础金额为实缴金额,paidAmt={},feeRateMethod={}", paidAmt, feeRateMethod);
            return paidAmt;
        }
        log.info("QueryFeeInfoFacadeService-getHzFeeBaseAmt,默认用paidAmt,paidAmt={},feeRateMethod={}", paidAmt, feeRateMethod);
        return paidAmt;

    }

    private String getBusiCode(HighProductBaseInfoBean highProductBaseInfo) {
        String busiCode = BusinessCodeEnum.SUBS.getCode();
        if (DateUtil.getAppDt().compareTo(highProductBaseInfo.getIpoEndDt()) > 0) {
            busiCode = BusinessCodeEnum.PURCHASE.getCode();
        }

        return busiCode;
    }
}
