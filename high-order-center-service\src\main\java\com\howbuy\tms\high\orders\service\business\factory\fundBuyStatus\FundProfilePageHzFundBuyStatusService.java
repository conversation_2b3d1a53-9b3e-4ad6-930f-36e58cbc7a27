/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.tms.high.orders.service.business.factory.fundBuyStatus;

import com.howbuy.acccenter.facade.query.querybindcustbankcardlist.CustBankInfoBean;
import com.howbuy.tms.common.enums.busi.BuyStatusTypeEnum;
import com.howbuy.tms.common.enums.busi.FundBuyStatusEnum;
import com.howbuy.tms.common.enums.busi.YesOrNoEnum;
import com.howbuy.tms.common.outerservice.acccenter.querycustbankcard.QueryCustBankCardOuterService;
import com.howbuy.tms.common.outerservice.acccenter.querycustbankcard.QueryCustomerBankCardParam;
import com.howbuy.tms.high.orders.service.business.relatedaccount.AccCenterAccountService;
import com.howbuy.tms.high.orders.service.business.relatedaccount.dto.AccountStatusInfoDTO;
import com.howbuy.tms.high.orders.service.common.enums.FundBuyStatusChannelResourceEnum;
import com.howbuy.tms.high.orders.service.common.enums.FundBuyStatusNotCanbuyCodeEnum;
import com.howbuy.tms.high.orders.service.facade.search.querybuyfundstatus.bean.FundBuyStatusDto;
import com.howbuy.tms.high.orders.service.facade.search.querybuyfundstatus.bean.QueryFundBuyStatusParam;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @description: 好甄基金档案页 基金是否可以购买
 * <AUTHOR>
 * @date 2025/9/10 15:10
 * @since JDK 1.8
 */
@Service
public class FundProfilePageHzFundBuyStatusService extends AbstractFundBuyStatusChannelSourceService{

    @Resource
    private AccCenterAccountService accCenterAccountService;

    @Resource
    private QueryCustBankCardOuterService queryCustBankCardOuterService;
    @Override
    public boolean getChannelSource(String disCode,String channelSource) {
        FundBuyStatusChannelResourceEnum enumByCodeAndDisCode = FundBuyStatusChannelResourceEnum.getEnumByCodeAndDisCode(channelSource, disCode);
        return FundBuyStatusChannelResourceEnum.FUND_PROFILE_PAGE_HM.equals(enumByCodeAndDisCode);
    }

    @Override
    public FundBuyStatusDto getFundBuyStatus(QueryFundBuyStatusParam queryFundBuyStatusParam) {
        // 客户尚未完成好买开户
        AccountStatusInfoDTO accountStatusInfo = accCenterAccountService.getAccountStatusInfo(queryFundBuyStatusParam.getCustInfo().getHboneNo());
        if(!YesOrNoEnum.YES.getCode().equals(accountStatusInfo.getHasHzAccount())){
            FundBuyStatusDto fundBuyStatusDto = new FundBuyStatusDto();
            fundBuyStatusDto.setFundBuyStatusEnum(FundBuyStatusEnum.CAN_NOT_BUY);
            fundBuyStatusDto.setBuyStatusType(BuyStatusTypeEnum.ERROR_CONFIG.getCode());
            fundBuyStatusDto.setFundBuyStatusNotCanbuyCodeEnum(FundBuyStatusNotCanbuyCodeEnum.CUST_NOT_FINISH_HM_ACCOUNT);
            return fundBuyStatusDto;
        }
        // 客户账号未激活
        if(!YesOrNoEnum.YES.getCode().equals(accountStatusInfo.getHasHzAccountActive())){
            FundBuyStatusDto fundBuyStatusDto = new FundBuyStatusDto();
            fundBuyStatusDto.setFundBuyStatusEnum(FundBuyStatusEnum.CAN_NOT_BUY);
            fundBuyStatusDto.setBuyStatusType(BuyStatusTypeEnum.ERROR_CONFIG.getCode());
            fundBuyStatusDto.setFundBuyStatusNotCanbuyCodeEnum(FundBuyStatusNotCanbuyCodeEnum.CUST_HM_ACCOUNT_NOT_ACTIVE);
            return fundBuyStatusDto;
        }
        // 客户未绑定银行卡
        QueryCustomerBankCardParam queryCustomerBankCardParam = new QueryCustomerBankCardParam();
        queryCustomerBankCardParam.setDisCode(queryFundBuyStatusParam.getDisCode());
        queryCustomerBankCardParam.setTxAcctNo(queryFundBuyStatusParam.getTxAcctNo());
        List<CustBankInfoBean> custBankInfoBeans = queryCustBankCardOuterService.queryBindBankCardList(queryCustomerBankCardParam);
        if(CollectionUtils.isEmpty(custBankInfoBeans)){
            FundBuyStatusDto fundBuyStatusDto = new FundBuyStatusDto();
            fundBuyStatusDto.setFundBuyStatusEnum(FundBuyStatusEnum.CAN_NOT_BUY);
            fundBuyStatusDto.setBuyStatusType(BuyStatusTypeEnum.ERROR_CONFIG.getCode());
            fundBuyStatusDto.setFundBuyStatusNotCanbuyCodeEnum(FundBuyStatusNotCanbuyCodeEnum.CUST_HM_ACCOUNT_NOT_BIND_BANK);
            return fundBuyStatusDto;
        }
        // 通用基础校验
        return getFundBuyStatusLogicService(queryFundBuyStatusParam);
    }
}
