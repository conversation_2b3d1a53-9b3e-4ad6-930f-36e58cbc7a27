/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.orders.facade.search.querybuyunconfirmasset;

import com.howbuy.tms.high.orders.facade.common.OrderSearchBaseResponse;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @description: 查询买入待确认资产响应结果
 * <AUTHOR>
 * @date 2025/9/19 21:00
 * @since JDK 1.8
 */
public class QueryBuyUnConfirmAssetResponse extends OrderSearchBaseResponse implements Serializable {


    private static final long serialVersionUID = 7767214780145979997L;
    /**
     * 总待确认金额(人民币)
     */
    private BigDecimal totalUnConfirmAmt;

    /**
     * 总待确认金额当前币种
     */
    private BigDecimal totalCurrencyUnConfirmAmt;

    /**
     * 订单明细列表
     */
    private List<BuyUnConfirmOrderDetailResponse> orderDetailList;

    public BigDecimal getTotalUnConfirmAmt() {
        return totalUnConfirmAmt;
    }

    public void setTotalUnConfirmAmt(BigDecimal totalUnConfirmAmt) {
        this.totalUnConfirmAmt = totalUnConfirmAmt;
    }

    public BigDecimal getTotalCurrencyUnConfirmAmt() {
        return totalCurrencyUnConfirmAmt;
    }

    public void setTotalCurrencyUnConfirmAmt(BigDecimal totalCurrencyUnConfirmAmt) {
        this.totalCurrencyUnConfirmAmt = totalCurrencyUnConfirmAmt;
    }

    public List<BuyUnConfirmOrderDetailResponse> getOrderDetailList() {
        return orderDetailList;
    }

    public void setOrderDetailList(List<BuyUnConfirmOrderDetailResponse> orderDetailList) {
        this.orderDetailList = orderDetailList;
    }
}
