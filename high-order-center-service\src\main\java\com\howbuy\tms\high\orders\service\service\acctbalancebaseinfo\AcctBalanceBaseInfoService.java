package com.howbuy.tms.high.orders.service.service.acctbalancebaseinfo;

import com.howbuy.interlayer.product.model.JjxswConfigModel;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.*;
import com.howbuy.tms.high.orders.dao.vo.AckDealOrderInfo;
import com.howbuy.tms.high.orders.dao.vo.DealOrderVo;
import com.howbuy.tms.high.orders.facade.search.queryacctbalance.QueryAcctBalanceResponse.BalanceBean;
import com.howbuy.tms.high.orders.facade.search.queryasset.HighFundAssetIncomeDomain;
import com.howbuy.tms.high.orders.service.facade.search.queryacctbalance.bean.OwnershipOrderDto;
import com.howbuy.tms.high.orders.service.facade.search.queryacctbalancewithouthk.bean.BalanceQueryResult;
import com.howbuy.tms.high.orders.service.service.acctbalancebaseinfo.bean.*;
import com.howbuy.tms.high.orders.service.service.ownershiprightorder.OwnershipOrderInfo;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @Description:用户持仓基础信息接口
 * @Author: yun.lu
 * Date: 2023/8/16 14:05
 */
public interface AcctBalanceBaseInfoService {

    /**
     * 查询股权订单信息
     */
    List<OwnershipOrderInfo> queryAcctOwnershipOrderInfo(QueryAcctBalanceBaseParam queryAcctBalanceBaseParam);


    /**
     * 查询用户持仓基础信息
     */
    List<AcctBalanceBaseInfo> queryAcctBalanceBaseInfo(QueryAcctBalanceBaseParam queryAcctBalanceBaseInfoParam);


    /**
     * 查询用户持仓明细基础信息
     */
    List<AcctBalanceDetailBaseInfo> queryAcctBalanceDetailBaseInfo(QueryAcctBalanceDetailBaseInfoParam queryAcctBalanceDetailBaseInfoParam);

    /**
     * 查询用户持仓明细基础信息
     */
    List<AcctSubBalanceDetailInfo> queryAcctSubBalanceDetailInfo(QueryAcctSubBalanceDetailParam param);

    /**
     * 查询在途持仓信息,已付款订单
     *
     * @param queryAcctBalanceBaseInfoParam 在途持仓信息查询入参
     * @return 持仓基本信息
     */
    List<AcctBalanceBaseInfo> queryOnWayBalanceBaseInfo(QueryAcctBalanceBaseParam queryAcctBalanceBaseInfoParam);


    /**
     * 查询确认持仓信息
     *
     * @param queryAcctBalanceBaseInfoParam 查询入参
     * @return 持仓基本信息
     */
    List<AcctBalanceBaseInfo> queryConfirmBalanceBaseInfo(QueryAcctBalanceBaseParam queryAcctBalanceBaseInfoParam);

    /**
     * 查询股权产品订单信息
     */
    Map<String, OwnershipOrderDto> getOwnershipOrderInfoMap(QueryAcctBalanceBaseParam queryAcctBalanceBaseParam);

    /**
     * 是否首单,好臻分次call首次只跟确认持仓有关
     *
     * @param fundCode 基金代码
     * @param txAcctNo 交易账号
     * @return 是否是首单, 1:是 0:不是
     */
    String getIsFirstBuy(String fundCode, String txAcctNo, String disCode);


    /**
     * 待资金到账订单信息
     *
     * @param queryBalanceParam 查询入参
     * @return 待资金到账订单信息
     */
    List<RefundDealOrderInfo> queryRefundAmtDealOrderInfo(QueryBalanceParam queryBalanceParam);


    /**
     * 查询国内海外持仓
     * @param param 查询入参
     * @return 持仓信息
     */
    List<CustConfirmBalanceDto> queryUnHkCustConfirmBalance(QueryBalanceParam param);

    /**
     * 查询代销确认的交易
     *
     * @param txAcctNo  交易账号
     * @param fundCodes 基金代码
     * @return 代销确认的交易
     */
    Map<String, List<AckDealOrderInfo>> getAgentAckDealDtlMap(String txAcctNo, Set<String> fundCodes);


    /**
     * 查询直销确认的交易
     *
     * @param hboneNo   一账通
     * @param fundCodes 基金代码
     * @return 直销确认的交易
     */
    Map<String, List<AckDealOrderInfo>> getDirectAckDealDtlMap(String hboneNo, Set<String> fundCodes);

    /**
     * 查询清盘产品
     *
     * @return 所有清盘产品
     */
    List<String> getCrisisFundList();

    /**
     * 设置代销市值与净值相关信息
     *
     * @param balanceBean          持仓信息
     * @param navBean              净值信息
     * @param ackDealOrderInfoList 确认交易
     */
    void setAgentMarketValueAndNavInfo(BalanceBean balanceBean, HighProductNavBean navBean, List<AckDealOrderInfo> ackDealOrderInfoList);

    /**
     * 设置直销市值与净值相关信息
     *
     * @param hbOneNo              一账通
     * @param balanceBean          持仓信息
     * @param rmbZjj               人民币汇率
     * @param ackDealOrderInfoList 确认订单
     * @param config               小数位配置
     * @param navBean              净值信息
     */
    void setDirectMarketValueAndNavInfo(String hbOneNo, BalanceBean balanceBean, BigDecimal rmbZjj,
                                        List<AckDealOrderInfo> ackDealOrderInfoList,
                                        JjxswConfigModel config, HighProductNavBean navBean);

    /**
     * 设置分红状态
     *
     * @param balanceBean   持仓信息
     * @param navBean       净值信息
     * @param txAcctNo      交易账号
     * @param hboneNo       一账通
     * @param fundNavDivMap 产品净值分红信息
     */
    void processNavDivFlag(BalanceBean balanceBean, HighProductNavBean navBean,
                           String txAcctNo, String hboneNo, Map<String, HighProductNavDivBean> fundNavDivMap);

    /**
     * 设置代销收益信息
     *
     * @param balanceBean     持仓信息
     * @param currentAssetDto 收益信息
     */
    void setAgentBalanceAssetInfo(BalanceBean balanceBean, HighFundAssetIncomeDomain currentAssetDto);

    /**
     * 设置直销收益信息
     *
     * @param balanceBean     持仓信息
     * @param rmbZjj          人民币汇率
     * @param config          字段保留位数配置
     * @param currentAssetDto 收益信息
     */
    void setDirectBalanceAssetInfo(BalanceBean balanceBean, BigDecimal rmbZjj, JjxswConfigModel config, HighFundAssetIncomeDomain currentAssetDto);

    /**
     * 设置持仓年化信息
     *
     * @param balanceBean 持仓信息
     */
    void setYieldIncomeInfo(BalanceBean balanceBean);

    /**
     * 获取当前收益计算状态
     *
     * @param balanceBean    持仓信息
     * @param crisisFundList 清盘产品
     * @return 当前收益计算状态
     */
    String getIncomeCalStatus(BalanceBean balanceBean, List<String> crisisFundList);

    /**
     * 设置持仓信息,不管是否清盘,都必须设置的
     *
     * @param balanceBean     持仓信息
     * @param currentAssetDto 收益信息
     */
    void setAssertWithOutCrisis(BalanceBean balanceBean, HighFundAssetIncomeDomain currentAssetDto);

    /**
     * 设置股权收益信息,不管是否清盘
     *
     * @param balanceBean     持仓信息
     * @param currentAssetDto 收益信息
     */
    void setGuQuanAssertInfoWithOutCrisis(BalanceBean balanceBean, HighFundAssetIncomeDomain currentAssetDto);


    /**
     * 设置类固定收益,不管是否清盘
     *
     * @param balanceBean     持仓信息
     * @param currentAssetDto 收益信息
     */
    void setGuShouAssetInfoWithOutCrisis(BalanceBean balanceBean, HighFundAssetIncomeDomain currentAssetDto);

    /**
     * 设置千禧年产品信息
     *
     * @param qxNetBuyAmtMap 千禧年产品购买净金额
     * @param paidInAmtMap   实缴金额
     * @param qianXiProducts 千禧年产品
     * @param balanceBean    持仓信息
     * @param rmbZjj         人民币汇率
     */
    void setQxFundInfo(Map<String, BigDecimal> qxNetBuyAmtMap, Map<String, BigDecimal> paidInAmtMap, List<String> qianXiProducts, BalanceBean balanceBean, BigDecimal rmbZjj);


    /**
     * 设置起息日
     *
     * @param balanceBean  持仓信息
     * @param valueDateMap 起息日相关信
     */
    void calFixedIncomeValueDate(BalanceBean balanceBean, Map<String, List<HighProductValueDateBean>> valueDateMap);

    /**
     * 买入非香港待确认订单交易记录
     *
     * @param queryBalanceParam 查询入参
     * @return 买入非香港待确认订单交易记录列表
     */
    List<BuyUnConfirmUnHkOrderBean> queryBuyUnConfirmUnHkOrderList(QueryBalanceParam queryBalanceParam);


    /**
     * 待付款非香港待确认订单交易记录
     * @param queryBalanceParam
     * @return 待付款非香港待确认订单交易记录列表
     */
    List<DealOrderVo> queryUnPayUnHkOrderList(QueryBalanceParam queryBalanceParam);
    /**
     * 卖出非香港待确认订单交易记录
     *
     * @param queryBalanceParam 查询入参
     * @return 卖出非香港待确认订单交易记录列表
     */
    List<SellUnConfirmUnHkOrderBean> querySellUnConfirmUnHkOrderList(QueryBalanceParam queryBalanceParam);


    /**
     * 是否需要计算收益等信息
     *
     * @param balanceBean 持仓信息
     * @param fieldMap    特殊产品配置表
     * @return true-不需要计算,false-需要计算
     */
    boolean canNotCountAssert(BalanceBean balanceBean, Map<String, List<HighProductFieldControlBean>> fieldMap);

    /**
     * 产品维度总资产
     *
     * @param balanceBean 持仓信息
     */
    void setFundTotalAssert(BalanceBean balanceBean);


    /**
     * 按照fundCode维度处理持仓列表，将买入待确认、卖出待确认、贷资金到账匹配加入持仓列表
     *
     * @param balanceQueryResult 持仓数据
     */
    List<BalanceBean> buildAndSetBalanceWithRefundAndUnConfirmInfo(BalanceQueryResult balanceQueryResult);


    /**
     * 构建根据买入交易,构建持仓基金bean
     *
     * @param splitFundList  拆单基金
     * @param buyOrderList   购买交易
     * @param productBean    产品信息
     * @param crisisFundList 清盘信息
     * @return 持仓信息
     */
    BalanceBean buildBalanceBeanByBuyOrder(List<String> splitFundList, List<BuyUnConfirmUnHkOrderBean> buyOrderList,
                                           HighProductDBInfoBean productBean, List<String> crisisFundList);

    /**
     * 查询清仓持仓基础信息
     *
     * @param param 查询清仓持仓请求参数
     * @return 清仓持仓基础信息列表
     */
    List<QueryLiquidationBalanceFundInfo> queryLiquidationBalanceFundInfo(QueryBalanceParam param);
}
