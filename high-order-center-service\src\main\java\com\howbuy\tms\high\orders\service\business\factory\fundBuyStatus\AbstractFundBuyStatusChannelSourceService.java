/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.tms.high.orders.service.business.factory.fundBuyStatus;

import com.howbuy.tms.cache.service.AbstractCacheService;
import com.howbuy.tms.high.orders.service.facade.search.querybuyfundstatus.bean.FundBuyStatusDto;
import com.howbuy.tms.high.orders.service.facade.search.querybuyfundstatus.bean.QueryFundBuyStatusParam;

import javax.annotation.Resource;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2025/9/10 14:49
 * @since JDK 1.8
 */
public abstract class AbstractFundBuyStatusChannelSourceService extends AbstractCacheService {

    @Resource
    protected FundBuyStatusLogicFactory fundBuyStatusLogicFactory;
    /**
     * 渠道来源
     * @See
     * @param channelSource 渠道来源
     * @return
     */
    public abstract boolean getChannelSource(String disCode,String channelSource);

    public abstract FundBuyStatusDto getFundBuyStatus(QueryFundBuyStatusParam queryFundBuyStatusParam);


    protected FundBuyStatusDto getFundBuyStatusLogicService(QueryFundBuyStatusParam queryFundBuyStatusParam) {
        AbstractFundBuyStatusLogicService fundBuyStatusLogicService = fundBuyStatusLogicFactory.getFundBuyStatusLogicService(queryFundBuyStatusParam.getDisCode());
        return  fundBuyStatusLogicService.getFundBuyStatus(queryFundBuyStatusParam);
    }

}
