/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.orders.facade.search.querymemberinfo;

import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import com.howbuy.tms.common.validate.MyValidation;
import com.howbuy.tms.high.orders.facade.common.OrderSearchBaseRequest;
import com.howbuy.tms.high.orders.facade.enums.TxCodes;
import com.howbuy.tms.high.orders.facade.search.querymemberinfo.enums.PageSourceEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * @description: 会员信息查询请求参数
 * <AUTHOR>
 * @date 2025/9/9
 * @since JDK 1.8
 */
@Setter
@Getter
public class QueryMemberInfoRequest extends OrderSearchBaseRequest implements Serializable {

    private static final long serialVersionUID = -960656898702661370L;

    /**
     * 构造函数，设置默认交易代码
     */
    public QueryMemberInfoRequest() {
        setTxCode(TxCodes.QUERY_MEMBER_INFO);
    }

    /**
     * 页面来源
     * @see PageSourceEnum
     * 1-我的页面
     * 2-私募持仓页面
     * 3-好臻专区页面
     * 4-好买香港专区页面
     * 5-收益分析页面
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "页面来源", isRequired = false, max = 1)
    private String pageSource;

}
