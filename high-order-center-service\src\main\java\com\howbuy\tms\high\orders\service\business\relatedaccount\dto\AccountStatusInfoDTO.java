/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.tms.high.orders.service.business.relatedaccount.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2025/9/10 17:38
 * @since JDK 1.8
 */
@Setter
@Getter
public class AccountStatusInfoDTO implements Serializable {

    private static final long serialVersionUID = 5049504730458001368L;
    /**
     * 是否有好臻账户,1:是;0:否
     */
    private String hasHzAccount;

    /**
     * 好臻账户是否已激活,1:是;0:否
     */
    private String hasHzAccountActive;

    /**
     * 是否有好买账户,1:是;0:否
     */
    private String hasHmAccount;

    /**
     * 好买账户是否已激活,1:是;0:否
     */
    private String hasHmAccountActive;
}
