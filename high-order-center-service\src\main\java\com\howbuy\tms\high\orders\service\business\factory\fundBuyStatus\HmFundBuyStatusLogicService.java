package com.howbuy.tms.high.orders.service.business.factory.fundBuyStatus;

import com.alibaba.fastjson.JSON;
import com.howbuy.interlayer.product.model.HighProductInfoModel;
import com.howbuy.interlayer.product.model.HighProductLockWhithListModel;
import com.howbuy.tms.common.enums.busi.BuyStatusTypeEnum;
import com.howbuy.tms.common.enums.busi.DisCodeEnum;
import com.howbuy.tms.common.enums.busi.FundBuyStatusEnum;
import com.howbuy.tms.common.enums.busi.YesOrNoEnum;
import com.howbuy.tms.common.outerservice.acccenter.querycustInfo.QueryCustInfoResult;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductInfoBean;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductLockWhithListBean;
import com.howbuy.tms.high.orders.service.common.enums.FundBuyStatusNotCanbuyCodeEnum;
import com.howbuy.tms.high.orders.service.facade.search.querybuyfundstatus.bean.CustomerInfoCommand;
import com.howbuy.tms.high.orders.service.facade.search.querybuyfundstatus.bean.FundBuyStatusDto;
import com.howbuy.tms.high.orders.service.facade.search.querybuyfundstatus.bean.QueryFundBuyStatusListParam;
import com.howbuy.tms.high.orders.service.facade.search.querybuyfundstatus.bean.QueryFundBuyStatusParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

/**
 * @Description:好买产品购买状态核心逻辑类
 * @Author: yun.lu
 * Date: 2023/10/26 9:24
 */
@Service
public class HmFundBuyStatusLogicService extends AbstractFundBuyStatusLogicService {
    Logger log = LoggerFactory.getLogger(HmFundBuyStatusLogicService.class);

    @Override
    public String getDisCode() {
        return DisCodeEnum.HM.getCode();
    }

    @Override
    public boolean isWithe(String hbOneNo, QueryFundBuyStatusParam queryFundBuyStatusParam, HighProductInfoModel highProductInfoBean) {
        // 高端产品白名单
        HighProductLockWhithListModel lockWhiteBean = queryFundBuyStatusParam.getHighProductCanBuyInfoModel().getHighProductLockWhithListModel();
        return lockWhiteBean!=null;
    }


    @Override
    public FundBuyStatusDto getFundBuyStatus(QueryFundBuyStatusParam queryFundBuyStatusParam) {
        FundBuyStatusDto fundBuyStatusDto = super.getFundBuyStatus(queryFundBuyStatusParam);
        // 如果不可以购买,就直接返回
        if (YesOrNoEnum.NO.getCode().equals(fundBuyStatusDto.getFundBuyStatusEnum().getCanBuy())) {
            return fundBuyStatusDto;
        }

        // 年龄校验
        HighProductInfoModel highProductInfoModel  = queryFundBuyStatusParam.getHighProductCanBuyInfoModel().getHighProductInfoModel();
        HighProductInfoBean highProductInfoBean = new HighProductInfoBean();
        BeanUtils.copyProperties(highProductInfoModel, highProductInfoBean);
        QueryCustInfoResult customerInfo = queryFundBuyStatusParam.getCustInfo();
        CustomerInfoCommand customerInfoCommand = null;
        if (customerInfo != null) {
            customerInfoCommand = new CustomerInfoCommand();
            BeanUtils.copyProperties(customerInfo, customerInfoCommand);
        }
        if (!validAgeLimit(highProductInfoBean, queryFundBuyStatusParam.getTxChannel(), customerInfoCommand)) {
            fundBuyStatusDto.setFundBuyStatusEnum(FundBuyStatusEnum.CAN_NOT_BUY);
            fundBuyStatusDto.setFundBuyStatusNotCanbuyCodeEnum(FundBuyStatusNotCanbuyCodeEnum.CUST_NOT_IN_PRODUCT_AGE_LIMIT);
            fundBuyStatusDto.setBuyStatusType(BuyStatusTypeEnum.LIMIT_AGE.getCode());
            fundBuyStatusDto.setMsg("年龄校验不通过");
            log.info("AbstractFundBuyStatusLogicService-getFundBuyStatus,获取产品购买状态-年龄校验不通过,fundBuyStatusParam={},fundBuyStatusDto={}", JSON.toJSONString(queryFundBuyStatusParam), JSON.toJSONString(fundBuyStatusDto));
            return fundBuyStatusDto;
        }
        return fundBuyStatusDto;
    }
}
