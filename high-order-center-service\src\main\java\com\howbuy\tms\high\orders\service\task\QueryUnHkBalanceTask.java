package com.howbuy.tms.high.orders.service.task;

import com.alibaba.fastjson.JSON;
import com.howbuy.tms.common.constant.ExceptionCodes;
import com.howbuy.tms.common.enums.busi.DisCodeEnum;
import com.howbuy.tms.common.enums.busi.YesOrNoEnum;
import com.howbuy.tms.high.orders.facade.search.queryacctbalance.QueryAcctBalanceFacade;
import com.howbuy.tms.high.orders.facade.search.queryacctbalance.QueryAcctBalanceRequest;
import com.howbuy.tms.high.orders.facade.search.queryacctbalance.QueryAcctBalanceResponse;
import com.howbuy.tms.high.orders.facade.search.queryacctbalancewithouthk.QueryAcctBalanceWithoutHkFacade;
import com.howbuy.tms.high.orders.facade.search.queryacctbalancewithouthk.QueryAcctBalanceWithoutHkRequest;
import com.howbuy.tms.high.orders.service.facade.search.queryacctbalanceNew.QueryBalanceContext;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.BeanUtils;

import java.util.Collections;

/**
 * @Description:非海外持仓
 * @Author: yun.lu
 * Date: 2025/8/1 18:18
 */
@Getter
@Setter
@AllArgsConstructor
public class QueryUnHkBalanceTask extends HowbuyBaseTask {
    private static final Logger log = LogManager.getLogger(QueryUnHkBalanceTask.class);
    private QueryBalanceContext queryBalanceContext;
    private QueryAcctBalanceRequest request;
    private Boolean hkProduct;
    private QueryAcctBalanceWithoutHkFacade queryAcctBalanceWithoutHkFacade;
    private static final String HZ_PRODUCT_SUB_TYPE="5";

    @Override
    protected void callTask() {
        log.info("queryUnHkAcctBalance-查询非海外持仓接口,queryBalanceParamCmd={}", JSON.toJSONString(request));
        if (hkProduct != null && hkProduct) {
            log.info("查询持仓的产品有值,而且是香港产品,不需要查询非香港持仓接口");
            QueryAcctBalanceResponse unHkBalanceResponse = new QueryAcctBalanceResponse();
            unHkBalanceResponse.setReturnCode(ExceptionCodes.SUCCESS);
            queryBalanceContext.setUnHkBalance(unHkBalanceResponse);
            return;
        }
        log.info("queryUnHkAcctBalance-查询非海外持仓接口-start,queryAcctBalanceRequest={}", JSON.toJSONString(request));
        QueryAcctBalanceWithoutHkRequest queryAcctBalanceRequest = new QueryAcctBalanceWithoutHkRequest();
        BeanUtils.copyProperties(request, queryAcctBalanceRequest);
        // 兼容老页面,固定查询好臻传参
        if (HZ_PRODUCT_SUB_TYPE.equals(request.getProductSubType()) && YesOrNoEnum.NO.getCode().equals(request.getHkSaleFlag())) {
            queryAcctBalanceRequest.setDisCodeList(Collections.singletonList(DisCodeEnum.HZ.getCode()));
        }
        queryAcctBalanceRequest.setQueryWithAllRedeemWaiteRefund(request.getQueryWithAllRedeemWaiteRefund());
        QueryAcctBalanceResponse response = queryAcctBalanceWithoutHkFacade.execute(queryAcctBalanceRequest);
        log.info("queryUnHkAcctBalance-查询非海外持仓接口-结果,response={}", JSON.toJSONString(response));
        queryBalanceContext.setUnHkBalance(response);
    }




}
