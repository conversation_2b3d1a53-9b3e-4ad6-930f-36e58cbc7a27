/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.tms.high.orders.service.service.queryfundpositiondate;

import com.howbuy.tms.common.constant.ExceptionCodes;
import com.howbuy.tms.common.enums.busi.BusinessCodeEnum;
import com.howbuy.tms.common.enums.busi.DisCodeEnum;
import com.howbuy.tms.common.enums.busi.YesOrNoEnum;
import com.howbuy.tms.common.message.MessageSource;
import com.howbuy.tms.common.outerservice.cc.center.queryhboneno.QueryHbOneNoOuterService;
import com.howbuy.tms.common.outerservice.dtms.queryholdFundByFundTxAcctNo.QueryFundPositionDateOuterService;
import com.howbuy.tms.common.outerservice.dtms.queryholdFundByFundTxAcctNo.request.QueryFundPositionDateByHbOneNoRequestDTO;
import com.howbuy.tms.common.outerservice.dtms.queryholdFundByFundTxAcctNo.response.QueryFundPositionDateByHbOneNoResponseDTO;
import com.howbuy.tms.high.orders.dao.vo.QueryFundPositionFirstBuyDateVO;
import com.howbuy.tms.high.orders.dao.vo.QueryFundPositionLastSellDateVO;
import com.howbuy.tms.high.orders.facade.search.queryfundpositiondate.QueryFundPositionDateByFundCodeRequest;
import com.howbuy.tms.high.orders.facade.search.queryfundpositiondate.QueryFundPositionDateByFundCodeResponse;
import com.howbuy.tms.high.orders.facade.search.queryfundpositiondate.QueryFundPositionDateRequest;
import com.howbuy.tms.high.orders.facade.search.queryfundpositiondate.QueryFundPositionDateResponse;
import com.howbuy.tms.high.orders.service.common.enums.queryfundliquidation.DataChannelEnum;
import com.howbuy.tms.high.orders.service.common.enums.queryfundliquidation.HoldStatusEnum;
import com.howbuy.tms.high.orders.service.repository.HighDealOrderDtlRepository;
import com.howbuy.tms.high.orders.service.service.acctbalancebaseinfo.AcctBalanceBaseInfoService;
import com.howbuy.tms.high.orders.service.service.acctbalancebaseinfo.bean.CustConfirmBalanceDto;
import com.howbuy.tms.high.orders.service.service.acctbalancebaseinfo.bean.QueryBalanceParam;
import com.howbuy.tms.high.orders.service.service.acctbalancebaseinfo.bean.QueryLiquidationBalanceFundInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description: 基金持仓/清仓日期查询服务
 * @date 2025/9/11 13:37
 * @since JDK 1.8
 */
@Service
@Slf4j
public class QueryFundPositionDateService {

    @Resource
    private QueryFundPositionDateOuterService queryFundPositionDateOuterService;

    @Resource
    private AcctBalanceBaseInfoService acctBalanceBaseInfoService;

    @Resource
    private QueryHbOneNoOuterService queryHbOneNoOuterService;

    @Resource
    private HighDealOrderDtlRepository highDealOrderDtlRepository;

    /**
     * 获取买入类业务编码
     *
     * @return 买入类业务编码列表
     */
    private static List<String> getBuyBusinessCodes() {
        return Arrays.asList(
                BusinessCodeEnum.SUBS.getMCode(),                    // 1120-认购
                BusinessCodeEnum.PURCHASE.getMCode(),               // 1122-申购
                BusinessCodeEnum.NOTRADE_OVERACCOUNT_IN.getMCode(), // 1134-非交易过户转入
                BusinessCodeEnum.FORCE_ADD.getMCode(),              // 1144-强增
                BusinessCodeEnum.SERIES_MERGE_IN.getMCode(),        // 113C-系列合并转入
                BusinessCodeEnum.TRADE_TRANSFER_APPLY.getMCode(),   // 119A-交易过户申请
                BusinessCodeEnum.TRADE_TRANSFER_PURCHASE.getMCode(),// 119C-交易过户申购
                BusinessCodeEnum.FUND_EXCHANGE.getMCode(),          // 1136-基金转换
                BusinessCodeEnum.ACTUAL_PAID.getMCode()             // 112B-实缴
        );
    }

    /**
     * 获取卖出类业务编码
     *
     * @return 卖出类业务编码列表
     */
    private static List<String> getSellBusinessCodes() {
        return Arrays.asList(
                BusinessCodeEnum.CFM_FUNDCLR.getMCode(),            // 1150-基金清盘
                BusinessCodeEnum.REDEEM.getMCode(),                 // 1124-赎回
                BusinessCodeEnum.NOTRADE_OVERACCOUNT_OUT.getMCode(),// 1135-非交易过户转出
                BusinessCodeEnum.FORCE_REDEEM.getMCode(),           // 1142-强行赎回
                BusinessCodeEnum.FORCE_SUBTRACT.getMCode(),         // 1145-强行调减
                BusinessCodeEnum.FUND_TERMINATE.getMCode(),         // 1151-基金终止
                BusinessCodeEnum.TRADE_TRANSFER_REDEEM.getMCode(),  // 119B-交易过户赎回
                BusinessCodeEnum.SERIES_MERGE_OUT.getMCode()        // 113B-系列合并转出
        );
    }

    /**
     * @param request 请求参数
     * @return com.howbuy.tms.high.orders.facade.search.queryfundpositiondate.QueryFundPositionDateByFundCodeResponse
     * @description: 根据基金代码查询基金的仓位持有日期信息
     * @author: jinqing.rao
     * @date: 2025/9/11 13:37
     * @since JDK 1.8
     */
    public QueryFundPositionDateByFundCodeResponse queryFundPositionDateByFundCode(QueryFundPositionDateByFundCodeRequest request) {
        // 1. 构建响应结果
        QueryFundPositionDateByFundCodeResponse response = buildQueryFundPositionDateByFundCodeResponse();
        // 2. 获取持仓/清仓日期信息 默认好买,好臻
        List<String> disCodeList = Arrays.asList(DisCodeEnum.HM.getCode(), DisCodeEnum.HZ.getCode());

        QueryFundPositionResult queryFundPositionResult = queryHmFundPositionDateByHbOneNoResponseDTO(request.getFundCode(), request.getHbOneNo(), disCodeList);

        response.setFundCode(request.getFundCode());
        if (CollectionUtils.isNotEmpty(queryFundPositionResult.getHoldFundInfoList())) {
            response.setFirstHoldDate(queryFundPositionResult.getHoldFundInfoList().get(0).getFirstHoldDate());
            response.setFirstHoldDealNo(queryFundPositionResult.getHoldFundInfoList().get(0).getFirstHoldDealNo());
        }
        if (CollectionUtils.isNotEmpty(queryFundPositionResult.getClearFundInfoList())) {
            response.setFirstHoldDate(queryFundPositionResult.getClearFundInfoList().get(0).getFirstHoldDate());
            response.setLiquidationDate(queryFundPositionResult.getClearFundInfoList().get(0).getLiquidationDate());
            response.setLiquidationDealNo(queryFundPositionResult.getClearFundInfoList().get(0).getLiquidationDealNo());
        }
        return response;
    }

    private static QueryFundPositionDateByFundCodeResponse buildQueryFundPositionDateByFundCodeResponse() {
        QueryFundPositionDateByFundCodeResponse response = new QueryFundPositionDateByFundCodeResponse();
        // 默认成功状态
        response.setReturnCode(ExceptionCodes.SUCCESS);
        response.setDescription(MessageSource.getMessageByCode(ExceptionCodes.SUCCESS));
        return response;
    }

    /**
     * @param fundCode 基金编码
     * @param hbOneNo  一账通号
     * @return com.howbuy.tms.common.outerservice.dtms.queryholdFundByFundTxAcctNo.response.QueryFundPositionDateByHbOneNoResponseDTO
     * @description: 查询海外基金的初始持仓日期和清仓结束日期
     * @author: jinqing.rao
     * @date: 2025/9/11 19:26
     * @since JDK 1.8
     */
    private QueryFundPositionDateByHbOneNoResponseDTO queryHwFundPositionDateByHbOneNoResponseDTO(String fundCode, String hbOneNo) {
        QueryFundPositionDateByHbOneNoRequestDTO acctNoRequestDTO = new QueryFundPositionDateByHbOneNoRequestDTO();
        acctNoRequestDTO.setFundCode(fundCode);
        acctNoRequestDTO.setHbOneNo(hbOneNo);
        return queryFundPositionDateOuterService.queryFundPositionDateByHbOneNo(acctNoRequestDTO);
    }

    /**
     * 根据一账通号查询基金产品持仓/清仓日期信息
     *
     * @param request 查询请求
     * @return 查询响应
     */
    public QueryFundPositionDateResponse queryFundPositionDate(QueryFundPositionDateRequest request) {
        log.info("查询基金产品持仓/清仓日期开始, request: {}", request);
        // 1.构建成功的响应体
        QueryFundPositionDateResponse response = buildSuccessQueryFundPositionDateResponse();
        // 2.获取分销渠道,海外特殊,不是通过渠道号判断的(没有海外渠道分销号)
        List<String> disCodeList = getDisCodeList(request);
        // 3.是否查询海外渠道
        boolean queryOverseasFlag = DataChannelEnum.OVERSEAS.getCode().equals(request.getDataChannel());
        String searchOverseas = queryOverseasFlag ? YesOrNoEnum.YES.getCode() : YesOrNoEnum.NO.getCode();
        // 4.查询清仓和持仓信息
        QueryFundDateResult allResult = getQueryFundDateResult(request, disCodeList, searchOverseas);
        // 5.数据封装
        response.setHoldPositionDateList(allResult.getHoldDateList());
        response.setLiquidationPositionDateList(allResult.getLiquidationDateList());
        return response;
    }

    /**
     * @param request
     * @param disCodeList
     * @return com.howbuy.tms.high.orders.service.service.queryfundpositiondate.QueryFundPositionDateService.QueryFundDateResult
     * @description: 查询清仓和持仓信息
     * @author: jinqing.rao
     * @date: 2025/9/17 14:53
     * @since JDK 1.8
     */
    private QueryFundDateResult getQueryFundDateResult(QueryFundPositionDateRequest request, List<String> disCodeList, String searchOverseas) {
        QueryFundDateResult allResult = new QueryFundDateResult();

        QueryFundDateResult channelResult = queryFundDateByDisCodeList(request.getHbOneNo(), disCodeList, request.getHoldStatus(), searchOverseas);

        allResult.getHoldDateList().addAll(channelResult.getHoldDateList());
        allResult.getLiquidationDateList().addAll(channelResult.getLiquidationDateList());
        return allResult;
    }

    /**
     * @param request
     * @return java.util.List<java.lang.String>
     * @description: 获取分销渠道
     * @author: jinqing.rao
     * @date: 2025/9/17 14:50
     * @since JDK 1.8
     */
    private static List<String> getDisCodeList(QueryFundPositionDateRequest request) {
        // 不传分销渠道，默认查询好买、好臻、OTC 分销渠道
        if (StringUtils.isBlank(request.getDataChannel())) {
            return Arrays.asList(DisCodeEnum.HM.getCode(), DisCodeEnum.HZ.getCode(), DisCodeEnum.OTC.getCode());
        }
        if (DataChannelEnum.HOWBUY.getCode().equals(request.getDataChannel())) {
            return Arrays.asList(DisCodeEnum.HM.getCode(), DisCodeEnum.OTC.getCode());
        }
        if (DataChannelEnum.HAOZHEN.getCode().equals(request.getDataChannel())) {
            return Collections.singletonList(DisCodeEnum.HM.getCode());
        }
        return new ArrayList<>();
    }

    /**
     * @param
     * @return com.howbuy.tms.high.orders.facade.search.queryfundpositiondate.QueryFundPositionDateResponse
     * @description: 构建成功的响应体
     * @author: jinqing.rao
     * @date: 2025/9/17 14:47
     * @since JDK 1.8
     */
    private static QueryFundPositionDateResponse buildSuccessQueryFundPositionDateResponse() {
        QueryFundPositionDateResponse response = new QueryFundPositionDateResponse();
        // 默认成功状态
        response.setReturnCode(ExceptionCodes.SUCCESS);
        response.setDescription(MessageSource.getMessageByCode(ExceptionCodes.SUCCESS));
        return response;
    }

    /**
     * 根据数据渠道和持仓状态查询基金产品日期信息
     *
     * @param hbOneNo     一账通号
     * @param disCodeList 分销渠道
     * @param holdStatus  持仓状态
     * @return 基金产品日期信息结果
     */
    private QueryFundDateResult queryFundDateByDisCodeList(String hbOneNo, List<String> disCodeList, String holdStatus, String searchOverseas) {
        QueryFundDateResult result = new QueryFundDateResult();

        QueryFundDateResult hmFundDateResult = queryHowbuyFundDate(hbOneNo, disCodeList, holdStatus);

        QueryFundDateResult hwFundDateResult = new QueryFundDateResult();
        if (YesOrNoEnum.YES.getCode().equals(searchOverseas)) {
            hwFundDateResult = queryOverseasFundDate(hbOneNo, holdStatus);
        }
        // 持仓数据信息
        List<QueryFundPositionDateResponse.FundDateInfo> holdDateList = result.getHoldDateList();
        holdDateList.addAll(hmFundDateResult.getHoldDateList());
        holdDateList.addAll(hwFundDateResult.getHoldDateList());
        // 清仓数据信息
        List<QueryFundPositionDateResponse.FundDateInfo> liquidationDateList = result.getLiquidationDateList();
        liquidationDateList.addAll(hmFundDateResult.getLiquidationDateList());
        liquidationDateList.addAll(hwFundDateResult.getLiquidationDateList());
        return result;
    }

    /**
     * 查询好买渠道基金产品日期信息
     *
     * @param hbOneNo    一账通号
     * @param holdStatus 持仓状态
     * @return 基金产品日期信息结果
     */
    private QueryFundDateResult queryHowbuyFundDate(String hbOneNo, List<String> disCodeList, String holdStatus) {
        QueryFundDateResult queryFundDateResult = new QueryFundDateResult();
        QueryFundPositionResult result =
                queryHmFundPositionDateByHoldStatus(holdStatus,null, hbOneNo, disCodeList);
        queryFundDateResult.setHoldDateList(result.getHoldFundInfoList());
        queryFundDateResult.setLiquidationDateList(result.getClearFundInfoList());
        return queryFundDateResult;
    }

    /**
     * @param fundCode 基金编码
     * @param hbOneNo  一账通号
     * @return QueryFundPositionDateByHbOneNoResponseDTO
     * @description: 查询好买渠道基金持仓和清仓日期信息
     * @author: jinqing.rao
     * @date: 2025/9/12 17:21
     * @since JDK 1.8
     */
    private QueryFundPositionResult queryHmFundPositionDateByHbOneNoResponseDTO(String fundCode, String hbOneNo, List<String> disCodeList) {
        QueryFundPositionResult result = new QueryFundPositionResult();
        try {
            // 获取交易账号
            String txAcctNo = queryHbOneNoOuterService.queryTxAcctNoByHbOneNo(hbOneNo);

            // 1.查询所有持仓信息
            List<CustConfirmBalanceDto> custConfirmBalanceDtoList = queryAllBalanceInfo(fundCode, hbOneNo, txAcctNo, disCodeList);

            // 2. 获取清仓的代码信息
            List<String> liquidationFundCodeList = queryLiquidationFundCodeList(txAcctNo, hbOneNo, fundCode, disCodeList);

            if (CollectionUtils.isEmpty(custConfirmBalanceDtoList) && CollectionUtils.isEmpty(liquidationFundCodeList)) {
                log.info("queryHmFundPositionDateByHbOneNoResponseDTO >>> 没有仓位产品信息, fundCode : {} hbOneNo :{}", fundCode, hbOneNo);
                return result;
            }
            // 查询基金
            queryFundPositionDateByFundCodeList(fundCode, custConfirmBalanceDtoList, liquidationFundCodeList, txAcctNo);

        } catch (Exception e) {
            log.error("查询好买渠道基金日期信息异常, hbOneNo: {}, error: {}", hbOneNo, e.getMessage(), e);
        }

        return result;
    }

    private QueryFundPositionResult queryHmFundPositionDateByHoldStatus(String foldStatus, String fundCode, String hbOneNo, List<String> disCodeList) {
        QueryFundPositionResult result = new QueryFundPositionResult();
        try {
            // 获取交易账号
            String txAcctNo = queryHbOneNoOuterService.queryTxAcctNoByHbOneNo(hbOneNo);
            // 1.查询所有持仓信息
            List<CustConfirmBalanceDto> custConfirmBalanceDtoList = new ArrayList<>();
            // 2. 获取清仓的代码信息
            List<String> liquidationFundCodeList = new ArrayList<>();
            if (StringUtils.isBlank(foldStatus)) {
                custConfirmBalanceDtoList = queryAllBalanceInfo(fundCode, hbOneNo, txAcctNo, disCodeList);
                liquidationFundCodeList = queryLiquidationFundCodeList(txAcctNo, hbOneNo, fundCode, disCodeList);
            }
            if(HoldStatusEnum.HOLDING.getCode().equals(foldStatus)){
                custConfirmBalanceDtoList = queryAllBalanceInfo(fundCode, hbOneNo, txAcctNo, disCodeList);
            }
            if(HoldStatusEnum.LIQUIDATION.getCode().equals(foldStatus)){
                liquidationFundCodeList = queryLiquidationFundCodeList(txAcctNo, hbOneNo, fundCode, disCodeList);
            }
            if (CollectionUtils.isEmpty(custConfirmBalanceDtoList) && CollectionUtils.isEmpty(liquidationFundCodeList)) {
                log.info("queryHmFundPositionDateByHbOneNoResponseDTO >>> 没有仓位产品信息, fundCode : {} hbOneNo :{}", fundCode, hbOneNo);
                return result;
            }
            // 查询基金
            queryFundPositionDateByFundCodeList(fundCode, custConfirmBalanceDtoList, liquidationFundCodeList, txAcctNo);

        } catch (Exception e) {
            log.error("查询好买渠道基金日期信息异常, hbOneNo: {}, error: {}", hbOneNo, e.getMessage(), e);
        }

        return result;
    }

    private void queryFundPositionDateByFundCodeList(String fundCode, List<CustConfirmBalanceDto> custConfirmBalanceDtoList, List<String> liquidationFundCodeList, String txAcctNo) {
        QueryFundPositionResult result = new QueryFundPositionResult();
        List<String> holdingFundCodeList = new ArrayList<>();
        // 如果指定了基金编码，则过滤
        if (StringUtils.isNotBlank(fundCode)) {
            // 持仓产品过滤
            holdingFundCodeList = getHoldingFundCodeList(fundCode, custConfirmBalanceDtoList);
            // 过滤清仓基金编码
            liquidationFundCodeList = getLiquidationFundCodeList(fundCode, liquidationFundCodeList);
        }
        // 3.查询持仓基金的首笔交易日期
        List<QueryFundPositionDateResponse.FundDateInfo> holdFundInfoList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(holdingFundCodeList)) {
            holdFundInfoList = queryFirstBuyTradeDate(txAcctNo, holdingFundCodeList);
        }

        // 4.查询清仓基金的最后清仓日期（使用聚合后的清仓基金编码）
        List<QueryFundPositionDateResponse.FundDateInfo> clearFundInfoList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(liquidationFundCodeList)) {
            clearFundInfoList = queryLastSellTradeDate(txAcctNo, liquidationFundCodeList);
        }

        result.setHoldFundInfoList(holdFundInfoList);
        result.setClearFundInfoList(clearFundInfoList);
    }

    /**
     * @param fundCode                基金编码
     * @param liquidationFundCodeList 清仓数据
     * @return java.util.List<java.lang.String>
     * @description: 筛选取清仓代码
     * @author: jinqing.rao
     * @date: 2025/9/15 17:07
     * @since JDK 1.8
     */
    private static List<String> getLiquidationFundCodeList(String fundCode, List<String> liquidationFundCodeList) {
        List<String> holdingFundCodeList = new ArrayList<>();
        if (StringUtils.isBlank(fundCode)) {
            return holdingFundCodeList;
        }
        if (CollectionUtils.isEmpty(liquidationFundCodeList)) {
            return holdingFundCodeList;
        }
        liquidationFundCodeList = liquidationFundCodeList.stream()
                .filter(code -> code.equals(fundCode))
                .collect(Collectors.toList());
        return liquidationFundCodeList;
    }

    /**
     * @param fundCode                  基金编码
     * @param custConfirmBalanceDtoList 持仓产品集合
     * @return java.util.List<java.lang.String>
     * @description: 持仓产品过滤
     * @author: jinqing.rao
     * @date: 2025/9/15 17:04
     * @since JDK 1.8
     */
    private static List<String> getHoldingFundCodeList(String fundCode, List<CustConfirmBalanceDto> custConfirmBalanceDtoList) {
        List<String> holdingFundCodeList = new ArrayList<>();
        if (StringUtils.isBlank(fundCode)) {
            return holdingFundCodeList;
        }
        if (CollectionUtils.isEmpty(custConfirmBalanceDtoList)) {
            return holdingFundCodeList;
        }
        // 根据持仓信息，分离持仓大于0的基金编码
        holdingFundCodeList = custConfirmBalanceDtoList.stream()
                .filter(balance -> balance.getBalanceVol().compareTo(BigDecimal.ZERO) > 0)
                .map(CustConfirmBalanceDto::getFundCode)
                .filter(code -> code.equals(fundCode))
                .distinct()
                .collect(Collectors.toList());
        return holdingFundCodeList;
    }

    /**
     * 查询所有持仓信息（
     * 根据一账通查询用户的全部持仓信息，包括当前持仓和已清仓的基金
     */
    private List<CustConfirmBalanceDto> queryAllBalanceInfo(String fundCode, String hbOneNo, String txAcctNo, List<String> disCodeList) {

        return acctBalanceBaseInfoService.queryUnHkCustConfirmBalance(
                buildQueryBalanceParam(fundCode, hbOneNo, txAcctNo, disCodeList));

    }

    /**
     * 查询首笔买入交易日期
     */
    private List<QueryFundPositionDateResponse.FundDateInfo> queryFirstBuyTradeDate(String txAcctNo, List<String> fundCodes) {
        List<QueryFundPositionDateResponse.FundDateInfo> holdFundInfoList = new ArrayList<>();

        // 使用静态方法获取买入类业务编码
        List<String> buyBusinessCodes = getBuyBusinessCodes();

        for (String fundCode : fundCodes) {
            // 查询该基金的首笔买入订单
            QueryFundPositionFirstBuyDateVO firstBuyDate = highDealOrderDtlRepository.queryFirstBuyTradeDate(
                    txAcctNo, fundCode, buyBusinessCodes);

            if (firstBuyDate != null) {
                QueryFundPositionDateResponse.FundDateInfo holdFundInfo =
                        new QueryFundPositionDateResponse.FundDateInfo();
                holdFundInfo.setFundCode(fundCode);
                holdFundInfo.setFirstHoldDate(firstBuyDate.getFirstBuyDate());
                holdFundInfo.setFirstHoldDealNo(firstBuyDate.getDealNo());
                holdFundInfoList.add(holdFundInfo);
            }
        }

        return holdFundInfoList;
    }

    /**
     * 查询最后卖出交易日期
     */
    private List<QueryFundPositionDateResponse.FundDateInfo> queryLastSellTradeDate(String txAcctNo, List<String> fundCodes) {
        List<QueryFundPositionDateResponse.FundDateInfo> clearFundInfoList = new ArrayList<>();

        // 使用静态方法获取卖出类业务编码
        List<String> sellBusinessCodes = getSellBusinessCodes();
        // 使用静态方法获取买入类业务编码（用于查询首笔买入日期）
        List<String> buyBusinessCodes = getBuyBusinessCodes();

        for (String fundCode : fundCodes) {
            // 查询该基金的首笔买入日期
            QueryFundPositionFirstBuyDateVO firstBuyDate = highDealOrderDtlRepository.queryFirstBuyTradeDate(
                    txAcctNo, fundCode, buyBusinessCodes);

            // 查询该基金的最后卖出订单
            QueryFundPositionLastSellDateVO lastSellDate = highDealOrderDtlRepository.queryLastSellTradeDate(
                    txAcctNo, fundCode, sellBusinessCodes);

            if (firstBuyDate != null && lastSellDate != null) {
                QueryFundPositionDateResponse.FundDateInfo clearFundInfo =
                        new QueryFundPositionDateResponse.FundDateInfo();
                clearFundInfo.setFundCode(fundCode);
                clearFundInfo.setFirstHoldDate(firstBuyDate.getFirstBuyDate());
                clearFundInfo.setLiquidationDate(lastSellDate.getLastSellDate());
                clearFundInfo.setFirstHoldDealNo(firstBuyDate.getDealNo());
                clearFundInfo.setLiquidationDealNo(lastSellDate.getDealNo());
                clearFundInfoList.add(clearFundInfo);
            }
        }

        return clearFundInfoList;
    }

    /**
     * 转换参数类型
     */
    private QueryBalanceParam buildQueryBalanceParam(String fundCode, String hbOneNo, String txAcctNo, List<String> disCodeList) {
        QueryBalanceParam baseParam = new QueryBalanceParam();
        baseParam.setFundCode(fundCode);
        baseParam.setTxAcctNo(txAcctNo);
        baseParam.setHboneNo(hbOneNo);
        baseParam.setDisCodeList(disCodeList);
        return baseParam;
    }

    /**
     * 查询海外渠道基金产品日期信息
     *
     * @param hbOneNo    一账通号
     * @param holdStatus 持仓状态
     * @return 基金产品日期信息结果
     */
    private QueryFundDateResult queryOverseasFundDate(String hbOneNo, String holdStatus) {
        QueryFundDateResult queryFundDateResult = new QueryFundDateResult();

        QueryFundPositionDateByHbOneNoResponseDTO queryFundPositionDateByHbOneNoResponseDTO = queryHwFundPositionDateByHbOneNoResponseDTO(null, hbOneNo);
        queryFundDateResult.setHoldDateList(buildHoldDateList(queryFundPositionDateByHbOneNoResponseDTO.getHoldfundCodeList()));
        queryFundDateResult.setLiquidationDateList(buildLiquidationDateList(queryFundPositionDateByHbOneNoResponseDTO.getClearfundCodeList()));
        return new QueryFundDateResult();
    }

    /**
     * @param holdfundCodeList
     * @return java.util.List<com.howbuy.tms.high.orders.facade.search.queryfundpositiondate.QueryFundPositionDateResponse.FundDateInfo>
     * @description: 构建持仓信息
     * @author: jinqing.rao
     * @date: 2025/9/11 19:46
     * @since JDK 1.8
     */
    private List<QueryFundPositionDateResponse.FundDateInfo> buildHoldDateList(List<QueryFundPositionDateByHbOneNoResponseDTO.HoldFundInfo> holdfundCodeList) {

        if (CollectionUtils.isEmpty(holdfundCodeList)) {
            return new ArrayList<>();
        }
        return holdfundCodeList.stream().map(f -> {
            QueryFundPositionDateResponse.FundDateInfo fundDateInfo = new QueryFundPositionDateResponse.FundDateInfo();
            fundDateInfo.setFundCode(f.getFundCode());
            fundDateInfo.setFirstHoldDate(f.getFirstBuyTradeDt());
            return fundDateInfo;
        }).collect(Collectors.toList());

    }

    /**
     * @param clearFundInfoList
     * @return java.util.List<com.howbuy.tms.high.orders.facade.search.queryfundpositiondate.QueryFundPositionDateResponse.FundDateInfo>
     * @description: 构建清仓信息
     * @author: jinqing.rao
     * @date: 2025/9/11 19:45
     * @since JDK 1.8
     */
    private List<QueryFundPositionDateResponse.FundDateInfo> buildLiquidationDateList(List<QueryFundPositionDateByHbOneNoResponseDTO.ClearFundInfo> clearFundInfoList) {

        if (CollectionUtils.isEmpty(clearFundInfoList)) {
            return new ArrayList<>();
        }
        return clearFundInfoList.stream().map(f -> {
            QueryFundPositionDateResponse.FundDateInfo fundDateInfo = new QueryFundPositionDateResponse.FundDateInfo();
            fundDateInfo.setFundCode(f.getFundCode());
            fundDateInfo.setFirstHoldDate(f.getFirstBuyTradeDt());
            fundDateInfo.setLiquidationDate(f.getLastSellTradeDt());
            return fundDateInfo;
        }).collect(Collectors.toList());
    }

    /**
     * 基金产品日期查询结果内部类
     */
    private static class QueryFundDateResult {
        private List<QueryFundPositionDateResponse.FundDateInfo> holdDateList = new ArrayList<>();
        private List<QueryFundPositionDateResponse.FundDateInfo> liquidationDateList = new ArrayList<>();

        public List<QueryFundPositionDateResponse.FundDateInfo> getHoldDateList() {
            return holdDateList;
        }

        public void setHoldDateList(List<QueryFundPositionDateResponse.FundDateInfo> holdDateList) {
            this.holdDateList = holdDateList;
        }

        public List<QueryFundPositionDateResponse.FundDateInfo> getLiquidationDateList() {
            return liquidationDateList;
        }

        public void setLiquidationDateList(List<QueryFundPositionDateResponse.FundDateInfo> liquidationDateList) {
            this.liquidationDateList = liquidationDateList;
        }
    }

    /**
     * 基金位置查询结果内部类
     */
    private static class QueryFundPositionResult {
        private List<QueryFundPositionDateResponse.FundDateInfo> holdFundInfoList = new ArrayList<>();
        private List<QueryFundPositionDateResponse.FundDateInfo> clearFundInfoList = new ArrayList<>();

        public List<QueryFundPositionDateResponse.FundDateInfo> getHoldFundInfoList() {
            return holdFundInfoList;
        }

        public void setHoldFundInfoList(List<QueryFundPositionDateResponse.FundDateInfo> holdFundInfoList) {
            this.holdFundInfoList = holdFundInfoList;
        }

        public List<QueryFundPositionDateResponse.FundDateInfo> getClearFundInfoList() {
            return clearFundInfoList;
        }

        public void setClearFundInfoList(List<QueryFundPositionDateResponse.FundDateInfo> clearFundInfoList) {
            this.clearFundInfoList = clearFundInfoList;
        }
    }

    /**
     * 查询清仓基金编码列表
     *
     * @param txAcctNo 交易账号
     * @param hbOneNo  一账通号
     * @param fundCode 基金编码（可为空，为空时查询所有清仓基金）
     * @return 清仓基金编码列表
     */
    private List<String> queryLiquidationFundCodeList(String txAcctNo, String hbOneNo, String fundCode, List<String> disCodeList) {
        List<String> liquidationFundCodes = new ArrayList<>();

        try {
            // 构建查询参数
            QueryBalanceParam queryParam = new QueryBalanceParam();
            queryParam.setTxAcctNo(txAcctNo);
            queryParam.setHboneNo(hbOneNo);
            queryParam.setFundCode(fundCode); // 如果为空，则查询所有清仓基金
            queryParam.setDisCodeList(disCodeList);

            // 调用清仓查询接口
            List<QueryLiquidationBalanceFundInfo> liquidationBalanceList =
                    acctBalanceBaseInfoService.queryLiquidationBalanceFundInfo(queryParam);

            if (CollectionUtils.isNotEmpty(liquidationBalanceList)) {
                liquidationFundCodes = liquidationBalanceList.stream()
                        .map(QueryLiquidationBalanceFundInfo::getFundCode)
                        .distinct()
                        .collect(Collectors.toList());
            }

            log.info("查询清仓基金编码完成, txAcctNo: {}, hbOneNo: {}, fundCode: {}, 清仓基金数量: {}",
                    txAcctNo, hbOneNo, fundCode, liquidationFundCodes.size());

        } catch (Exception e) {
            log.error("查询清仓基金编码异常, txAcctNo: {}, hbOneNo: {}, fundCode: {}, error: {}",
                    txAcctNo, hbOneNo, fundCode, e.getMessage(), e);
        }

        return liquidationFundCodes;
    }
}
