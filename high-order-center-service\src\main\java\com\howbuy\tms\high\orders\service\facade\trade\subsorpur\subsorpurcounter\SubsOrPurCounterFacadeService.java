/**
 * Copyright (c) 2017, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.orders.service.facade.trade.subsorpur.subsorpurcounter;

import com.howbuy.tms.common.enums.database.InvstTypeEnum;
import com.howbuy.tms.common.idempotent.Idempotent;
import com.howbuy.tms.high.orders.dao.po.DealOrderPo;
import com.howbuy.tms.high.orders.facade.trade.subsorpur.subsorpurcounter.SubsOrPurCounterFacade;
import com.howbuy.tms.high.orders.facade.trade.subsorpur.subsorpurcounter.SubsOrPurCounterRequest;
import com.howbuy.tms.high.orders.facade.trade.subsorpur.subsorpurcounter.SubsOrPurCounterResponse;
import com.howbuy.tms.high.orders.service.business.busiprocess.SubsOrPurCounterBusiProcess;
import com.howbuy.tms.high.orders.service.business.busiprocess.common.AbstractDisCodeCreateOrderLogicProcess;
import com.howbuy.tms.high.orders.service.business.factory.createOrder.DisCodeCreateOrderLogicFactory;
import com.howbuy.tms.high.orders.service.business.ordercreater.OrderCreateBean;
import com.howbuy.tms.high.orders.service.business.ordercreater.OrderCreateContext;
import com.howbuy.tms.high.orders.service.facade.trade.subsorpur.BaseSubsOrPurService;
import com.howbuy.tms.high.orders.service.repository.DealCompositeRepository;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Description:柜台基金认申购接口
 *
 * @reason:
 * <AUTHOR>
 * @date 2017年3月15日 下午6:35:05
 * @since JDK 1.7
 */
@DubboService
@Service("subsOrPurCounterFacade")
public class SubsOrPurCounterFacadeService extends BaseSubsOrPurService implements SubsOrPurCounterFacade {
    @Autowired
    private DisCodeCreateOrderLogicFactory disCodeCreateOrderLogicFactory;

    @Autowired
    private DealCompositeRepository dealCompositeRepository;

    @Autowired
    private SubsOrPurCounterBusiProcess subsOrPurCounterBusiProcess;

    @Override
    @Idempotent
    public SubsOrPurCounterResponse execute(SubsOrPurCounterRequest request) {
        OrderCreateContext context = new OrderCreateContext();
        checkParamsExceptInst(request);
        // 检查参数PayList
        validatePayList(request);
        // 整合请求参数
        combineRequestInfo(request, context);
        context.getBuyList().forEach(buyBean -> {
            buyBean.setDiscountRate(request.getDiscountRate());
            buyBean.setIsRedeemExpire(request.getIsRedeemExpire());
            buyBean.setPreExpireDate(request.getPreExpireDate());
        });
        // 业务处理
        subsOrPurCounterBusiProcess.process(request, context);
        // 创建订单
        context.setBuyBean(context.getBuyList().get(0));
        AbstractDisCodeCreateOrderLogicProcess disCodeCreateOrderCheckLogicProcess = disCodeCreateOrderLogicFactory.getDisCodeCreateOrderCheckLogicProcess(request.getDisCode());
        OrderCreateBean orderBean = disCodeCreateOrderCheckLogicProcess.createOrder(context);
        // 保存订单相关信息
        dealCompositeRepository.saveOrder(orderBean);
        // 发送支付消息
        sendPayMessage(orderBean.getPaymentOrderPo());
        // 发送订单实时信息
        sendDealActualMsg(orderBean);
        // 发送app站内信消息
        sendAppInBoxCreateOrderMsg(orderBean);
        // 发送柜台机构下单消息
        DealOrderPo orderPo = orderBean.getOrderPo();
        if (InvstTypeEnum.INST.getCode().equals(orderPo.getInvstType()) || InvstTypeEnum.PRODUCT.getCode().equals(orderPo.getInvstType())) {
            subsOrPurCounterBusiProcess.sendCounterOrderMessage(orderPo.getExternalDealNo(), orderPo.getDealNo(), request.getTxAcctNo(), request.getDisCode());
        }
        // 创建填充返回结果
        return createResponse(context, orderBean, new SubsOrPurCounterResponse());
    }
}
