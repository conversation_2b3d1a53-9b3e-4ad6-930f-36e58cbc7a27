package com.howbuy.tms.high.orders.facade.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Description:查询持仓,持仓状态枚举,0:不持仓,1:持仓,2:全部
 * @Author: yun.lu
 * Date: 2025/9/15 15:39
 */
@Getter
@AllArgsConstructor
public enum QueryBalanceStatusEnum {
    NOT_HOLD("0", "不持仓"),
    HOLD("1", "持仓"),
    ALL("2", "全部");
    /**
     * 状态码
     */
    private String code;
    /**
     * 描述
     */
    private String desc;
}
