package com.howbuy.tms.high.orders.service.service.ownershiprightorder;

import com.howbuy.tms.high.orders.dao.vo.BalanceOrderVo;
import lombok.Getter;
import lombok.Setter;

/**
 * @Description:直销订单
 * @Author: yun.lu
 * Date: 2023/5/26 9:50
 */
@Getter
@Setter
public class DirectOrderInfo extends OwnershipOrderInfo {
    /**
     * 订单号
     */
    private String appSerialNo;
    /**
     * 业务编码
     */
    private String businessCode;

    public DirectOrderInfo(BalanceOrderVo balanceOrderVo, String transferMBusinessCode) {
        super(balanceOrderVo, transferMBusinessCode);
        this.appSerialNo = balanceOrderVo.getOrderNo();
        this.businessCode = balanceOrderVo.getBusinessCode();
        this.setOrderNo(appSerialNo);
    }

}
