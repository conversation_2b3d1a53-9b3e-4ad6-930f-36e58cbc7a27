/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.orders.facade.search.queryactivebuysellstats;

import com.howbuy.tms.common.enums.busi.DisCodeEnum;
import com.howbuy.tms.high.orders.facade.common.OrderSearchBaseRequest;
import com.howbuy.tms.high.orders.facade.enums.TxCodes;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * @description: 查询主动买卖统计数据请求参数
 * <AUTHOR>
 * @date 2025/9/19 20:30
 * @since JDK 1.8
 */
@Setter
@Getter
public class QueryActiveBuySellStatsRequest extends OrderSearchBaseRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 构造函数，设置默认交易代码
     */
    public QueryActiveBuySellStatsRequest() {
        setTxCode(TxCodes.QUERY_ACTIVE_BUY_SELL_STATS);
        setDisCode(DisCodeEnum.HM.getCode());
    }
}
