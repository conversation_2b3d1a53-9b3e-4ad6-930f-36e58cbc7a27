/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.orders.facade.search.queryfundpositiondate;

import com.howbuy.tms.common.enums.busi.DisCodeEnum;
import com.howbuy.tms.high.orders.facade.common.OrderSearchBaseRequest;
import com.howbuy.tms.high.orders.facade.enums.TxCodes;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * @description: 清仓基金产品清仓日期查询请求参数
 * <AUTHOR>
 * @date 2025/9/4 22:00
 * @since JDK 1.8
 */
@Setter
@Getter
public class QueryFundPositionDateRequest extends OrderSearchBaseRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 构造函数，设置默认交易代码
     */
    public QueryFundPositionDateRequest() {
        setTxCode(TxCodes.QUERY_FUND_LIQUIDATION_DATE);
        setDisCode(DisCodeEnum.HM.getCode());
    }

    /**
     * 一账通号
     */
    private String hbOneNo;

    /**
     * 数据渠道
     * 1: 好买
     * 2：好臻
     * 3: 海外
     * 不传查询所有
     */
    private String dataChannel;

    /**
     * 持仓/清仓标识
     * 1: 持仓
     * 2: 清仓
     * 不传: 查询所有
     */
    private String holdStatus;

}
