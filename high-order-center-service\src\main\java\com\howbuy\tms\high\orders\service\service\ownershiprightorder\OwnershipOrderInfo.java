package com.howbuy.tms.high.orders.service.service.ownershiprightorder;

import com.howbuy.tms.common.enums.busi.BusinessCodeEnum;
import com.howbuy.tms.common.enums.busi.OwnershipTransferIdentityEnum;
import com.howbuy.tms.common.enums.busi.YesOrNoEnum;
import com.howbuy.tms.high.orders.dao.vo.BalanceOrderVo;
import com.howbuy.tms.high.orders.service.service.ownershiprightorder.OwnershipRightOrder;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description:股权订单抽象类
 * @Author: yun.lu
 * Date: 2023/5/26 9:50
 */
@Data
public class OwnershipOrderInfo implements OwnershipRightOrder {

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 产品code
     */
    private String fundCode;

    /**
     * 中台业务编码
     */
    private String mBusinessCode;

    /**
     * 确认日期
     */
    public String ackDt;


    /**
     * 确认份额
     */
    public BigDecimal ackVol;

    /**
     * 确认金额
     */
    public BigDecimal ackAmt;

    /**
     * 费用
     */
    public BigDecimal fee;

    /**
     * 转让价格
     */
    public BigDecimal transferPrice;

    /**
     * 是否非交易转让
     */
    public String isNoTradeTransfer;

    /**
     * 转译处理后的中台业务编码
     */
    public String transferMBusinessCode;

    public OwnershipOrderInfo(BalanceOrderVo balanceOrderVo, String transferMBusinessCode) {
        this.fundCode = balanceOrderVo.getFundCode();
        this.mBusinessCode = balanceOrderVo.getMBusinessCode();
        this.ackVol = balanceOrderVo.getAckVol();
        this.ackAmt = balanceOrderVo.getAckAmt();
        this.fee = balanceOrderVo.getFee();
        this.transferPrice = balanceOrderVo.getTransferPrice();
        this.isNoTradeTransfer = balanceOrderVo.getIsNoTradeTransfer();
        this.transferMBusinessCode = transferMBusinessCode;
        this.ackDt = balanceOrderVo.getAckDt();
    }

    /**
     * 是否是买入订单
     *
     * @return true:是买入订单
     */
    private boolean isBuyOrder() {
        // 认购
        if (BusinessCodeEnum.SUBS.getMCode().equals(transferMBusinessCode)) {
            return true;
        }
        // 申购
        if (BusinessCodeEnum.PURCHASE.getMCode().equals(transferMBusinessCode)) {
            return true;
        }

        // 认购结果
        if (BusinessCodeEnum.SUBS_RESULT.getMCode().equals(transferMBusinessCode)) {
            return true;
        }

        // 基金转换转入
        if (BusinessCodeEnum.FUND_EXCHANGE_IN.getMCode().equals(transferMBusinessCode)) {
            return true;
        }
        // 非交易过户转入
        if (BusinessCodeEnum.NOTRADE_OVERACCOUNT_IN.getMCode().equals(transferMBusinessCode)) {
            return true;
        }
        // 是否非交易过户+强增
        if (BusinessCodeEnum.FORCE_ADD.getMCode().equals(transferMBusinessCode) && YesOrNoEnum.YES.getCode().equals(isNoTradeTransfer)) {
            return true;
        }
        return false;
    }

    /**
     * 存在受让：若存在【交易类型】=1134-非交易过户转入；或【交易类型】=1144-强行调增且【是否非交易过户】=是 的交易记录，则【股权转让标识】为“受让”；
     * 存在转让：若存在【交易类型】=1135-非交易过户转出；或【交易类型】=1145-强行调减/1142-强行赎回且【是否非交易过户】=是 的交易记录，则【股权转让标识】为“转让”；
     * 若上述交易记录均不存在，则【股权转让标识】=为“无”
     *
     * @return
     */
    @Override
    public String getOwnershipTransferIdentity() {
        // 非交易过户转入
        if (BusinessCodeEnum.NOTRADE_OVERACCOUNT_IN.getMCode().equals(transferMBusinessCode)) {
            return OwnershipTransferIdentityEnum.TRANSFER_IN.getType();
        }
        // 是否非交易过户+强增
        if (BusinessCodeEnum.FORCE_ADD.getMCode().equals(transferMBusinessCode) && YesOrNoEnum.YES.getCode().equals(isNoTradeTransfer)) {
            return OwnershipTransferIdentityEnum.TRANSFER_IN.getType();
        }

        // 非交易过户转出
        if (BusinessCodeEnum.NOTRADE_OVERACCOUNT_OUT.getMCode().equals(transferMBusinessCode)) {
            return OwnershipTransferIdentityEnum.TRANSFER_OUT.getType();
        }
        // 是否非交易过户=是，且【交易类型】=1145-强行调减、1142-强行赎回
        if (YesOrNoEnum.YES.getCode().equals(isNoTradeTransfer)) {
            if (BusinessCodeEnum.FORCE_SUBTRACT.getMCode().equals(transferMBusinessCode) || BusinessCodeEnum.FORCE_REDEEM.getMCode().equals(transferMBusinessCode)) {
                return OwnershipTransferIdentityEnum.TRANSFER_OUT.getType();

            }
        }
        return OwnershipTransferIdentityEnum.NO_TRANSFER.getType();
    }


    /**
     * 是否是卖出订单
     *
     * @return true:是卖出订单
     */
    private boolean isSaleOrder() {
        // 非交易过户转出
        if (BusinessCodeEnum.NOTRADE_OVERACCOUNT_OUT.getMCode().equals(transferMBusinessCode)) {
            return true;
        }
        // 是否非交易过户=是，且【交易类型】=1145-强行调减、1142-强行赎回
        if (YesOrNoEnum.YES.getCode().equals(isNoTradeTransfer)) {
            if (BusinessCodeEnum.FORCE_SUBTRACT.getMCode().equals(transferMBusinessCode) || BusinessCodeEnum.FORCE_REDEEM.getMCode().equals(transferMBusinessCode)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 获取成本
     */
    @Override
    public BigDecimal getNetBuyAmount() {
        if (isBuyOrder()) {
            return getBuyPrice();
        } else if (isSaleOrder()) {
            // 确认份额的负值
            return ackVol.negate();
        } else {
            return BigDecimal.ZERO;
        }

    }

    /**
     * 计算:净买入价格
     * 优先级1：取订单的（【确认金额】-【确认手续费】）；
     * 优先级2：若【确认金额】为空或≤0，则取订单的（【确认份额】*固定净值”1“-【确认手续费】）；
     * 确认手续费：若为空或≤0，按0计算；
     *
     * @return 买入价格
     */
    private BigDecimal getBuyPrice() {
        // 其他情况都是先按照确认金额-手续费,如果确认金额为空/小于0,则用确认份额-手续费
        if (this.ackAmt != null && BigDecimal.ZERO.compareTo(this.ackAmt) < 0) {
            return this.ackAmt.subtract(getFee());
        } else {
            return this.ackVol;
        }
    }

    /**
     * 获取手续费,若为空或≤0，按0计算
     *
     * @return 手续费
     */
    private BigDecimal getFee() {
        if (this.fee == null || BigDecimal.ZERO.compareTo(this.fee) > 0) {
            return BigDecimal.ZERO;
        }
        return this.fee;

    }

}
