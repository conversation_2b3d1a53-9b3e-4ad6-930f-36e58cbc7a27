/**
 * Copyright (c) 2021, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.tms.high.orders.service.facade.trade.redeem.redeemmergeweb;

import com.howbuy.tms.common.enums.busi.BusinessCodeEnum;
import com.howbuy.tms.common.idempotent.Idempotent;
import com.howbuy.tms.high.orders.facade.trade.redeem.redeemmergeweb.RedeemMergeWebFacade;
import com.howbuy.tms.high.orders.facade.trade.redeem.redeemmergeweb.RedeemMergeWebRequest;
import com.howbuy.tms.high.orders.facade.trade.redeem.redeemmergeweb.RedeemMergeWebResponse;
import com.howbuy.tms.high.orders.service.business.busiprocess.RedeemWebBusiProcess;
import com.howbuy.tms.high.orders.service.business.busiprocess.common.AbstractDisCodeCreateOrderLogicProcess;
import com.howbuy.tms.high.orders.service.business.factory.createOrder.DisCodeCreateOrderLogicFactory;
import com.howbuy.tms.high.orders.service.business.ordercreater.OrderCreateBean;
import com.howbuy.tms.high.orders.service.business.ordercreater.OrderCreateContext;
import com.howbuy.tms.high.orders.service.facade.trade.redeem.BaseRedeemService;
import com.howbuy.tms.high.orders.service.repository.DealCompositeRepository;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 请在此添加描述
 *
 * <AUTHOR>
 * @date 2021/2/24 14:01
 * @since JDK 1.8
 */
@DubboService
@Service("redeemMergeWebFacade")
public class RedeemMergeWebFacadeService extends BaseRedeemService implements RedeemMergeWebFacade {

    @Autowired
    private DisCodeCreateOrderLogicFactory disCodeCreateOrderLogicFactory;

    @Autowired
    private DealCompositeRepository dealCompositeRepository;

    @Autowired
    private RedeemWebBusiProcess redeemWebBusiProcess;

    @Override
    @Idempotent
    public RedeemMergeWebResponse execute(RedeemMergeWebRequest request) {
        // 验证参数
        validateRedeemListSize(request);

        OrderCreateContext context = new OrderCreateContext();
        combineRequestInfo(request, context);

        // 业务处理
        redeemWebBusiProcess.process(request, context, request.getTxPwd());

        // 创建订单
        AbstractDisCodeCreateOrderLogicProcess disCodeCreateOrderCheckLogicProcess = disCodeCreateOrderLogicFactory.getDisCodeCreateOrderCheckLogicProcess(request.getDisCode());
        List<OrderCreateBean> orderList = disCodeCreateOrderCheckLogicProcess.createMergeOrders(context);

        // 保存订单相关信息
        dealCompositeRepository.saveMergeOrders(orderList);

        //发送订单实时信息
        sendDealActualMsg(orderList.get(0));
        // 发送app站内信消息
        sendAppInBoxCreateOrderMsg(orderList.get(0));

        // 创建返回对象
        RedeemMergeWebResponse response = new RedeemMergeWebResponse();
        response.setmBusiCode(BusinessCodeEnum.REDEEM.getMCode());
        return createResponse(request.getRedeemCapitalFlag(), context, orderList.get(0), response);
    }
}