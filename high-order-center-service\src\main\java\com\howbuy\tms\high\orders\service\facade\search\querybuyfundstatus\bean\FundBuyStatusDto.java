package com.howbuy.tms.high.orders.service.facade.search.querybuyfundstatus.bean;

import com.howbuy.tms.common.enums.busi.FundBuyStatusEnum;
import com.howbuy.tms.high.orders.facade.common.BaseDto;
import com.howbuy.tms.high.orders.service.common.enums.FundBuyStatusNotCanbuyCodeEnum;
import lombok.Data;

/**
 * @Description:产品购买状态
 * @Author: yun.lu
 * Date: 2023/10/26 14:55
 */
@Data
public class FundBuyStatusDto extends BaseDto {
    /**
     * 产品编码
     */
    private String productCode;

    /**
     * 产品购买状态
     *
     * @see com.howbuy.tms.common.enums.busi.FundBuyStatusEnum
     */
    private FundBuyStatusEnum fundBuyStatusEnum;

    /**
     * @see com.howbuy.tms.high.orders.service.common.enums.FundBuyStatusNotCanbuyCodeEnum
     */
    private FundBuyStatusNotCanbuyCodeEnum fundBuyStatusNotCanbuyCodeEnum;

    /**
     * 收费类型A-前收费;B-后收费
     */
    private String shareClass;

    /**
     * 状态标识1-正常 2-代销不支持 3.年龄限制 4-已售罄 5-直销转代销的黑名单6-产品状态不可购买或不在预约期内
     * 8-产品参数配置有误99-其它
     *
     * @see com.howbuy.tms.common.enums.busi.BuyStatusTypeEnum
     */
    private String buyStatusType;

    /**
     * 说明信息
     */
    private String msg;

    /**
     * 延期下单标识 1 是  0 否
     */
    private String delayedOrderFlag;


}
