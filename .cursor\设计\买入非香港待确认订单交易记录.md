### 1.买入非香港待确认订单交易记录(新增模块)

- 请求地址

| 类名                                                         | 方法名                         | 描述                     |
| :----------------------------------------------------------- | :----------------------------- | :----------------------- |
| com.howbuy.tms.high.orders.service.service.acctbalancebaseinfo.AcctBalanceBaseInfoService | queryBuyUnConfirmUnHkOrderList | 买入待确认非香港订单信息 |


- 入参 

| 字段        | 字段注释 | 类型         | 是否必填 | 备注 |
| :---------- | :------- | :----------- | :------- | :--- |
| 字段        | 字段注释 | 类型         | 是否必填 | 备注 |
| txAcctNo    | 用户号   | String       | 是       |      |
| hboneNo     | 一账通   | String       | 是       |      |
| fundCode    | 基金代码 | String       | 否       |      |
| disCodeList | 分销渠道 | List<String> | 是       |      |

- 出参

| 字段        | 字段注释       | 类型       | 备注 |
| :---------- | :------------- | :--------- | :--- |
| dealNo      | 订单号         | String     | ``   |
| fundCode    | 产品编码       | String     |      |
| applyNetAmt | 申请净金额     | BigDecimal |      |
| cxgFrznAmt  | 储蓄罐冻结金额 | BigDecimal |      |
| mBusiCode   | 中台业务类型   | String     |      |

- 需求:

（1）代销产品：若【是否计入OP】=是，则取【交易类型】=认购/申购，且【订单状态】=申请成功，且【付款状态】=4-成功 的交易记录的【净申购金额】，【净申购金额】=【申购金额】-【申购费用】。若为外币，需按实时汇率接口转化为人民币。若【申请费用】为空，则按0处理

（2）直销产品：若【是否计入OP】=否，取CRM中【录入时间】≥2022/11/1 & 【交易类型】=认购/申购 &【预约状态】=已确认 &【订单状态】=申请成功(未确认) &【打款状态】=到账确认 的预约记录的【实际打款金额】；若为外币，需按实时汇率接口转化为人民币

（3）储蓄罐预支付冻结金额：取支付方式为储蓄罐的在途订单，关联的储蓄罐预约冻结金额

- 业务逻辑

  - 根据disCodeList判断查询的数据范围

    - 查询直销,代销的非香港交易记录,实现:highDealOrderDtlRepository.queryUnConfirmUnHkBuyOrderList(txAcctNo,hboneNo,fundCode,disCodeList返回实体字段 dealNo 订单号;mergeSubmitFlag 是否合并上报,1:是,0:不是;mainDealOrderNo 主订单号;dealType:订单交易类型,1:代销,0:直销;fundCode 基金代码;netAppAmt:净申请金额;m_busiCode:业务类型;currency:币种,cxgFrznAmt 储蓄罐冻结金额

    - 查询mybatis xml的sql如下

      ``

      ```mysql
      select
          d.deal_no,
          d.merge_submit_flag,
          d.main_deal_order_no,
          '1' as deal_type,
          d.fund_code ,
          d.net_app_amt as net_app_amt,
          d.m_busi_code ,
          '156' as currency,
          if(p.tx_pmt_flag='11',d.net_app_amt,0) as cxg_frzn_amt
          from high_deal_order_dtl d
          inner join deal_order o
          on o.deal_no = d.deal_no
         inner join payment_order p
          on d.deal_no=p.deal_no
          where d.tx_app_flag = '0'
          and (d.tx_ack_flag is null or d.tx_ack_flag in ('1', '2'))
          and o.pay_status = '4'
          <if test = "fundCode != null and fundCode != ''">
              and d.fund_code = #{fundCode,jdbcType = VARCHAR}
          </if>
          <if test="disCodeList != null and disCodeList.size() > 0 ">
              and d.DIS_CODE in
              <foreach collection="disCodeList" index="index" item="disCode" open="(" separator="," close=")">
                  #{disCode}
              </foreach>
          </if>
          and d.m_busi_code in ('1120', '1122')
          and o.tx_acct_no = #{txAcctNo,jdbcType=VARCHAR}
          union all
      select
          c.appserialno as deal_no,
          '0' as merge_submit_flag,
          '' as main_deal_order_no,
          '0' as deal_type,
           ifnull(c.mjjdm,c.fundcode) as fund_code,
           ifnull(c.appamt,0)- ifnull(c.fee,0) as net_app_amt ,
           CONCAT('1', c.busicode) AS m_busi_code,
           c.currency ,
           0 as cxg_frzn_amt
      from cm_custtrade_direct c
      where c.busicode in ('120','122','12B','12A')
        and c.prebookstate = '2'
        and c.RECSTAT='0'
        and c.IS_HK_PRODUCT!='1'
          <if test="disCodeList != null and disCodeList.size() > 0 ">
              and c.DISCODE in
              <foreach collection="disCodeList" index="index" item="disCode" open="(" separator="," close=")">
                  #{disCode}
              </foreach>
          </if>
          <if test = "fundCode != null and fundCode != ''">
              and c.fundcode= #{fundCode,jdbcType = VARCHAR}
          </if>
        and c.paystate ='4'
        and c.orderstate = '1'
        and c.txacctno = #{txAcctNo,jdbcType=VARCHAR}
        and c.tradedt <![CDATA[ >= ]]> '20221101'
      ```

​      

- 遍历代销部分交易记录,找出 mergeSubmitFlag ='1' && mainDealOrderNo is not null 的,然后按照将这部分交易记录,按照mainDealOrderNo 分组,每组的净申请金额汇总求和,储蓄罐冻结金额求和 ,每组合并为一条新的交易记录,替换为分组前的这部分交易记录,dealNo=mainDealOrderNo ,appNetAmt=每组的净申请金额汇总求和,cxgFrznAmt=每组储蓄罐冻结金额求和,fundCode 取dealNo=mainDealOrderNo那条记录的fundCode,mBusiCode取dealNo=mainDealOrderNo那条记录的mBusiCode
- 上面获取的交易记录,判断如果是非人民币(com.howbuy.tms.common.enums.busi.CurrencyEnum#RMB),需要转换汇率,将金额转为人民币,查询人民币的汇率,com.howbuy.tms.common.outerservice.simu.comprehensive.QueryComprehensiveOuterService#getRmbhlzjj,入参第一个入参传null,第二个传入当前交易的币种,获取到的汇率*净申请金额=新的人民币净申请金额
- 返回将交易记录,转为符合接口定义返回结果字段的交易记录