package com.howbuy.tms.high.orders.service.facade.search.querybuyfundstatus.bean;

import com.howbuy.tms.common.outerservice.acccenter.querycustInfo.QueryCustInfoResult;
import com.howbuy.tms.high.orders.facade.common.BaseDto;
import lombok.Data;

/**
 * @Description:查询产品购买状基础入参
 * @Author: yun.lu
 * Date: 2023/10/30 20:15
 */
@Data
public class QueryFundBuyStatusBaseParam extends BaseDto {
    /**
     * 交易账号
     */
    private String txAcctNo;
    /**
     * 购买渠道
     */
    private String txChannel;
    /**
     * 预约日历场景标识 1-是
     */
    private String appointmentFlag;
    /**
     * ta交易日
     */
    private String taTradeDt;
    /**
     * 申请日期
     */
    private String appDt;
    /**
     * 申请时间
     */
    private String appTm;

    /**
     * 分销渠道
     */
    private String disCode;

    /**
     * 页面来源, 针对不同的页面可能需要定制化的校验
     */
    private String pageSource;
}
