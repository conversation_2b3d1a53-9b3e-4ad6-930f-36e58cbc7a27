/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.orders.facade.search.queryactivebuysellstats;

import com.howbuy.tms.high.orders.facade.common.OrderSearchBaseResponse;

import java.io.Serializable;
import java.util.List;

/**
 * @description: 查询主动买卖统计数据响应结果
 * <AUTHOR>
 * @date 2025/9/19 20:30
 * @since JDK 1.8
 */
public class QueryActiveBuySellStatsResponse extends OrderSearchBaseResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 交易明细列表
     */
    private List<TradeDetailInfoResponse> tradeDetailList;

    public List<TradeDetailInfoResponse> getTradeDetailList() {
        return tradeDetailList;
    }

    public void setTradeDetailList(List<TradeDetailInfoResponse> tradeDetailList) {
        this.tradeDetailList = tradeDetailList;
    }
}
