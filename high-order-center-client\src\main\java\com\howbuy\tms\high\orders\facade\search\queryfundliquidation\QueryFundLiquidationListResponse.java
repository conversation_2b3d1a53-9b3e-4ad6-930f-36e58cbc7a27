/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.orders.facade.search.queryfundliquidation;

import com.howbuy.tms.high.orders.facade.common.OrderSearchBaseResponse;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @description: 清仓产品列表查询响应结果
 * <AUTHOR>
 * @date 2025/9/4 20:05
 * @since JDK 1.8
 */
@Setter
@Getter
public class QueryFundLiquidationListResponse extends OrderSearchBaseResponse {


    private static final long serialVersionUID = -4243023519133751152L;
    /**
     * 清仓产品列表
     */
    private List<FundLiquidationInfo> fundLiquidationList;

    /**
     * 清仓产品信息
     */
    @Setter
    @Getter
    public static class FundLiquidationInfo implements Serializable {


        private static final long serialVersionUID = -3682119952096873588L;
        /**
         * 基金代码
         */
        private String fundCode;

        /**
         * 基金名称
         */
        private String fundName;

        /**
         * 产品收益
         */
        private BigDecimal productAsset;

        /**
         * 累计天数
         * 累计持有天数，单位：天
         */
        private String totalDays;
        /**
         * 是否是净值型产品,1:是,0:不是
         */
        private String isNavProduct;


        /**
         * 单位
         * 默认是元
         */
        private String unit = "元";
    }
}
