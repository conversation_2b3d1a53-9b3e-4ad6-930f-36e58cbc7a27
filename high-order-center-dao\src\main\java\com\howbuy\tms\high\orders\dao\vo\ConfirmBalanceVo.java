package com.howbuy.tms.high.orders.dao.vo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Description: 确认持仓通用返回实体
 * @Author: yun.lu
 * @Date: 2025/08/29 10:00:00
 */
@Getter
@Setter
public class ConfirmBalanceVo implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 基金代码
     */
    private String fundCode;
    /**
     * 子基金代码
     */
    private String subFundCode;
    /**
     * 交易账号
     */
    private String txAcctNo;

    /**
     * 一账通
     */
    private String hboneNo;
    /**
     * 持仓份额
     */
    private BigDecimal balanceVol;
    /**
     * 分销渠道
     */
    private String disCode;

    /**
     * 币种
     */
    private String currency;

    /**
     * 确认日期(只有子账本明细有)
     */
    private String ackDt;
    /**
     * 登记日期
     */
    private String regDt;
    /**
     * 成立日期
     */
    private String establishDt;
}