<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	   xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-2.5.xsd">

    <import resource="classpath:context/spring-common-cache.xml"/>
    <import resource="classpath:context/spring-facade-common.xml"/>
    <import resource="classpath:context/spring-facade-fbsonline.xml"/>
    <import resource="classpath:context/spring-facade-fbsonlinesearch.xml"/>
    <import resource="classpath:context/spring-facade-ftxonline.xml"/>
    <import resource="classpath:context/spring-facade-ftxonlinesearch.xml"/>
    <import resource="classpath:context/spring-facade-payonline.xml"/>
    <import resource="classpath:context/spring-facade-acccenter.xml"/>
    <import resource="classpath:META-INF/spring/spring-facade-product.xml"/>
    <import resource="classpath:context/spring-facade-ftxconsole.xml"/>
    <import resource="classpath:context/spring-facade-finonline.xml"/>
    <import resource="classpath:context/spring-facade-cc.xml"/>
    <import resource="classpath:context/spring-facade-cms.xml"/>
    <import resource="classpath:context/spring-facade-simu.xml"/>
    <import resource="classpath:context/spring-facade-paramcenter.xml"/>
    <import resource="classpath:context/spring-facade-crm.xml"/>
    <import resource="classpath:context/spring-facade-cc-message.xml"/>
    <import resource="classpath:context/spring-facade-esconsole.xml"/>
    <import resource="classpath:context/spring-facade-auth.xml"/>
    <import resource="classpath:context/spring-facade-sale.xml"/>
    <import resource="classpath:context/spring-facade-dtms.xml"/>
    <import resource="classpath:context/spring-facade-elasticsearch.xml"/>
    <import resource="classpath:context/spring-facade-howhow-web-client.xml"/>
    <import resource="classpath:context/spring-facade-centercli.xml"/>
</beans>