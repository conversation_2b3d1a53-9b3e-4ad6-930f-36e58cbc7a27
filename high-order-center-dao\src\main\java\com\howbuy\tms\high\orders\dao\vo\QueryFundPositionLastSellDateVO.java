/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.tms.high.orders.dao.vo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2025/9/18 9:56
 * @since JDK 1.8
 */
@Setter
@Getter
public class QueryFundPositionLastSellDateVO implements Serializable {


    private static final long serialVersionUID = 2494340651876107343L;
    /**
     * 订单号
     */
    private String dealNo;

    /**
     * 基金代码
     */
    private String fundCode;

    /**
     * 首次购买时间
     */
    private String lastSellDate;
}
