# 高端订单中心项目开发规范

## 项目概述

高端订单中心（high-order-center）是好买基金高端产品交易系统的核心模块，负责处理客户产品查询、交易处理、风险匹配评估等业务功能。

## 项目架构

### 模块结构
- **high-order-center-client**: 对外接口和模型定义
- **high-order-center-service**: 业务逻辑实现层
- **high-order-center-dao**: 数据访问层
- **high-order-trade-remote**: 交易远程服务
- **high-order-search-remote**: 搜索远程服务

### 技术栈
- 框架：Spring Boot, Spring Cloud
- 微服务通信：Dubbo
- 数据库：MySQL
- ORM：MyBatis
- 消息队列：RocketMQ, ActiveMQ
- 缓存：Redis
- 服务注册与发现：Nacos, Zookeeper

### 系统分层架构
```
[controller/facade/scheduler/listener] -> service -> repository -> mapper
```

## 包命名规范

### 基础包结构
```
com.howbuy.tms.high.orders
├── client/                          # 客户端接口定义
│   └── facade/                      # Dubbo接口定义
├── service/                         # 服务实现层
│   ├── facade/                      # Dubbo接口实现
│   ├── service/                     # 业务服务层
│   ├── repository/                  # 数据仓储层
│   ├── business/                    # 公共业务组件
│   ├── cacheservice/               # 缓存服务
│   ├── config/                     # 配置类
│   ├── common/                     # 公共组件
│   └── aspect/                     # 切面处理
└── dao/                            # 数据访问层
    ├── po/                         # 持久化对象
    ├── vo/                         # 值对象
    └── mapper/                     # MyBatis映射器
```

### 类命名规范

#### 接口层组件
- **Dubbo接口**: 功能描述+`Facade`，如`QueryAcctBalanceFacade`、`CreateOrderFacade`
- **请求对象**: 接口名+`Request`，如`QueryAcctBalanceRequest`
- **响应对象**: 接口名+`Response`，如`QueryAcctBalanceResponse`
- **内部Bean**: 功能描述+`Bean`，如`BalanceBean`、`OrderBean`

#### 服务层组件
- **Dubbo实现类**: 接口名+`Service`，如`QueryAcctBalanceFacadeService`
- **Service类**: 业务功能+`Service`，如`AcctBalanceBaseInfoService`
- **Repository类**: 表名+`Repository`，如`CustBooksRepository`
- **Mapper接口**: 表名+`Mapper`，如`CustBooksPoMapper`

#### 数据层组件
- **PO类**: 表名+`Po`，如`CustBooksPo`、`DealOrderPo`
- **VO类**: 功能描述+`Vo`，如`BalanceVo`、`OrderVo`

## 版权头规范

所有Java文件必须包含标准版权头：

```java
/**
 * Copyright (c) 2017, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
```

## 接口定义规范

### Dubbo接口定义
1. 接口必须继承`BaseFacade<Request, Response>`
2. 接口必须使用APIDOC风格注释，包含以下内容：
   - API路径(`@api`)
   - API版本(`@apiVersion`)
   - API组(`@apiGroup`)
   - API名称(`@apiName`)
   - API描述(`@apiDescription`)
   - 请求参数(`@apiParam`)
   - 请求示例(`@apiParamExample`)
   - 响应结果(`@apiSuccess`)
   - 响应示例(`@apiSuccessExample`)

```java
/**
 * @api {DUBBO} com.howbuy.tms.high.orders.facade.search.queryacctbalance.QueryAcctBalanceFacade.execute()
 * @apiVersion 1.0.0
 * @apiGroup QueryAcctBalanceFacadeService
 * @apiName execute
 * @apiDescription 查询产品持仓接口实现（整个高端持仓基础接口）
 * @apiParam (请求参数) {String} hkSaleFlag 好买香港代销标识 0-否，1-是
 * @apiParam (请求参数) {String} productCode 产品代码
 * @apiParamExample 请求参数示例
 * {"hkSaleFlag":"0","productCode":"HM001"}
 * @apiSuccess (响应结果) {String} returnCode 返回码
 * @apiSuccess (响应结果) {String} description 描述信息
 * @apiSuccessExample 响应结果示例
 * {"returnCode":"0000","description":"成功"}
 */
public interface QueryAcctBalanceFacade extends BaseFacade<QueryAcctBalanceRequest, QueryAcctBalanceResponse> {
}
```

### 入参/出参定义规范
1. 请求类必须继承`OrderBaseRequest`或`OrderSearchBaseRequest`
2. 响应类必须继承`OrderBaseResponse`或`OrderSearchBaseResponse`
3. 所有字段必须有注释说明用途
4. 使用`@Getter`和`@Setter`注解（不使用`@Data`）
5. 字段命名遵循Java驼峰命名法

```java
@Getter
@Setter
public class QueryAcctBalanceRequest extends OrderSearchBaseRequest {
    /**
     * 好买香港代销标识
     * 0-否，1-是
     */
    private String hkSaleFlag;

    /**
     * 产品代码
     */
    private String productCode;
}
```

## 服务层实现规范

### Facade实现类
1. 使用`@DubboService`和`@Service`注解
2. 可选使用`@RefreshScope`支持配置刷新
3. 使用Log4j2日志框架：`LogManager.getLogger()`
4. 通过`@Autowired`注入依赖
5. 方法应有完整的Javadoc注释

```java
@DubboService
@Service("queryAcctBalanceFacade")
@RefreshScope
public class QueryAcctBalanceFacadeService implements QueryAcctBalanceFacade {
    private static final Logger logger = LogManager.getLogger(QueryAcctBalanceFacadeService.class);

    @Autowired
    private CustBooksRepository custBooksRepository;

    /**
     * @description: 查询客户持仓信息
     * @param request 请求参数
     * @return QueryAcctBalanceResponse 持仓信息
     * @author: hongdong.xie
     * @date: 2025/8/26 14:30
     * @since JDK 1.8
     */
    @Override
    public QueryAcctBalanceResponse execute(QueryAcctBalanceRequest request) {
        // 业务逻辑实现
        return new QueryAcctBalanceResponse();
    }
}
```

### Service层
1. 使用`@Service`注解将服务注册到Spring容器
2. 通过`@Autowired`注入Repository类和其他Service类
3. 实现具体业务逻辑，不直接操作数据库
4. 方法应有完整的Javadoc注释

```java
@Service
public class AcctBalanceBaseInfoService {
    private static final Logger logger = LogManager.getLogger(AcctBalanceBaseInfoService.class);

    @Autowired
    private CustBooksRepository custBooksRepository;

    /**
     * @description: 获取是否首次购买
     * @param fundCode 产品代码
     * @param txAcctNo 交易账号
     * @param disCode 分销机构代码
     * @return String 是否首次购买 0-否，1-是
     * @author: hongdong.xie
     * @date: 2025/8/26 14:30
     * @since JDK 1.8
     */
    public String getIsFirstBuy(String fundCode, String txAcctNo, String disCode) {
        // 业务逻辑实现
        return YesOrNoEnum.NO.getCode();
    }
}
```

### Repository层
1. 使用`@Repository`注解
2. 注入Mapper接口，封装数据库操作
3. 写操作方法使用`@Transactional(propagation = Propagation.REQUIRED)`
4. 类级别使用`@Transactional(propagation = Propagation.SUPPORTS)`
5. 方法需有完整的Javadoc注释

```java
@Repository
@Transactional(propagation = Propagation.SUPPORTS)
public class CustBooksRepository {
    @Autowired
    private CustBooksPoMapper custBooksPoMapper;

    /**
     * @description: 查询客户持仓信息
     * @param disCodeList 分销机构代码列表
     * @param txAcctNo 交易账号
     * @param productCodeList 产品代码列表
     * @param balanceStatus 持仓状态
     * @return List<BalanceVo> 持仓信息列表
     * @author: hongdong.xie
     * @date: 2025/8/26 14:30
     * @since JDK 1.8
     */
    public List<BalanceVo> selectBalanceWithLockPeriod(List<String> disCodeList, String txAcctNo,
                                                       List<String> productCodeList, String balanceStatus) {
        return custBooksPoMapper.selectBalanceWithLockPeriod(disCodeList, txAcctNo, productCodeList, balanceStatus);
    }
}
```

## 事务管理规范

1. **事务只能加在repository层**
2. **事务内不能包含外部服务调用**
3. **事务尽量小**
4. Repository类级别使用`@Transactional(propagation = Propagation.SUPPORTS)`
5. 写操作方法使用`@Transactional(propagation = Propagation.REQUIRED)`

## 异常处理规范

### 统一异常处理
项目使用AOP进行统一异常处理，支持以下异常类型：
- `ConstraintsViolatedException`: 参数校验异常
- `ValidateException`: 业务校验异常
- `BusinessException`: 业务异常
- `DataBaseException`: 数据库异常
- `SystemException`: 系统异常

### 异常使用规范
1. 使用统一的异常处理机制
2. 业务异常应继承自应用的基础异常类
3. 合理使用自定义异常和错误码
4. 异常信息应包含足够的上下文信息
5. 优先在`ExceptionCodes`中查找错误码

```java
try {
    // 业务逻辑
} catch (BusinessException e) {
    logger.error("业务处理异常: {}", e.getMessage(), e);
    throw e;
} catch (Exception e) {
    logger.error("系统异常: {}", e.getMessage(), e);
    throw new SystemException(ExceptionCodes.HIGH_ORDER_SYSTEM_ERROR, e);
}
```

## 枚举使用规范

### 枚举定义规范
1. 枚举类使用`@AllArgsConstructor`和`@Getter`注解
2. 提供`getEnum(String value)`静态方法
3. 字段命名：`code`/`value`表示值，`desc`/`description`表示描述

```java
@AllArgsConstructor
@Getter
public enum VolLockStatusEnum {
    LOCKING("1", "锁定中"),
    LOCK_REDEEMABLE("2", "锁定可赎回"),
    LOCK_OVER("3", "锁定已过");

    private String status;
    private String desc;

    public static VolLockStatusEnum getEnum(String status) {
        if (status == null) {
            return null;
        }
        for (VolLockStatusEnum statusEnum : VolLockStatusEnum.values()) {
            if (statusEnum.status.equals(status)) {
                return statusEnum;
            }
        }
        return null;
    }
}
```

### 常用枚举包
- `com.howbuy.tms.common.enums.busi.*`: 业务枚举
- `com.howbuy.tms.common.enums.database.*`: 数据库枚举
- `com.howbuy.tms.high.orders.service.common.enums.*`: 项目特有枚举

## 日志规范

### 日志框架
使用Log4j2日志框架：
```java
private static final Logger logger = LogManager.getLogger(ClassName.class);
```

### 日志级别使用
1. **ERROR**: 记录异常和错误信息
2. **WARN**: 记录警告信息
3. **INFO**: 记录重要业务操作和流程信息
4. **DEBUG**: 记录调试信息

### 日志内容规范
1. 日志内容应包含足够的上下文信息
2. 敏感信息不应记录到日志中
3. 使用占位符而非字符串拼接
4. 重要业务操作必须记录日志

```java
// 正确的做法
logger.info("查询客户持仓信息开始，txAcctNo: {}, productCode: {}", txAcctNo, productCode);

// 错误的做法
logger.info("查询客户持仓信息开始，txAcctNo: " + txAcctNo + ", productCode: " + productCode);
```

## 代码质量规范

### 禁止使用的组件
1. **禁止使用`@Data`注解**: 必须使用`@Getter`和`@Setter`
2. **禁止使用`BeanUtils.copyProperties`**: 必须手动赋值
3. **禁止使用魔法值**: 必须定义为常量或枚举
4. **禁止跨层调用**: 严格按照分层架构调用

### 推荐使用的组件
1. **使用枚举替代魔法值**
2. **使用工具类进行数据转换**
3. **使用缓存提高性能**
4. **使用线程池处理并发任务**

### 代码注释规范
1. 所有public方法必须有Javadoc注释
2. 注释必须包含`@description`、`@param`、`@return`、`@author`、`@date`、`@since`
3. 复杂业务逻辑必须有行内注释
4. 枚举值必须有中文说明

## 性能优化指南

1. 合理使用索引提高查询性能
2. 避免N+1查询问题
3. 使用批量操作替代循环单条操作
4. 使用缓存减少数据库访问
5. 大数据量处理时使用分页查询
6. 使用异步处理提高并发能力
7. 合理设置连接池参数

## 安全规范

1. 敏感数据（如密码、证件号）需要加密存储
2. API调用需要进行身份验证和授权
3. 防止SQL注入、XSS等常见安全问题
4. 日志中不应包含敏感信息
5. 错误响应不应暴露系统内部信息

## 测试规范

1. 单元测试覆盖率不低于70%
2. 关键业务逻辑必须有单元测试
3. 集成测试覆盖主要业务流程
4. 性能测试验证系统性能指标
5. 代码提交前必须通过所有测试

## 代码审查重点

在进行代码审查时，应重点关注以下方面：

1. **命名规范**: 类名、方法名、变量名是否符合项目规范
2. **接口定义**: Dubbo接口定义是否符合规范，包括注释、入参出参等
3. **接口实现**: 实现类是否位于正确的包下，是否使用了正确的注解
4. **服务层调用**: Service与Repository的职责是否分明，方法是否有必要的注释
5. **事务管理**: 是否正确使用了事务注解，事务传播行为是否合适
6. **异常处理**: 是否有统一的异常处理机制，是否合理使用了自定义异常
7. **日志规范**: 是否使用了正确的日志级别，日志内容是否合适
8. **代码质量**: 是否遵循了禁用组件规范，是否有重复代码

## 开发流程规范

### 开发环境要求
- Java 8
- Maven 3.6+
- IntelliJ IDEA或Eclipse
- Git版本控制

### 代码提交规范
1. 遵循接口设计规范，所有对外接口需有详细的接口文档
2. 代码提交前需通过单元测试和代码质量检查
3. 遵循代码分层原则，不允许跨层调用
4. 提交信息应清晰描述修改内容

### 分支管理规范
1. 主分支：master/main
2. 开发分支：develop
3. 功能分支：feature/功能名称
4. 修复分支：hotfix/问题描述

## 重构指导原则

### 重构目标
1. **提高代码可读性**: 清晰的命名和结构
2. **增强可维护性**: 降低耦合度，提高内聚性
3. **优化性能**: 减少不必要的计算和数据库访问
4. **统一代码风格**: 遵循项目规范

### 重构策略
1. **小步快跑**: 每次重构范围不宜过大
2. **保持功能不变**: 重构过程中不改变业务逻辑
3. **充分测试**: 重构前后都要有完整的测试覆盖
4. **文档同步**: 及时更新相关文档

## 常见问题和解决方案

### 性能问题
1. **数据库查询慢**: 检查索引、优化SQL、使用缓存
2. **内存占用高**: 检查对象创建、集合使用、缓存策略
3. **并发问题**: 使用线程池、异步处理、锁机制

### 业务问题
1. **数据一致性**: 合理使用事务、分布式锁
2. **异常处理**: 统一异常处理机制、错误码管理
3. **日志追踪**: 完善日志记录、链路追踪

## 附录

### 常用工具类
- `MoneyUtil`: 金额处理工具
- `DateUtils`: 日期处理工具
- `StringUtils`: 字符串处理工具
- `CollectionUtils`: 集合处理工具

### 常用配置
- 数据源配置
- 缓存配置
- 消息队列配置
- 服务注册配置

### 监控和告警
- 业务监控指标
- 系统性能监控
- 异常告警机制
- 日志分析工具