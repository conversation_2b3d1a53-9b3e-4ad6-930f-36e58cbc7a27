package com.howbuy.tms.high.orders.service.facade.search.queryhboneinlicaihideconf;

import com.howbuy.tms.common.constant.ExceptionCodes;
import com.howbuy.tms.common.enums.busi.YesOrNoEnum;
import com.howbuy.tms.common.message.MessageSource;
import com.howbuy.tms.common.utils.StringUtils;
import com.howbuy.tms.high.orders.facade.search.queryhboneinlicaihideconf.QueryHboneNoInLiCaiHideConfFacade;
import com.howbuy.tms.high.orders.facade.search.queryhboneinlicaihideconf.QueryHboneNoInLiCaiHideConfRequest;
import com.howbuy.tms.high.orders.facade.search.queryhboneinlicaihideconf.QueryHboneNoInLiCaiHideConfResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

/**
 * @Description:查询一账通是否在理财隐藏列表
 * @Author: yun.lu
 * Date: 2025/9/9 10:45
 */
@DubboService
@Service("queryHboneNoInLiCaiHideConfFacade")
@Slf4j
public class QueryHboneNoInLiCaiHideConfService implements QueryHboneNoInLiCaiHideConfFacade {
    @Value("${liCai_hide_hbOneNo}")
    private String liCaiHideHbOneNo;

    @Override
    public QueryHboneNoInLiCaiHideConfResponse execute(QueryHboneNoInLiCaiHideConfRequest queryHboneNoInLiCaiHideConfRequest) {
        QueryHboneNoInLiCaiHideConfResponse queryHboneNoInLiCaiHideConfResponse = new QueryHboneNoInLiCaiHideConfResponse();
        queryHboneNoInLiCaiHideConfResponse.setReturnCode(ExceptionCodes.SUCCESS);
        queryHboneNoInLiCaiHideConfResponse.setDescription(MessageSource.getMessageByCode(ExceptionCodes.SUCCESS));
        // 查询一账通隐藏列表
        String hbOneNo = queryHboneNoInLiCaiHideConfRequest.getHbOneNo();
        if (StringUtils.isBlank(liCaiHideHbOneNo)) {
            log.info("QueryHboneNoInLiCaiHideConfFacade-没有需要隐藏的一账通,hbOneNo={},shouYiHideHbOneNoList={}", hbOneNo, liCaiHideHbOneNo);
            queryHboneNoInLiCaiHideConfResponse.setInHideConf(YesOrNoEnum.NO.getCode());
            return queryHboneNoInLiCaiHideConfResponse;
        }
        List<String> liCaiHideHbOneNoList = Arrays.asList(liCaiHideHbOneNo.split(","));
        if (CollectionUtils.isNotEmpty(liCaiHideHbOneNoList) && liCaiHideHbOneNoList.contains(hbOneNo)) {
            log.info("QueryHboneNoInLiCaiHideConfFacade-在隐藏一账通,hbOneNo={},shouYiHideHbOneNoList={}", hbOneNo, liCaiHideHbOneNo);
            queryHboneNoInLiCaiHideConfResponse.setInHideConf(YesOrNoEnum.YES.getCode());
        } else {
            log.info("QueryHboneNoInLiCaiHideConfFacade-不在隐藏一账通,hbOneNo={},shouYiHideHbOneNoList={}", hbOneNo, liCaiHideHbOneNo);
            queryHboneNoInLiCaiHideConfResponse.setInHideConf(YesOrNoEnum.NO.getCode());
        }
        return queryHboneNoInLiCaiHideConfResponse;
    }
}
