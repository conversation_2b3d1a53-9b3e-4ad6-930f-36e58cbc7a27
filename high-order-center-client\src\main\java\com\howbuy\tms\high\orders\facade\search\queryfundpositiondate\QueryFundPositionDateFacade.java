/**
 * Copyright (c) 2025, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.orders.facade.search.queryfundpositiondate;

import com.howbuy.tms.common.client.BaseFacade;

/**
 * @api {dubbo} com.howbuy.tms.high.orders.facade.search.queryfundpositiondate.QueryFundPositionDateFacade.execute()
 * @apiVersion 1.0.0
 * @apiGroup QueryFundPositionDateFacade
 * @apiName execute
 * @apiDescription 查询基金产品持仓/清仓日期
 * @apiParam (请求参数) {String} hbOneNo 一账通号
 * @apiParam (请求参数) {List} disCodeList 分销渠道 HB000A001", "好买 ， HZ000N001", "好臻
 * @apiParam (请求参数) {String} searchOverseas 是否查询海外 1 是 0 否
 *
 * @apiParam (请求参数) {String} holdStatus 持仓/清仓标识 1:持仓 2:清仓 不传查询所有
 * @apiSuccess (返回参数) {List} holdDateList 持仓日期信息列表
 * @apiSuccess (返回参数) {List} liquidationDateList 清仓日期信息列表
 */
/**
 * @description: 清仓基金产品清仓日期查询接口
 * <AUTHOR>
 * @date 2025/9/4 22:10
 * @since JDK 1.8
 */
public interface QueryFundPositionDateFacade extends BaseFacade<QueryFundPositionDateRequest, QueryFundPositionDateResponse> {

}
