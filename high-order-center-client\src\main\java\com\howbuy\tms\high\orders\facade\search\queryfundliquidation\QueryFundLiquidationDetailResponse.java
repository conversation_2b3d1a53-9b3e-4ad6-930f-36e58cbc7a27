/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT Co., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.orders.facade.search.queryfundliquidation;

import com.howbuy.tms.high.orders.facade.common.OrderSearchBaseResponse;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @description: 清仓产品详情查询响应结果
 * <AUTHOR>
 * @date 2025/9/4 21:05
 * @since JDK 1.8
 */
@Setter
@Getter
public class QueryFundLiquidationDetailResponse extends OrderSearchBaseResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 基金名称
     */
    private String fundName;

    /**
     * 基金代码
     */
    private String fundCode;

    /**
     * 单位
     */
    private String unit;

    /**
     * 清仓收益
     */
    private BigDecimal clearUpIncome;

    /**
     * 清仓后涨跌幅
     */
    private BigDecimal clearUpRate;

    /**
     * 产品类型
     */
    private String productType;

    /**
     * 产品子类型
     */
    private String productSubType;

    /**
     * 累计持有天数
     */
    private String totalHoldDays;

    /**
     * 累计收益率
     */
    private BigDecimal accumIncomeRate;

    /**
     * 收益计算中标签
     * 1：有计算中标签 0：无计算中标签
     */
    private String incomeStatus;

    /**
     * 回款金额
     */
    private BigDecimal cashCollection;

    /**
     * 回款进度
     */
    private BigDecimal cashCollectionProgress;

    /**
     * 初始投资成本
     */
    private BigDecimal initInvestCost;

    /**
     * 清仓明细列表
     */
    private List<ClearDetailInfo> clearDetailList;

    /**
     * 清仓明细信息
     */
    @Setter
    @Getter
    public static class ClearDetailInfo implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 基金名称
         */
        private String fundName;

        /**
         * 基金代码
         */
        private String fundCode;

        /**
         * 单位
         */
        private String unit;

        /**
         * 清仓收益
         */
        private BigDecimal clearUpIncome;

        /**
         * 清仓后涨跌幅
         */
        private BigDecimal clearUpRate;

        /**
         * 产品类型
         */
        private String productType;

        /**
         * 产品子类型
         */
        private String productSubType;

        /**
         * 累计持有天数
         */
        private Integer totalHoldDays;

        /**
         * 累计收益率
         */
        private BigDecimal accumIncomeRate;

        /**
         * 收益计算中标签
         * 1：有计算中标签 0：无计算中标签
         */
        private String incomeStatus;
    }
}
