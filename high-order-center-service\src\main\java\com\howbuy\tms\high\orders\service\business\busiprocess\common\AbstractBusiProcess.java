/**
 * Copyright (c) 2018, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.orders.service.business.busiprocess.common;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.howbuy.crm.base.discount.DisCountUseTypeEnum;
import com.howbuy.dtms.common.enums.OrderStatusEnum;
import com.howbuy.interlayer.product.enums.BusiCodeEnum;
import com.howbuy.message.MessageService;
import com.howbuy.message.SimpleMessage;
import com.howbuy.tms.common.constant.ExceptionCodes;
import com.howbuy.tms.common.enums.busi.*;
import com.howbuy.tms.common.enums.database.*;
import com.howbuy.tms.common.exception.ValidateException;
import com.howbuy.tms.common.message.MessageSource;
import com.howbuy.tms.common.outerservice.acccenter.queryacckycinfo.QueryAccKycInfoOuterService;
import com.howbuy.tms.common.outerservice.acccenter.queryallcustinfo.QueryAllCustInfoContext;
import com.howbuy.tms.common.outerservice.acccenter.queryallcustinfo.QueryAllCustInfoOuterService;
import com.howbuy.tms.common.outerservice.acccenter.queryallcustinfo.QueryAllCustInfoResult;
import com.howbuy.tms.common.outerservice.acccenter.queryallcustinfo.bean.CustInfoBean;
import com.howbuy.tms.common.outerservice.acccenter.querycustbankcard.QueryCustBankCardResult;
import com.howbuy.tms.common.outerservice.acccenter.querycustsensitiveinfo.QueryCustSensitiveInfoContext;
import com.howbuy.tms.common.outerservice.acccenter.querycustsensitiveinfo.QueryCustSensitiveInfoOutService;
import com.howbuy.tms.common.outerservice.acccenter.querycustsensitiveinfo.QueryCustSensitiveInfoResult;
import com.howbuy.tms.common.outerservice.cc.center.queryhboneno.QueryHbOneNoOuterService;
import com.howbuy.tms.common.outerservice.ccmessage.sendmessage.SendAppInboxMessageOuterService;
import com.howbuy.tms.common.outerservice.ccmessage.sendmessage.dto.TemplateMessageDTO;
import com.howbuy.tms.common.outerservice.ccmessage.sendmessage.enums.HighOrderTemplateIdEnum;
import com.howbuy.tms.common.outerservice.howbuyweb.querymessageswitch.enums.MessageBusinessTypeEnum;
import com.howbuy.tms.common.outerservice.interlayer.hzFundAmtLockCfg.HzFundAmtLockCfgService;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.QueryHighProductOuterService;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.*;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.context.HighConfirmAndPaymentReceiptParamContext;
import com.howbuy.tms.common.outerservice.interlayer.queryproductinfo.bean.ProductAppointmentInfoBean;
import com.howbuy.tms.common.utils.DateUtils;
import com.howbuy.tms.common.utils.StringUtils;
import com.howbuy.tms.common.validator.password.PasswdValidator;
import com.howbuy.tms.high.orders.dao.po.*;
import com.howbuy.tms.high.orders.dao.vo.CancelOrderVo;
import com.howbuy.tms.high.orders.dao.vo.QueryDealOrderMsgVo;
import com.howbuy.tms.high.orders.facade.common.OrderTradeBaseRequest;
import com.howbuy.tms.high.orders.facade.trade.subsorpur.BaseMergeSubsOrPurRequest;
import com.howbuy.tms.high.orders.service.business.distxpwdvalidate.DisTxPwdValidateService;
import com.howbuy.tms.high.orders.service.business.message.HighMessageBean;
import com.howbuy.tms.high.orders.service.business.message.MsgNotifySendService;
import com.howbuy.tms.high.orders.service.business.ordercreater.OrderCreateBean;
import com.howbuy.tms.high.orders.service.business.ordercreater.OrderCreateContext;
import com.howbuy.tms.high.orders.service.common.utils.BigDecimalUtils;
import com.howbuy.tms.high.orders.service.repository.CustBooksRepository;
import com.howbuy.tms.high.orders.service.repository.DealOrderRepository;
import com.howbuy.tms.high.orders.service.repository.HighDealOrderDtlRepository;
import com.howbuy.tms.high.orders.service.repository.HighOrderAppointinfoRepository;
import org.apache.commons.collections.CollectionUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * <AUTHOR>
 * @description:(抽象业务处理类)
 * @reason:
 * @date 2018年3月9日 下午4:18:37
 * @since JDK 1.6
 */
public abstract class AbstractBusiProcess {
    protected static Logger logger = LogManager.getLogger(AbstractBusiProcess.class);

    @Value("${highCounterInstOrder.queue}")
    private String highCounterInstOrderQueue;
    @Value("${high.queue.cxg.pay.cancel.order}")
    private String cxgPayCancelOrderQueueName;
    @Value("${queue.quota.control}")
    private String quotaControlQueue;
    @Resource
    private QueryAllCustInfoOuterService queryAllCustInfoOuterService;
    @Autowired
    private DisTxPwdValidateService disTxPwdValidateService;

    @Autowired
    protected HighDealOrderDtlRepository highDealOrderDtlRepository;

    @Autowired
    protected HighOrderAppointinfoRepository highOrderAppointinfoRepository;

    @Autowired
    protected QueryAccKycInfoOuterService queryAccKycInfoOuterService;

    @Autowired
    private QueryCustSensitiveInfoOutService queryCustSensitiveInfoOutService;

    @Autowired
    private QueryHighProductOuterService queryHighProductOuterService;

    @Autowired
    private DealOrderRepository dealOrderRepository;

    @Autowired
    private MsgNotifySendService msgNotifySendService;

    @Autowired
    private CustBooksRepository custBooksRepository;

    @Autowired
    private HzFundAmtLockCfgService hzFundAmtLockCfgService;

    @Resource
    private SendAppInboxMessageOuterService sendAppInboxMessageOuterService;

    @Resource
    private QueryHbOneNoOuterService queryHbOneNoOuterService;

    /**
     * getCustInfo:(获取客户信息)
     *
     * @param txAcctNo
     * @param cpAcctNo
     * @return
     * <AUTHOR>
     * @date 2018年3月9日 下午7:22:11
     */
    protected QueryAllCustInfoResult getCustInfo(String txAcctNo, String cpAcctNo, String disCode) {
        QueryAllCustInfoContext custInfoCtx = new QueryAllCustInfoContext();
        custInfoCtx.setTxAcctNo(txAcctNo);
        custInfoCtx.setCpAcctNo(cpAcctNo);
        if (!StringUtils.isBlank(disCode)) {
            custInfoCtx.setDisCode(disCode);
        }
        return queryAllCustInfoOuterService.queryCustInfo(custInfoCtx);
    }

    /**
     * 获取真实的身份证号
     *
     * @param custInfo
     * @return void
     * @author: huaqiang.liu
     * @date: 2021/4/23 13:43
     * @since JDK 1.8
     */
    protected void getRealIdCardNo(BaseMergeSubsOrPurRequest request, CustInfoBean custInfo, List<String> idNoSensitive) {
        if (!idNoSensitive.isEmpty()) {
            custInfo.setIdNo(idNoSensitive.get(0));
            return;
        }
        // 仅个人身份证需要查询明文，后面计算年龄用
        if (InvstTypeEnum.INDI.getCode().equals(custInfo.getInvstType())
                && IdTypeEnum.IDCARD.getValue().equals(custInfo.getIdType())) {
            QueryCustSensitiveInfoContext ctx = new QueryCustSensitiveInfoContext();
            ctx.setTxAcctNo(request.getTxAcctNo());
            ctx.setDisCode(request.getDisCode());
            QueryCustSensitiveInfoResult res = queryCustSensitiveInfoOutService.queryCustSensitiveInfo(ctx);
            custInfo.setIdNo(res.getIdNo());
            // 记录明文证件号，下次无需重复查询
            idNoSensitive.add(res.getIdNo());
        }
    }

    /**
     * validateTxPwd:(校验交易密码)
     *
     * @param disCode
     * @param txAcctNo
     * @param txPwd
     * <AUTHOR>
     * @date 2018年3月12日 下午3:21:23
     */
    protected void validateTxPwd(String disCode, String txAcctNo, String txPwd) {
        // 查询分销是否需要校验交易密码
        boolean disTxPwdValidateFlag = disTxPwdValidateService.queryDisTxPwdValidateFlag(disCode);
        // 根据分销机构标识, 校验交易密码
        PasswdValidator.validateTxPassword(txAcctNo, txPwd, disTxPwdValidateFlag);
    }

    /**
     * validateTxPwdByTxChannel:(校验交易密码)
     *
     * @param disCode   分销编码
     * @param txAcctNo  交易账号
     * @param txChannel 交易渠道
     * @param txPwd     密码
     * <AUTHOR>
     * @date 2018年3月12日 下午3:27:07
     */
    protected void validateTxPwdByTxChannel(String disCode, String txAcctNo, String txChannel, String txPwd) {
        // 机构/柜台/其他,不需要校验验证码
        List<Object> notNeedValidateTxChannel = new ArrayList<>();
        notNeedValidateTxChannel.add(TxChannelEnum.COUNTER.getCode());
        notNeedValidateTxChannel.add(TxChannelEnum.INST.getCode());
        notNeedValidateTxChannel.add(TxChannelEnum.OTHERS.getCode());
        if (notNeedValidateTxChannel.contains(txChannel)) {
            return;
        }
        // 校验交易密码
        validateTxPwd(disCode, txAcctNo, txPwd);
    }

    /**
     * validateAppointDealNo:(验证预约订单号是否已使用)
     *
     * @param appointmentDealNo
     * <AUTHOR>
     * @date 2018年5月22日 下午3:02:23
     */
    protected void validateAppointDealNo(String appointmentDealNo) {
        int num = highOrderAppointinfoRepository.selectUsedAppointmentDealNo(appointmentDealNo);
        if (num > 0) {
            throw new ValidateException(ExceptionCodes.HIGH_APPOINTMENTDEALNO_HAS_USED,
                    MessageSource.getMessageByCode(ExceptionCodes.HIGH_APPOINTMENTDEALNO_HAS_USED));
        }
    }

    /**
     * 设置电子合同版本号
     *
     * @param highProductInfoBean
     * @param context
     * @return void
     * @author: huaqiang.liu
     * @date: 2021/4/15 13:18
     * @since JDK 1.8
     */
    protected void setContractVersion(HighProductInfoBean highProductInfoBean, OrderCreateContext context) {
        // 设置电子合同版本号
        if (highProductInfoBean.getAgreementConfList() != null && !highProductInfoBean.getAgreementConfList().isEmpty()) {
            String contractVersion = highProductInfoBean.getAgreementConfList().get(0).getContractVersion();
            context.setContractVersion(StringUtils.isBlank(contractVersion) ? "001" : contractVersion);
        }
    }

    /**
     * 发送柜台下机构单消息
     */
    public void sendCounterOrderMessage(String externalDealNo, String dealNo, String txAcctNo, String disCode) {
        try {
            //{"dealAppNo":"2621041208543400000508653","txAcctNo":"1439213653","disCode":"HB000A001","checkFlag":"2"}
            JSONObject data = new JSONObject();
            data.put("dealAppNo", externalDealNo);
            data.put("dealNo", dealNo);
            data.put("txAcctNo", txAcctNo);
            data.put("disCode", disCode);
            // 审核通过
            data.put("checkFlag", "1");

            SimpleMessage message = new SimpleMessage();
            message.setContent(data.toJSONString());

            MessageService.getInstance().send(highCounterInstOrderQueue, message);
            logger.info("sendCounterOrderMessage send success. externalDealNo:{} txAcctNo:{}", externalDealNo, txAcctNo);
        } catch (Exception e) {
            logger.error("sendCounterOrderMessage externalDealNo:{} txAcctNo:{} err:{}", externalDealNo, txAcctNo, e.getMessage(), e);
        }
    }

    /**
     * processMutiCard:(多卡交易处理)
     *
     * @param bankInfoMap
     * @param memo        MP|bankcode|bankacct|amt,bankcode|bankacct|amt
     * <AUTHOR>
     * @date 2017年10月16日 下午2:38:27
     */
    public void processMutiCard(Map<String, QueryCustBankCardResult> bankInfoMap, String memo) {

        // 迁移后的多卡交易处理
        if (!org.apache.commons.lang3.StringUtils.isEmpty(memo)) {
            boolean isMutiCard = memo.startsWith("MP|");
            if (isMutiCard) {
                String[] cardInfoList = getMutiCardList(memo);
                for (String cardInfo : cardInfoList) {
                    String[] cardItemList = cardInfo.split("\\|");
                    if (cardItemList.length > 0) {
                        String cpAcct = cardItemList[0];
                        if (!bankInfoMap.containsKey(cpAcct)) {
                            QueryCustBankCardResult queryCustBankCardResult = new QueryCustBankCardResult();
                            queryCustBankCardResult.setCpAcctNo(cpAcct);
                            bankInfoMap.put(cpAcct, queryCustBankCardResult);
                        }
                    }
                }
            }
        }

    }

    /**
     * getMutiCardList:(从备注重解析多卡信息)
     *
     * @param memo
     * @return
     * <AUTHOR>
     * @date 2017年10月16日 下午3:34:18
     */
    private String[] getMutiCardList(String memo) {
        boolean isMutiCard = memo.startsWith("MP|");
        if (isMutiCard) {
            String mutiCardInfo = memo.replaceAll("MP\\|", "");
            String[] cardInfoList = mutiCardInfo.split(",");
            return cardInfoList;
        }
        return null;
    }

    /**
     * buildMemo:(重新构建备注字段)
     *
     * @param bankInfoMap
     * @param memo        MP|bankcode|bankacct|amt,bankcode|bankacct|amt
     * @return
     * <AUTHOR>
     * @date 2017年10月16日 下午3:37:06
     */
    public String buildMemo(Map<String, QueryCustBankCardResult> bankInfoMap, String memo) {
        if (!org.apache.commons.lang3.StringUtils.isEmpty(memo)) {
            if (memo.startsWith("MP|")) {
                String[] cardInfoArr = getMutiCardList(memo);
                StringBuilder sb = new StringBuilder();
                if (bankInfoMap != null && bankInfoMap.size() > 0) {
                    sb.append("MP|");
                    for (String cardInfo : cardInfoArr) {
                        if (!org.apache.commons.lang3.StringUtils.isEmpty(cardInfo)) {
                            String[] cardItemArr = cardInfo.split("\\|");
                            if (cardItemArr != null && cardItemArr.length == 2) {
                                String cpAcctNo = cardItemArr[0];
                                String appAmt = cardItemArr[1];
                                QueryCustBankCardResult queryCustBankCardResult = bankInfoMap.get(cpAcctNo);
                                if (queryCustBankCardResult != null) {
                                    sb.append(queryCustBankCardResult.getBankCode()).append("|").append(queryCustBankCardResult.getBankName()).append("|")
                                            .append(queryCustBankCardResult.getBankAcct()).append("|").append(appAmt).append(",");
                                }
                            }
                        }
                    }
                }
                if (sb.toString().lastIndexOf(",") >= 0) {
                    return sb.substring(0, sb.toString().lastIndexOf(","));
                } else {
                    return sb.toString();
                }

            } else {
                return memo;
            }

        } else {
            return null;
        }
    }

    /**
     * getBusiCode:(获取业务码)
     *
     * @param highProductBaseBean
     * @param productAppointmentInfoBean
     * @param taTradeDt
     * @return
     * <AUTHOR>
     * @date 2018年1月9日 下午4:56:30
     */
    public String getBusiCode(HighProductBaseInfoBean highProductBaseBean, ProductAppointmentInfoBean productAppointmentInfoBean, String taTradeDt) {
        //默认是认购
        String busiCode = BusiCodeEnum.PURCHASE.getCode();
        if ("1".equals(convertSupportAdvanceFlag(highProductBaseBean.getIsScheduledTrade()))) {
            if (productAppointmentInfoBean != null) {
                busiCode = BusinessCodeEnum.getByMCode(productAppointmentInfoBean.getmBusiCode()).getCode();
            }
        } else {
            HighProductStatInfoBean highProductStatInfoBean = queryHighProductOuterService.getHighProductStatInfo(highProductBaseBean.getFundCode(), taTradeDt);
            if (highProductStatInfoBean != null) {
                busiCode = convertToBusiCode(highProductStatInfoBean.getBuyStatus());
            }
        }

        return busiCode;
    }

    /**
     * convertAdvanceSupportFlag:(转换支持提前下单标识)
     *
     * @return 1-支持 0-不支持
     * <AUTHOR>
     * @date 2018年1月5日 上午11:37:20
     */
    protected String convertSupportAdvanceFlag(String supportAdvanceFlag) {
        if (IsScheduledTradeEnum.SupportBuyAdvance.getCode().equals(supportAdvanceFlag) || IsScheduledTradeEnum.SupportRedeemAdvance.getCode().equals(supportAdvanceFlag) || IsScheduledTradeEnum.SupportBuyAndRedeemAdvance.getCode().equals(supportAdvanceFlag)) {
            return "1";
        } else {
            return "0";
        }
    }

    /**
     * getBusiCode:(根据购买状态获取busiCode)
     *
     * @param buyStatus
     * @return
     * <AUTHOR>
     * @date 2018年1月5日 下午5:40:26
     */
    protected String convertToBusiCode(String buyStatus) {
        if ("1".equals(buyStatus)) {
            return BusiCodeEnum.IPO.getCode();
        } else if ("2".equals(buyStatus)) {
            return BusiCodeEnum.PURCHASE.getCode();
        } else {
            //默认申购
            return BusiCodeEnum.PURCHASE.getCode();
        }
    }

    /**
     * calFee:(计算手续费)
     *
     * @param amt
     * @param disCount
     * @return
     * <AUTHOR>
     * @date 2018年1月5日 下午7:32:28
     */
    public BigDecimal calFee(BigDecimal amt, BigDecimal disCount, HighProductFeeRateBean highProductFeeRateBean, String discountUseType, BigDecimal discountAmt) {
        logger.info("QueryPreBookListFacadeService|calFee|amt:{}, disCount:{}, feeRateInfo:{},discountUseType={},discountAmt={}", amt, disCount, JSON.toJSONString(highProductFeeRateBean),discountUseType,discountAmt);
        if (disCount == null) {
            disCount = BigDecimal.ONE;
        }
        if (discountAmt == null) {
            discountAmt = BigDecimal.ZERO;
        }
        if (highProductFeeRateBean == null) {
            return null;
        }
        if (GetFeeRateMethodEnum.FIX.getCode().equals(highProductFeeRateBean.getGetFeeRateMethod())) {
            logger.info("getFee-固定手续费:{},最终折扣:{}", highProductFeeRateBean.getConstantFee(), disCount);
            return highProductFeeRateBean.getConstantFee().multiply(disCount);
        }
        if (amt == null) {
            return null;
        }
        if (highProductFeeRateBean.getFeeRate() == null) {
            return BigDecimal.ZERO;
        }
        if (DisCountUseTypeEnum.DISCOUNT_AMOUNT.getCode().equals(discountUseType)) {
            return amt.multiply(highProductFeeRateBean.getFeeRate()).subtract(discountAmt);
        } else {
            return amt.multiply(disCount).multiply(highProductFeeRateBean.getFeeRate());
        }

    }

    /**
     * convertBusiType:(转换交易类型)
     *
     * @param tradeType 1-购买；2-追加；3-赎回
     * @return
     * <AUTHOR>
     * @date 2018年1月5日 下午1:17:32
     */
    public String convertBusiType(String tradeType) {
        if (PreTradeTypeEnum.BUY.getCode().equals(tradeType) || PreTradeTypeEnum.SUB_BUY.getCode().equals(tradeType)) {
            return "0";
        } else if (PreTradeTypeEnum.SELL.getCode().equals(tradeType)) {
            return "1";
        }
        return null;
    }

    /**
     * getHighProductNavMap:(批量获取基金净值map)
     *
     * @param midProductIds
     * @return
     * <AUTHOR>
     * @date 2017年11月20日 下午5:00:24
     */
    public Map<String, HighProductNavBean> getHighProductNavMap(List<String> midProductIds) {
        // 批量查询基金基金净值信息
        Map<String, HighProductNavBean> highProductNavMap = new HashMap<>();
        List<HighProductNavBean> highProductNavBeanList = null;
        try {
            highProductNavBeanList = queryHighProductOuterService.getHighProductNavInfo(new ArrayList<>(midProductIds));
        } catch (Exception e) {
            logger.error("QueryAcctBalanceFacadeService|queryHighProductOuterService.getHighProductNavInfo,fundCode={}, error ={}", JSON.toJSONString(midProductIds), e);
        }

        if (!CollectionUtils.isEmpty(highProductNavBeanList)) {
            for (HighProductNavBean highProductNavBean : highProductNavBeanList) {
                highProductNavMap.put(highProductNavBean.getFundCode(), highProductNavBean);
            }
        }
        return highProductNavMap;
    }

    /**
     * sendDealActualMsg:(发送订单实时信息)
     *
     * @param dealNo 订单号
     * <AUTHOR>
     * @date 2017年12月20日 下午2:37:07
     */
    protected void sendDealActualMsg(String dealNo, String isOriginalCancelOrder) {
        logger.info("sendDealActualMsg-dealNo:{},isOriginalCancelOrder={}", dealNo, isOriginalCancelOrder);
        try {
            QueryDealOrderMsgVo msgVo = dealOrderRepository.selectDealOrderMsgByDealNo(dealNo);
            if (msgVo != null) {
                List<HighMessageBean> highMessageBeanList = new ArrayList<HighMessageBean>();
                HighMessageBean highMessageBean = new HighMessageBean();
                BeanUtils.copyProperties(msgVo, highMessageBean);
                highMessageBean.setIsOriginalCancelOrder(isOriginalCancelOrder);
                highMessageBeanList.add(highMessageBean);
                msgNotifySendService.sendHighActualMessage(
                        JSON.toJSONString(highMessageBeanList, SerializerFeature.WriteMapNullValue, SerializerFeature.WriteNullStringAsEmpty));
            }
        } catch (Exception e) {
            logger.error("sendDealActualMsg|error:", e);
        }
        // 发撤单送站内信消息
        try {
            sendCancelAppNoticeMsg(dealNo);
        } catch (Exception e) {
            logger.error("sendCancelAppNoticeMsg|error:", e);
        }
    }

    /**
     * @description: 发送撤单站内信消息
     * @param dealNo 订单号
     * @return void
     * @author: jinqing.rao
     * @date: 2025/9/2 15:18
     * @since JDK 1.8
     */
    private void sendCancelAppNoticeMsg(String dealNo) {
        // 查询订单信息
        DealOrderPo orderPo = dealOrderRepository.selectByDealNo(dealNo);
        // 查询订单明细信息
        HighDealOrderDtlPo highDealOrderDtlPo = highDealOrderDtlRepository.selectDtlByDealNo(dealNo);
        // 根据交易账号查询一账通账号
        String hbOneNo = queryHbOneNoOuterService.queryHbOneNoByTxAcctNo(orderPo.getTxAcctNo());
        if(StringUtils.isBlank(hbOneNo)){
            logger.error("sendCancelAppNoticeMsg|error:根据交易账号查询一账通账号为空,txAcctNo={}", orderPo.getTxAcctNo());
            return;
        }
        // 产品名称
        String pname = orderPo.getProductName();
        // 申请金额
        String amount = BigDecimalUtils.formatToThousandths(orderPo.getAppAmt(),2, RoundingMode.DOWN);
        // 申请份额
        String share = BigDecimalUtils.formatToThousandths( orderPo.getAppVol(),2, RoundingMode.DOWN);

        // 预计交易日期
        String tradedate = DateUtils.formatDateConvert(highDealOrderDtlPo.getTaTradeDt(),DateUtils.YYYYMMDD,DateUtils.YYYY_MM_DD);
        // 订单状态
        String orderStatusDesc = OrderStatusEnum.getEnumByCode(orderPo.getOrderStatus()).getDesc();
        JSONObject jsonObject = new JSONObject();
        String templateId = null;
        String title = null;
        // 购买订单
        List<String> buyBusiCodeList = getBuyBusiCodeList();
        if(buyBusiCodeList.contains(highDealOrderDtlPo.getmBusiCode())){
             templateId = HighOrderTemplateIdEnum.PURCHASE_CANCEL_NOTIFY_TEMPLATE_ID.getCode();
             title = HighOrderTemplateIdEnum.PURCHASE_CANCEL_NOTIFY_TEMPLATE_ID.getProductNameTitle(pname);
            jsonObject.put("pname", pname);
            jsonObject.put("amount", amount);
        }
        // 赎回订单类型
        List<String> sellBusiCodeList = getRedeemBusiCodeList();
        if(sellBusiCodeList.contains(highDealOrderDtlPo.getmBusiCode())){
             templateId = HighOrderTemplateIdEnum.REDEEM_CANCEL_NOTIFY_TEMPLATE_ID.getCode();
             title = HighOrderTemplateIdEnum.REDEEM_CANCEL_NOTIFY_TEMPLATE_ID.getProductNameTitle(pname);
            jsonObject.put("pname", pname);
            jsonObject.put("share", share);
        }
        if(templateId == null){
            logger.error("sendCancelAppNoticeMsg|templateId is null,dealNo:{}", dealNo);
            return;
        }
        jsonObject.put("tradedate", tradedate);
        jsonObject.put("orderStatusDesc", orderStatusDesc);
        TemplateMessageDTO templateMessageDTO = new TemplateMessageDTO();
        templateMessageDTO.setTemplateId(templateId);
        templateMessageDTO.setContent(jsonObject.toJSONString());
        templateMessageDTO.setHboneNo(hbOneNo);
        templateMessageDTO.setBusinessType(MessageBusinessTypeEnum.TRADE_REMIND.getCode());
        templateMessageDTO.setTitle(title);
        sendAppInboxMessageOuterService.sendAppInboxMessageByMessageSwitch(templateMessageDTO);
    }

    /**
     * 删除好臻金额锁定配置
     *
     * @param cancelOrders 取消订单信息
     * @param hbOneNo      一账通
     */
    public void deleteHzFundAmtLockCfg(List<CancelOrderVo> cancelOrders, String hbOneNo) {
        cancelOrders.stream().flatMap(cancelOrder -> cancelOrder.getFundDealOrderDtlPos().stream()).forEach(fundDealOrderDtlPo -> {
            if (fundDealOrderDtlPo.getAppointId() != null) {
                hzFundAmtLockCfgService.deleteHzFundAmtLockCfg(hbOneNo, fundDealOrderDtlPo.getFundCode(), fundDealOrderDtlPo.getAppointId());
            } else {
                logger.info("删除好臻金额锁定配置,预约日历id是空的,dealNo:{}", fundDealOrderDtlPo.getDealNo());
            }
        });
    }


    /**
     * sendCxgPayCancelMsg:撤单发送消息，处理储蓄罐撤单业务
     *
     * @param dealNo
     * <AUTHOR>
     * @date 2019年11月14日 上午9:30:12
     */
    public void sendCxgPayCancelMsg(OrderTradeBaseRequest request, String dealNo, String cancelChannelCode) {
        logger.info("CancelOrderFacadeService|sendCxgPayCancelMsg|dealNo:{}", dealNo);

        try {
            JSONObject json = new JSONObject();
            json.put("dealNo", dealNo);
            json.put("appDt", request.getAppDt());
            json.put("appTm", request.getAppTm());
            // 柜台强撤-1 网站撤单-0
            json.put("cancelChannelCode", cancelChannelCode);

            msgNotifySendService.sendMsgWithContext(json.toJSONString(), cxgPayCancelOrderQueueName);

        } catch (Exception e) {
            logger.error("CancelOrderFacadeService|sendCxgPayCancelMsg|error:", e);
        }

    }

    /**
     * sendQuotaControlMsg:(发送额度控制消息)
     *
     * @param dealNo
     * <AUTHOR>
     * @date 2018年5月24日 上午10:36:35
     */
    public void sendQuotaControlMsg(String dealNo) {
        logger.info("sendQuotaControlMsg|dealNo:{}", dealNo);

        try {
            JSONObject json = new JSONObject();
            json.put("dealNo", dealNo);
            json.put("busiProcessDirect", BusiProcessDirectEnum.REVERSE.getCode());

            msgNotifySendService.sendMsgWithContext(json.toJSONString(), quotaControlQueue);

        } catch (Exception e) {
            logger.error("CancelOrderFacadeService|sendQuotaControlMsg|error:", e);
        }

    }

    /**
     * sendDealActualMsg:(发送订单实时信息)
     *
     * @param orderCreateBean
     * <AUTHOR>
     * @date 2017年12月20日 下午2:37:07
     */
    protected void sendDealActualMsg(OrderCreateBean orderCreateBean) {
        logger.info("开始发送外部系统订单实时消息,dealNo={}", orderCreateBean.getOrderPo().getDealNo());
        try {
            HighMessageBean highMessageBean = new HighMessageBean();
            highMessageBean.setDealNo(orderCreateBean.getOrderPo().getDealNo());
            highMessageBean.setTxAcctNo(orderCreateBean.getOrderPo().getTxAcctNo());
            highMessageBean.setExternalDealNo(orderCreateBean.getOrderPo().getExternalDealNo());

            List<HighDealOrderDtlPo> orderDtlList = orderCreateBean.getOrderDtlList();
            if (!org.springframework.util.CollectionUtils.isEmpty(orderDtlList)) {
                highMessageBean.setTradeType(orderCreateBean.getOrderDtlList().get(0).getmBusiCode());
            }

            DealOrderExtendPo dealOrderExtendPo = orderCreateBean.getDealOrderExtendPo();
            if (dealOrderExtendPo != null) {
                highMessageBean.setAppointmentDealNo(orderCreateBean.getDealOrderExtendPo().getAppointmentDealNo());
            }

            List<HighMessageBean> highMessageBeanList = new ArrayList<HighMessageBean>();
            highMessageBeanList.add(highMessageBean);
            msgNotifySendService.sendHighActualMessage(JSON.toJSONString(highMessageBeanList));
        } catch (Exception e) {
            logger.info("sendDealActualMsg|error:", e);
        }
    }

    /**
     * @description: 发送站内信消息
     * @param orderCreateBean
     * @return void
     * @author: jinqing.rao
     * @date: 2025/9/2 13:37
     * @since JDK 1.8
     */
    public void sendAppInBoxCreateOrderMsg(OrderCreateBean orderCreateBean) {
        try {
            // 订单信息
            DealOrderPo orderPo = orderCreateBean.getOrderPo();
            // 订单明细信息
            List<HighDealOrderDtlPo> orderDtlList = orderCreateBean.getOrderDtlList();
            // 支付订单
            PaymentOrderPo paymentOrderPo = orderCreateBean.getPaymentOrderPo();
            if(null == orderPo || CollectionUtils.isEmpty(orderDtlList)){
                logger.info("订单信息缺失,不推送APP站内信消息");
                return;
            }
            // 默认获取第一条
            HighDealOrderDtlPo highDealOrderDtlPo = orderDtlList.get(0);
            // 购买类
            List<String> buyBusiCodeList = getBuyBusiCodeList();
            // 赎回类
            List<String> redeemBusiCodeList = getRedeemBusiCodeList();
            if(!redeemBusiCodeList.contains(highDealOrderDtlPo.getmBusiCode()) && !buyBusiCodeList.contains(highDealOrderDtlPo.getmBusiCode())){
                logger.info("订单业务类型不支持,不推送APP站内信消息,orderNo:{},mBusiCode:{}",highDealOrderDtlPo.getDealNo(),highDealOrderDtlPo.getmBusiCode());
                return;
            }
            // 根据交易账号查询一账通账号
            String hbOneNo = queryHbOneNoOuterService.queryHbOneNoByTxAcctNo(orderPo.getTxAcctNo());
            if(StringUtils.isBlank(hbOneNo)){
                logger.error("sendAppNoticeMsg|error:根据交易账号查询一账通账号为空,txAcctNo={}", orderPo.getTxAcctNo());
                return;
            }
            // 购买类的下单通知
            sendBuyAppInBoxMessage(highDealOrderDtlPo, orderPo, paymentOrderPo, hbOneNo);

            // 赎回类的下单通知
            sendReedemAppInboxMessage(highDealOrderDtlPo, orderPo, hbOneNo);
        } catch (Exception e) {
            logger.info("sendAppInBoxCreateOrderMsg|error:", e);
        }
    }

    /**
     * @description: 发送赎回类下单通知站内信消息
     * @param highDealOrderDtlPo	
     * @param orderPo	
     * @param hbOneNo
     * @return void
     * @author: jinqing.rao
     * @date: 2025/9/16 13:52
     * @since JDK 1.8
     */
    private void sendReedemAppInboxMessage(HighDealOrderDtlPo highDealOrderDtlPo, DealOrderPo orderPo, String hbOneNo) {
        List<String> redeemBusiCodeList = getRedeemBusiCodeList();
        if(redeemBusiCodeList.contains(highDealOrderDtlPo.getmBusiCode())){
            // 产品名称
            String pname = orderPo.getProductName();
            // 申请份额
            String share = BigDecimalUtils.formatToThousandths(orderPo.getAppVol(),2, RoundingMode.DOWN);
            // 预计交易日期
            String tradedate = DateUtils.formatDateConvert(highDealOrderDtlPo.getTaTradeDt(),DateUtils.YYYYMMDD,DateUtils.YYYY_MM_DD);
            // 赎回方式
            String redeemTypeName = RedeemDirectionEnum.getName(highDealOrderDtlPo.getRedeemDirection());

            JSONObject jsonObject = new JSONObject();
            jsonObject.put("pname", pname);
            jsonObject.put("share", share);
            jsonObject.put("tradedate", tradedate);
            jsonObject.put("redeemTypeName", redeemTypeName);
            TemplateMessageDTO templateMessageDTO = new TemplateMessageDTO();
            templateMessageDTO.setTemplateId(HighOrderTemplateIdEnum.REDEEM_NOTIFY_TEMPLATE_ID.getCode());
            templateMessageDTO.setBusinessType(MessageBusinessTypeEnum.TRADE_REMIND.getCode());
            templateMessageDTO.setTitle(HighOrderTemplateIdEnum.REDEEM_NOTIFY_TEMPLATE_ID.getProductNameTitle(pname));
            templateMessageDTO.setContent(jsonObject.toJSONString());
            templateMessageDTO.setHboneNo(hbOneNo);
            sendAppInboxMessageOuterService.sendAppInboxMessageByMessageSwitch(templateMessageDTO);
        }
    }

    /**
     * @description: 购买类下单APP的站内信消息
     * @param highDealOrderDtlPo	
     * @param orderPo	
     * @param paymentOrderPo	
     * @param hbOneNo
     * @return void
     * @author: jinqing.rao
     * @date: 2025/9/16 13:50
     * @since JDK 1.8
     */
    private void sendBuyAppInBoxMessage(HighDealOrderDtlPo highDealOrderDtlPo, DealOrderPo orderPo, PaymentOrderPo paymentOrderPo, String hbOneNo) {
        List<String> buyBusiCodeList = getBuyBusiCodeList();
        if(buyBusiCodeList.contains(highDealOrderDtlPo.getmBusiCode())){
            // 产品名称
            String pname = orderPo.getProductName();
            // 申请金额
            String amount = BigDecimalUtils.formatToThousandths(orderPo.getAppAmt(),2, RoundingMode.DOWN);
            // 手续费
            String fee = BigDecimalUtils.formatToThousandths(highDealOrderDtlPo.getEsitmateFee(),2, RoundingMode.DOWN);
            if(null != highDealOrderDtlPo.getEsitmateFee() && highDealOrderDtlPo.getEsitmateFee().compareTo(BigDecimal.ZERO) >= 0){
                amount = amount.concat("（含手续费:"+fee+"元）");
            }
            // 预计交易日期
            String tradedate = DateUtils.formatDateConvert(highDealOrderDtlPo.getTaTradeDt(),DateUtils.YYYYMMDD,DateUtils.YYYY_MM_DD);            // 支付方式
            String paymentTypeName = PaymentTypeEnum.getName(paymentOrderPo.getPaymentType());

            JSONObject jsonObject = new JSONObject();
            jsonObject.put("pname", pname);
            jsonObject.put("amount", amount);
            jsonObject.put("tradedate", tradedate);
            jsonObject.put("paymentTypeName", paymentTypeName);
            TemplateMessageDTO templateMessageDTO = new TemplateMessageDTO();
            templateMessageDTO.setTemplateId(HighOrderTemplateIdEnum.PURCHASE_NOTIFY_TEMPLATE_ID.getCode());
            templateMessageDTO.setBusinessType(MessageBusinessTypeEnum.TRADE_REMIND.getCode());
            templateMessageDTO.setTitle(HighOrderTemplateIdEnum.PURCHASE_NOTIFY_TEMPLATE_ID.getProductNameTitle(pname));
            templateMessageDTO.setContent(jsonObject.toJSONString());
            templateMessageDTO.setHboneNo(hbOneNo);
            sendAppInboxMessageOuterService.sendAppInboxMessageByMessageSwitch(templateMessageDTO);
        }
    }

    /**
     * @description: 获取赎回订单
     * @param
     * @return java.util.List<java.lang.String>
     * @author: jinqing.rao
     * @date: 2025/9/2 18:05
     * @since JDK 1.8
     */
    private static List<String> getRedeemBusiCodeList() {
        return Collections.singletonList(BusinessCodeEnum.REDEEM.getMCode());
    }

    /**
     * @description: 购买类订单
     * @param
     * @return java.util.List<java.lang.String>
     * @author: jinqing.rao
     * @date: 2025/9/2 18:04
     * @since JDK 1.8
     */
    private static List<String> getBuyBusiCodeList() {
        return Arrays.asList(BusinessCodeEnum.SUBS.getMCode(), BusinessCodeEnum.PURCHASE.getMCode());
    }

    /**
     * getHighConfirmAndPaymentReceiptDt:(查询基金预计确认日期)
     *
     * @param submitTaDt      上报TA日期
     * @param fundCode        产品代码
     * @param busiCode        业务码  020-认购；022-申购；024-赎回；036-基金转换；039-定时定额投资；029-修改分红方式
     * @param businessType    业务类型 :1-确认日期 2-预计到账日期 3-预计打款日期 4-赎回到储蓄罐份额可用日期 其余-查全部
     * @param redeemDirection 赎回去向 0-银行卡 1-储蓄罐 默认银行卡
     * @return
     * <AUTHOR>
     * @date 2018年6月4日 下午1:25:58
     */
    public HighConfirmAndPaymentReceiptDtBean getHighConfirmAndPaymentReceiptDt(String submitTaDt, String fundCode, String busiCode, String businessType, String redeemDirection) {

        List<HighConfirmAndPaymentReceiptParamContext> paramContextList = new ArrayList<HighConfirmAndPaymentReceiptParamContext>();
        HighConfirmAndPaymentReceiptParamContext paramContext = new HighConfirmAndPaymentReceiptParamContext();
        paramContext.setFundCode(fundCode);
        paramContext.setBusiCode(busiCode);
        paramContext.setBusinessType(businessType);
        paramContext.setTaTradeDt(submitTaDt);
        paramContextList.add(paramContext);

        List<HighConfirmAndPaymentReceiptDtBean> qryRstList = queryHighProductOuterService.getHighConfirmAndPaymentReceiptDt(paramContextList);

        if (org.springframework.util.CollectionUtils.isEmpty(qryRstList)) {
            return null;
        }
        return qryRstList.get(0);

    }

    /**
     * 判断是否需要校验
     *
     * @return
     */
    public boolean isValidatorRisk(String fundCode, String txAcctNo, String disCode, String peDivideCallFlag) {
        boolean flag = true;
        // 分次call产品
        if (FractionateCallFlagEnum.YES.getCode().equals(peDivideCallFlag)) {

            List<CustBooksPo> balanceList = custBooksRepository.selectBalanceList(txAcctNo, getDisCodeList(disCode), "4", fundCode);
            // 有持仓
            if (CollectionUtils.isNotEmpty(balanceList)) {
                flag = false;
            }
        }
        return flag;
    }

    private List<String> getDisCodeList(String disCode) {
        List<String> disCodeList = new ArrayList<>();
        disCodeList.add(disCode);
        return disCodeList;
    }
}
