package com.howbuy.tms.high.orders.service.business.relatedaccount;

import com.alibaba.fastjson.JSON;
import com.howbuy.tms.common.enums.busi.DisCodeEnum;
import com.howbuy.tms.common.enums.busi.YesOrNoEnum;
import com.howbuy.tms.common.outerservice.acccenter.querybatchcustdisandtxacctinfo.QueryBatchCustDisAndTxAcctInfoOuterService;
import com.howbuy.tms.common.outerservice.acccenter.querybatchcustdisandtxacctinfo.bean.CustDisAndTxAcctBean;
import com.howbuy.tms.common.outerservice.acccenter.querybatchcustdisandtxacctinfo.request.QueryBatchCustDisAndTxAcctInfoRequestDTO;
import com.howbuy.tms.common.outerservice.acccenter.querybatchcustdisandtxacctinfo.response.QueryBatchCustDisAndTxAcctInfoResponseDTO;
import com.howbuy.tms.high.orders.service.business.relatedaccount.dto.AccountStatusInfoDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @Description:账户中心服务
 * @Author: yun.lu
 * Date: 2023/6/29 17:02
 */
@Service
@Slf4j
public class AccCenterAccountService {

    @Autowired
    private QueryBatchCustDisAndTxAcctInfoOuterService queryBatchCustDisAndTxAcctInfo;


    /**
     * 查询账户开户信息
     *
     * @param hbOneNo 一账通
     */
    public AccountStatusInfoDTO getAccountStatusInfo(String hbOneNo) {
        log.info("getAccountStatusInfo-查询账户开户信息,hbOneNo={}", hbOneNo);
        AccountStatusInfoDTO accountStatusInfoDTO = new AccountStatusInfoDTO();
        // 1.查询好臻,好买开户状态
        List<String> disCodeList = new ArrayList<>();
        disCodeList.add(DisCodeEnum.HM.getCode());
        disCodeList.add(DisCodeEnum.HZ.getCode());
        QueryBatchCustDisAndTxAcctInfoRequestDTO requestDTO = new QueryBatchCustDisAndTxAcctInfoRequestDTO();
        requestDTO.setDisCodeList(disCodeList);
        requestDTO.setHboneNo(hbOneNo);
        QueryBatchCustDisAndTxAcctInfoResponseDTO acctInfoResponseDTO = queryBatchCustDisAndTxAcctInfo.queryBatchCustDisAndTxAcctInfo(requestDTO);
        Map<String, CustDisAndTxAcctBean> customerDisAndTxAcctModelMap = acctInfoResponseDTO.getCustDisAndTxAcctMap();
        // 2.好臻状态信息检查
        CustDisAndTxAcctBean custDisAndTxAcctBean = customerDisAndTxAcctModelMap.get(DisCodeEnum.HZ.getCode());
        if (custDisAndTxAcctBean != null && custDisAndTxAcctBean.getDisCustBean() != null) {
            // 2.1.好臻账户是否激活
            if (YesOrNoEnum.YES.getCode().equals(custDisAndTxAcctBean.getDisCustBean().getActiveStat())) {
                accountStatusInfoDTO.setHasHzAccount(YesOrNoEnum.YES.getCode());
                accountStatusInfoDTO.setHasHzAccountActive(YesOrNoEnum.YES.getCode());
            } else {
                accountStatusInfoDTO.setHasHzAccount(YesOrNoEnum.YES.getCode());
                accountStatusInfoDTO.setHasHzAccountActive(YesOrNoEnum.NO.getCode());
            }
        } else {
            log.info("getAccountStatusInfo-没有好臻账户信息,hbOneNo={},customerDisAndTxAcctModelMap={}", hbOneNo, JSON.toJSONString(customerDisAndTxAcctModelMap));
            accountStatusInfoDTO.setHasHzAccount(YesOrNoEnum.NO.getCode());
            accountStatusInfoDTO.setHasHzAccountActive(YesOrNoEnum.NO.getCode());
        }
        // 3.好买账户状态信息检查
        CustDisAndTxAcctBean hmCustDisAndTxAcctBean = customerDisAndTxAcctModelMap.get(DisCodeEnum.HM.getCode());
        if (hmCustDisAndTxAcctBean == null || hmCustDisAndTxAcctBean.getDisTxAcctBean() == null) {
            log.info("getAccountStatusInfo-没有好买账户信息,hbOneNo={},customerDisAndTxAcctModelMap={}", hbOneNo, JSON.toJSONString(customerDisAndTxAcctModelMap));
            accountStatusInfoDTO.setHasHmAccount(YesOrNoEnum.NO.getCode());
            accountStatusInfoDTO.setHasHmAccountActive(YesOrNoEnum.NO.getCode());
        } else {
            // 3.1.好买账户是否激活
            if (YesOrNoEnum.YES.getCode().equals(hmCustDisAndTxAcctBean.getDisCustBean().getActiveStat())) {
                accountStatusInfoDTO.setHasHmAccount(YesOrNoEnum.YES.getCode());
                accountStatusInfoDTO.setHasHmAccountActive(YesOrNoEnum.YES.getCode());
            } else {
                accountStatusInfoDTO.setHasHmAccount(YesOrNoEnum.YES.getCode());
                accountStatusInfoDTO.setHasHmAccountActive(YesOrNoEnum.NO.getCode());
            }
        }
        log.info("getAccountStatusInfo-查询账户开户信息,hbOneNo={},accountCheckInfo={}", hbOneNo, JSON.toJSONString(accountStatusInfoDTO));
        return accountStatusInfoDTO;
    }
}
