package com.howbuy.tms.high.orders.service.facade.search.queryacctbalancewithouthk.bean;

import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductNavBean;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductNavDivBean;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductValueDateBean;
import com.howbuy.tms.high.orders.dao.vo.AckDealOrderInfo;
import com.howbuy.tms.high.orders.facade.search.queryasset.HighFundAssetIncomeDomain;
import com.howbuy.tms.high.orders.service.facade.search.queryacctbalance.bean.OwnershipOrderDto;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 资产市值业务数据
 */
@Getter
@Setter
public class AssetMarketNavContext {
    private final Map<String, HighProductNavBean> navBeanMap;
    private final Map<String, HighFundAssetIncomeDomain> currentAssetMap;
    private final Map<String, HighProductNavDivBean> fundNavDivMap;
    private final Map<String, BigDecimal> paidInAmtMap;
    private final Map<String, List<AckDealOrderInfo>> ackDealMap;
    private final Map<String, List<HighProductValueDateBean>> valueDateMap;
    private final Map<String, OwnershipOrderDto> ownershipDtoMap;
    private final Map<String, BigDecimal> netBuyAmtQxMap;

    public AssetMarketNavContext(Map<String, HighProductNavBean> navBeanMap,
                                 Map<String, HighFundAssetIncomeDomain> currentAssetMap,
                                 Map<String, HighProductNavDivBean> fundNavDivMap,
                                 Map<String, BigDecimal> paidInAmtMap,
                                 Map<String, List<AckDealOrderInfo>> ackDealMap,
                                 Map<String, List<HighProductValueDateBean>> valueDateMap,
                                 Map<String, OwnershipOrderDto> ownershipDtoMap,
                                 Map<String, BigDecimal> netBuyAmtQxMap) {
        this.navBeanMap = navBeanMap;
        this.currentAssetMap = currentAssetMap;
        this.fundNavDivMap = fundNavDivMap;
        this.paidInAmtMap = paidInAmtMap;
        this.ackDealMap = ackDealMap;
        this.valueDateMap = valueDateMap;
        this.ownershipDtoMap = ownershipDtoMap;
        this.netBuyAmtQxMap = netBuyAmtQxMap;
    }

}
