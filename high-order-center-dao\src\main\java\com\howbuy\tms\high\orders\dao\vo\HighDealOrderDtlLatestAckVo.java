/**
 *Copyright (c) 2016, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
*/

package com.howbuy.tms.high.orders.dao.vo;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @description:公募明细订单
 * @reason:公募明细订单
 * <AUTHOR>
 * @date 2016-10-12 下午8:51:20
 * @since JDK 1.6
 */
public class HighDealOrderDtlLatestAckVo implements Serializable {

    private static final long serialVersionUID = -5803779695752216917L;
    private String dealNo;
    private String dealDtlNo;
    private String fundCode;
    private BigDecimal fee;
    private BigDecimal ackAmt;
    private BigDecimal ackVol;
    private String ackDt;
    private String txAcctNo;
    private String submitTaDt;

    public String getDealNo() {
        return dealNo;
    }

    public void setDealNo(String dealNo) {
        this.dealNo = dealNo;
    }

    public String getDealDtlNo() {
        return dealDtlNo;
    }
    public void setDealDtlNo(String dealDtlNo) {
        this.dealDtlNo = dealDtlNo;
    }
    public String getFundCode() {
        return fundCode;
    }
    public void setFundCode(String fundCode) {
        this.fundCode = fundCode;
    }
    public BigDecimal getFee() {
        return fee;
    }
    public void setFee(BigDecimal fee) {
        this.fee = fee;
    }
    public BigDecimal getAckAmt() {
        return ackAmt;
    }
    public void setAckAmt(BigDecimal ackAmt) {
        this.ackAmt = ackAmt;
    }
    public BigDecimal getAckVol() {
        return ackVol;
    }
    public void setAckVol(BigDecimal ackVol) {
        this.ackVol = ackVol;
    }
    public String getAckDt() {
        return ackDt;
    }
    public void setAckDt(String ackDt) {
        this.ackDt = ackDt;
    }
    public String getTxAcctNo() {
        return txAcctNo;
    }
    public void setTxAcctNo(String txAcctNo) {
        this.txAcctNo = txAcctNo;
    }
    public String getSubmitTaDt() {
        return submitTaDt;
    }
    public void setSubmitTaDt(String submitTaDt) {
        this.submitTaDt = submitTaDt;
    }

}