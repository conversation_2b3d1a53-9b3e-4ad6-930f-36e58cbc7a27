/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.orders.service.facade.search.queryfundpositiondate;

import com.howbuy.tms.high.orders.facade.search.queryfundpositiondate.QueryFundPositionDateByFundCodeFacade;
import com.howbuy.tms.high.orders.facade.search.queryfundpositiondate.QueryFundPositionDateByFundCodeRequest;
import com.howbuy.tms.high.orders.facade.search.queryfundpositiondate.QueryFundPositionDateByFundCodeResponse;
import com.howbuy.tms.high.orders.service.service.queryfundpositiondate.QueryFundPositionDateService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @description: 根据基金编码查询基金持仓/清仓日期服务实现
 * <AUTHOR>
 * @date 2025/9/11 10:35
 * @since JDK 1.8
 */
@DubboService
@Component
@Slf4j
public class QueryFundPositionDateByFundCodeFacadeService implements QueryFundPositionDateByFundCodeFacade {


    @Resource
    private QueryFundPositionDateService queryFundPositionDateService;
    @Override
    public QueryFundPositionDateByFundCodeResponse execute(QueryFundPositionDateByFundCodeRequest request) {

        return queryFundPositionDateService.queryFundPositionDateByFundCode(request);
    }
} 