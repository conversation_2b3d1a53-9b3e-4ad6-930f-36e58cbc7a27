# AI生成接口详细设计文档提示词（基于产品需求文档）
你是一名资深后端架构师，请根据我提供的**产品需求文档**（PRD）内容，生成一份对应的**接口详细设计文档**，输出要求如下：
## 📌 输出要求：
- **输出格式**：Markdown（`.md`）
- **文件名**：根据接口功能命名，例如 `用户注册接口.md`
- **保存路径**：项目根目录下的 `.知识库/设计文档/模块名/功能名/` 目录中
- **图示工具**：所有流程图与时序图必须使用 **PlantUML** 语法编写，**不要使用 Mermaid**
- **不包含具体代码**，仅通过**自然语言**清晰描述接口的业务逻辑、流程和分支判断
## 📘 接口详细设计文档结构：
请完整输出以下内容：
### 1. 接口名称
接口的功能名称（中文）
### 2. 接口说明
接口的作用、业务背景、使用场景说明
### 3. 接口类型
例如 HTTP / Dubbo / WebSocket 等
### 4. 接口地址或方法签名
- **HTTP 接口**：请求方式（GET/POST 等） + URL 地址  
- **Dubbo 接口**：接口类全名 + 方法签名
### 5. 请求参数表（表格展示）
| 中文名 | 英文名 | 类型 | 是否必填 | 示例值 | 字段说明 |
|--------|--------|------|----------|--------|----------|
### 6. 响应参数表（表格展示）
| 中文名 | 英文名 | 类型 | 示例值 | 字段说明 |
|--------|--------|------|--------|----------|
### 7. 返回码说明
| 返回码 | 说明 | 备注 |
|--------|------|------|
### 8. 关键业务逻辑说明
根据产品需求推导的核心处理逻辑，详述判断分支、边界逻辑、特殊场景处理，**不要包含任何形式的代码**，示例：
	- 接收到请求后，首先校验用户输入参数是否合法（如手机号格式、验证码是否为空等）
	- 如果为新用户，则引导注册流程；否则校验账户状态是否正常
	- 若用户已被封禁，则中断流程，返回对应错误码
	- 成功后记录操作日志，并异步通知风控系统
### 9. 流程图（使用 PlantUML）
```plantuml
@startuml
...
@enduml
10. 时序图（使用 PlantUML）
@startuml
...
@enduml
11. 异常处理机制
常见错误场景、输入不合法时的处理方式、兜底策略等
12. 调用的公共模块或外部依赖
模块名称 | 功能简述
✏️ 注意事项：
	- 图示必须使用 PlantUML 标准语法，不可使用 Mermaid
	- Markdown 文档结构清晰，适合归档和评审使用
	- 所有术语、字段、流程需尽可能对齐产品需求中的描述，不要遗漏
	- 输出的接口设计应具备“可以指导开发”的完整性和可操作性
	- 文档中不要包含任何代码，包括类名、方法名、注解、代码片段等

