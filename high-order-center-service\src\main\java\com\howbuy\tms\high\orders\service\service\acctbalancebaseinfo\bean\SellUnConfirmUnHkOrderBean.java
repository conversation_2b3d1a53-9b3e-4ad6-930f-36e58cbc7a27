package com.howbuy.tms.high.orders.service.service.acctbalancebaseinfo.bean;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * @Description: 卖出非香港待确认订单交易记录Bean
 * @Author: yun.lu
 * @Date: 2023/11/15 14:06:24
 */
@Getter
@Setter
public class SellUnConfirmUnHkOrderBean {

    /**
     * 订单号
     */
    private String dealNo;

    /**
     * 产品编码
     */
    private String fundCode;

    /**
     * 申请份额
     */
    private BigDecimal appVol;

    /**
     * 中台业务类型
     */
    private String mBusiCode;
    
    /**
     * 上报状态
     * 0-无需上报，1-未上报，2-上报完成，3-需重新上报
     */
    private String notifySubmitFlag;

    /**
     * 上报日,yyyyMMdd
     */
    private String submitTaDt;

    /**
     * 销售类型: 1-直销;2-代销
     */
    private String scaleType;
}