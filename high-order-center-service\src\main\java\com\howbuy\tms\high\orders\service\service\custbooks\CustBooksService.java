package com.howbuy.tms.high.orders.service.service.custbooks;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.howbuy.tms.common.enums.busi.CurrencyEnum;
import com.howbuy.tms.common.enums.busi.DisCodeEnum;
import com.howbuy.tms.common.enums.busi.YesOrNoEnum;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.QueryHighProductOuterService;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductDBInfoBean;
import com.howbuy.tms.common.utils.MoneyUtil;
import com.howbuy.tms.high.orders.dao.po.CmCusttradeDirectPo;
import com.howbuy.tms.high.orders.dao.po.CustBooksPo;
import com.howbuy.tms.high.orders.facade.search.queryacctbalance.UnconfirmeProduct;
import com.howbuy.tms.high.orders.service.cacheservice.querydirectbalance.QueryDirectBalanceCacheService;
import com.howbuy.tms.high.orders.service.repository.CmCusttradeDirectRepository;
import com.howbuy.tms.high.orders.service.repository.CustBooksRepository;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description: 客户账本
 * @reason: 客户账本
 * @date 2016-9-7 上午10:55:26
 * @since JDK 1.7
 */
@Service("custBooksService")
public class CustBooksService {
    private static final Logger logger = LogManager.getLogger(CustBooksService.class);

    @Autowired
    private CustBooksRepository custBooksRepository;
    @Autowired
    private CmCusttradeDirectRepository cmCusttradeDirectRepository;
    @Autowired
    private QueryHighProductOuterService queryHighProductOuterService;
    @Autowired
    private QueryDirectBalanceCacheService queryDirectBalanceCacheService;


    /**
     * queryIntransit:查询是否存在在途交易
     *
     * @param txAcctNo
     * @param disCode
     * @param protocolNo
     * @return
     * <AUTHOR>
     * @date 2016-10-21 下午7:09:28
     */
    public int queryIntransit(String txAcctNo, String disCode, String protocolNo) {
        CustBooksPo custBooksPo = new CustBooksPo();
        custBooksPo.setTxAcctNo(txAcctNo);
        custBooksPo.setDisCode(disCode);
        custBooksPo.setProtocolNo(protocolNo);
        return custBooksRepository.selectInTransit(custBooksPo);
    }


    /**
     * getConsignmentOnWayAmt:(查询代销在途金额)
     *
     * @param disCodeList
     * @param txAcctNo
     * @return
     * <AUTHOR> @date
     */
    private List<CustBooksPo> getConsignmentOnWayAmt(List<String> disCodeList, String txAcctNo) {
        return custBooksRepository.selectConsignmentOnWayAmt(disCodeList, txAcctNo);
    }


    /**
     * 获取在途资产
     *
     * @param hkSaleFlag
     * @param productSubType
     * @param disCodeList
     * @param txAcctNo
     * @return
     */
    public List<UnconfirmeProduct> getTotalUnconfirmedAmtByQueryAcctBalance(String hkSaleFlag, String productSubType, List<String> disCodeList, String txAcctNo, String notFilterHkFund, String notFilterHzFund) {
        logger.info("CustBooksService|getTotalUnconfirmedAmt|hkSaleFlag:{},productSubType:{},txAcctNo:{},disCodeList:{}",
                hkSaleFlag, productSubType, txAcctNo, disCodeList);
        List<UnconfirmeProduct> unconfirmeFunds = new ArrayList<>();
        // 查询代销在途金额列表
        List<CustBooksPo> consignmentOnWayAmtList = getConsignmentOnWayAmt(disCodeList, txAcctNo);
        consignmentOnWayAmtList = consignmentOnWayAmtList == null ? new ArrayList<>() : consignmentOnWayAmtList;
        // 查询直销在途金额列表
        List<CmCusttradeDirectPo> directOnWayAmtList = cmCusttradeDirectRepository.selectDirectOnWayAmt(disCodeList, txAcctNo);
        directOnWayAmtList = directOnWayAmtList == null ? new ArrayList<>() : directOnWayAmtList;

        // 产品去重
        Set<String> productCodeSet = new HashSet<String>();
        productCodeSet.addAll(consignmentOnWayAmtList.stream().map(CustBooksPo::getProductCode).collect(Collectors.toSet()));
        productCodeSet.addAll(directOnWayAmtList.stream().map(CmCusttradeDirectPo::getFundcode).collect(Collectors.toSet()));
        // 批量查询产品基本信息
        Map<String, HighProductDBInfoBean> highProductDbInfoBeanMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(productCodeSet)) {
            highProductDbInfoBeanMap = queryHighProductOuterService.getHighProductDBInfoMap(new ArrayList<>(productCodeSet));
        }

        // 过滤产品
        filterFund(hkSaleFlag, productSubType, highProductDbInfoBeanMap, notFilterHkFund);
        // 累加代销在途资金
        sumOnwayAmt(txAcctNo, unconfirmeFunds, consignmentOnWayAmtList, directOnWayAmtList, highProductDbInfoBeanMap, notFilterHzFund);
        return unconfirmeFunds;
    }

    private void sumOnwayAmt(String txAcctNo, List<UnconfirmeProduct> unconfirmeFunds, List<CustBooksPo> consignmentOnWayAmtList,
                             List<CmCusttradeDirectPo> directOnWayAmtList, Map<String, HighProductDBInfoBean> highProductDbInfoBeanMap, String notFilterHzFund) {
        UnconfirmeProduct unconfirmeFund = null;
        for (CustBooksPo po : consignmentOnWayAmtList) {
            // 如果是非授权,需要过滤好臻产品
            if (YesOrNoEnum.NO.getCode().equals(notFilterHzFund) && DisCodeEnum.HZ.getCode().equals(po.getDisCode())) {
                continue;
            }
            HighProductDBInfoBean bean = highProductDbInfoBeanMap.get(po.getProductCode());
            if (null != bean) {
                // 查询未确认订单数量
                logger.info("查询未确认订单数量{}", JSON.toJSONString(po));
                int num = custBooksRepository.getUnAckNum(txAcctNo, po.getProductCode());
                logger.info("查询未确认订单数量 num{}", num);
                if (num > 0) {
                    unconfirmeFund = new UnconfirmeProduct();
                    unconfirmeFund.setFundCode(po.getProductCode());
                    unconfirmeFund.setProductSubType(bean.getFundSubType());
                    unconfirmeFund.setHkSaleFlag(bean.getHkSaleFlag());
                    unconfirmeFund.setProductType(bean.getFundType());
                    unconfirmeFund.setUnconfirmedAmt(po.getUnconfirmedAmt());
                    unconfirmeFund.setDisCode(po.getDisCode());
                    unconfirmeFunds.add(unconfirmeFund);
                }
            }
        }
        Map<String, BigDecimal> rmbZjjMap = new HashMap<>();
        // 累加直销在途资金
        for (CmCusttradeDirectPo po : directOnWayAmtList) {
            // 如果是非授权,需要过滤好臻产品
            if (YesOrNoEnum.NO.getCode().equals(notFilterHzFund) && DisCodeEnum.HZ.getCode().equals(po.getDiscode())) {
                continue;
            }
            HighProductDBInfoBean bean = highProductDbInfoBeanMap.get(po.getFundcode());
            if (null != bean) {
                unconfirmeFund = new UnconfirmeProduct();
                unconfirmeFund.setFundCode(po.getFundcode());
                unconfirmeFund.setProductSubType(bean.getFundSubType());
                unconfirmeFund.setHkSaleFlag(bean.getHkSaleFlag());
                unconfirmeFund.setProductType(bean.getFundType());
                unconfirmeFund.setDisCode(po.getDiscode());
                BigDecimal directUnConfirmedAmt = getDirectUnConfirmedAmt(po, rmbZjjMap);
                unconfirmeFund.setUnconfirmedAmt(directUnConfirmedAmt);
                unconfirmeFunds.add(unconfirmeFund);
            }
        }
    }

    /**
     * 获取直销在途金额
     */
    private BigDecimal getDirectUnConfirmedAmt(CmCusttradeDirectPo cmCusttradeDirectPo, Map<String, BigDecimal> rmbZjjMap) {
        BigDecimal amt;
        // 香港产品,在途金额取值为申请金额减去费用
        if (StringUtils.isNotBlank(cmCusttradeDirectPo.getIsHkProduct()) && YesOrNoEnum.YES.getCode().equals(cmCusttradeDirectPo.getIsHkProduct()) && cmCusttradeDirectPo.getAppamt() != null) {
            if (cmCusttradeDirectPo.getFee() != null) {
                amt = cmCusttradeDirectPo.getAppamt().subtract(cmCusttradeDirectPo.getFee());
            } else {
                amt = cmCusttradeDirectPo.getAppamt();
            }
        } else {
            amt = cmCusttradeDirectPo.getRealpayamt();
        }
        // 如果是人民币,就需要将外币转换为人民币
        if (CurrencyEnum.RMB.getCode().equals(cmCusttradeDirectPo.getCurrency())) {
            return amt;
        } else {
            if (null == rmbZjjMap.get(cmCusttradeDirectPo.getCurrency())) {
                BigDecimal rmbZJJ = queryDirectBalanceCacheService.getRMBZJJ(cmCusttradeDirectPo.getCurrency());
                rmbZjjMap.put(cmCusttradeDirectPo.getCurrency(), rmbZJJ);
            }
            return MoneyUtil.formatMoney(amt.multiply(rmbZjjMap.get(cmCusttradeDirectPo.getCurrency())), 2);
        }
    }

    private void filterFund(String hkSaleFlag, String productSubType, Map<String, HighProductDBInfoBean> highProductDbInfoBeanMap, String notFilterHkFund) {
        Iterator<String> ite = highProductDbInfoBeanMap.keySet().iterator();
        while (ite.hasNext()) {
            HighProductDBInfoBean highProductDbInfoBean = highProductDbInfoBeanMap.get(ite.next());
            // 好臻：取值逻辑：【是否香港-hkSaleFlag】=0-否，【产品分类-ProductSubtype】=5-股权
            if (StringUtils.isNotEmpty(hkSaleFlag) && StringUtils.isNotEmpty(productSubType)
                    && "0".equals(hkSaleFlag) && "5".equals(productSubType)) {
                if (!(hkSaleFlag.equals(highProductDbInfoBean.getHkSaleFlag())
                        && productSubType.equals(highProductDbInfoBean.getFundSubType()))) {
                    ite.remove();
                    continue;
                }
            }
            // 香港：取值逻辑：【是否香港-hkSaleFlag】=1-是；
            if (StringUtils.isNotEmpty(hkSaleFlag) && "1".equals(hkSaleFlag)) {
                if (!hkSaleFlag.equals(highProductDbInfoBean.getHkSaleFlag())) {
                    ite.remove();
                    continue;
                }
            }
            // 如果是非授权,需要过滤香港产品
            if (YesOrNoEnum.NO.getCode().equals(notFilterHkFund) && highProductDbInfoBean != null) {
                if (YesOrNoEnum.YES.getCode().equals(highProductDbInfoBean.getHkSaleFlag())) {
                    ite.remove();
                }
            }
        }
    }

    /**
     * 获取在途资产
     *
     * @param hkSaleFlag
     * @param productSubType
     * @param disCodeList
     * @param txAcctNo
     * @return
     */
    public BigDecimal getTotalUnconfirmedAmt(String hkSaleFlag, String productSubType, List<String> disCodeList, String txAcctNo) {
        logger.info("CustBooksService|getTotalUnconfirmedAmt|hkSaleFlag:{},productSubType:{},txAcctNo:{},disCodeList:{}",
                hkSaleFlag, productSubType, txAcctNo, disCodeList);
        // 在途总金额
        BigDecimal totalUnconfirmedAmt = BigDecimal.ZERO;
        // 查询代销在途金额列表
        List<CustBooksPo> consignmentOnWayAmtList = getConsignmentOnWayAmt(disCodeList, txAcctNo);
        consignmentOnWayAmtList = consignmentOnWayAmtList == null ? new ArrayList<>() : consignmentOnWayAmtList;
        // 查询直销在途金额列表
        List<CmCusttradeDirectPo> directOnWayAmtList = cmCusttradeDirectRepository.selectDirectOnWayAmt(disCodeList, txAcctNo);
        directOnWayAmtList = directOnWayAmtList == null ? new ArrayList<>() : directOnWayAmtList;

        // 产品去重
        Set<String> productCodeSet = new HashSet<>();
        productCodeSet.addAll(consignmentOnWayAmtList.stream().map(CustBooksPo::getProductCode).collect(Collectors.toSet()));
        productCodeSet.addAll(directOnWayAmtList.stream().map(CmCusttradeDirectPo::getFundcode).collect(Collectors.toSet()));
        // 批量查询产品基本信息
        Map<String, HighProductDBInfoBean> highProductDbInfoBeanMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(productCodeSet)) {
            highProductDbInfoBeanMap = queryHighProductOuterService.getHighProductDBInfoMap(new ArrayList<>(productCodeSet));
        }

        // 过滤产品
        filterProduct(hkSaleFlag, productSubType, highProductDbInfoBeanMap);
        // 累加代销在途资金
        for (CustBooksPo po : consignmentOnWayAmtList) {
            if (null != highProductDbInfoBeanMap.get(po.getProductCode())) {
                // 查询未确认订单数量
                logger.info("查询未确认订单数量{}", JSON.toJSONString(po));
                int num = custBooksRepository.getUnAckNum(txAcctNo, po.getProductCode());
                logger.info("查询未确认订单数量 num{}", num);
                if (num > 0) {
                    totalUnconfirmedAmt = totalUnconfirmedAmt.add(po.getUnconfirmedAmt());
                }
            }
        }
        totalUnconfirmedAmt = getTotalUnConfirmedAmt(totalUnconfirmedAmt, directOnWayAmtList, highProductDbInfoBeanMap);
        logger.info("CustBooksService|getTotalUnconfirmedAmt|hkSaleFlag:{},productSubType:{},txAcctNo:{},disCodeList:{},totalUnconfirmedAmt:{}",
                hkSaleFlag, productSubType, txAcctNo, disCodeList, totalUnconfirmedAmt);
        return totalUnconfirmedAmt;
    }

    private BigDecimal getTotalUnConfirmedAmt(BigDecimal totalUnconfirmedAmt, List<CmCusttradeDirectPo> directOnWayAmtList, Map<String, HighProductDBInfoBean> highProductDbInfoBeanMap) {
        Map<String, BigDecimal> rmbZjjMap = new HashMap<>();
        // 累加直销在途资金
        for (CmCusttradeDirectPo po : directOnWayAmtList) {
            if (null != highProductDbInfoBeanMap.get(po.getFundcode())) {
                // 若为外币，需按实时汇率接口转化为人民币
                if (CurrencyEnum.RMB.getCode().equals(po.getCurrency())) {
                    totalUnconfirmedAmt = totalUnconfirmedAmt.add(po.getRealpayamt());
                } else {
                    if (null == rmbZjjMap.get(po.getCurrency())) {
                        rmbZjjMap.put(po.getCurrency(), queryDirectBalanceCacheService.getRMBZJJ(po.getCurrency()));
                    }
                    totalUnconfirmedAmt = totalUnconfirmedAmt.add(MoneyUtil.formatMoney(
                            po.getRealpayamt().multiply(rmbZjjMap.get(po.getCurrency())), 2));
                }
            }
        }
        // 在途资产累加后，需取绝对值（原逻辑）
        totalUnconfirmedAmt = totalUnconfirmedAmt.abs();
        return totalUnconfirmedAmt;
    }

    private void filterProduct(String hkSaleFlag, String productSubType, Map<String, HighProductDBInfoBean> highProductDbInfoBeanMap) {
        HighProductDBInfoBean highProductDbInfoBean = null;
        Iterator<String> ite = highProductDbInfoBeanMap.keySet().iterator();
        while (ite.hasNext()) {
            highProductDbInfoBean = highProductDbInfoBeanMap.get(ite.next());
            // 好臻：取值逻辑：【是否香港-hkSaleFlag】=0-否，【产品分类-ProductSubtype】=5-股权
            if (StringUtils.isNotEmpty(hkSaleFlag) && StringUtils.isNotEmpty(productSubType)
                    && "0".equals(hkSaleFlag) && "5".equals(productSubType)) {
                if (!(hkSaleFlag.equals(highProductDbInfoBean.getHkSaleFlag())
                        && productSubType.equals(highProductDbInfoBean.getFundSubType()))) {
                    ite.remove();
                    continue;
                }
            }
            // 香港：取值逻辑：【是否香港-hkSaleFlag】=1-是；
            if (StringUtils.isNotEmpty(hkSaleFlag) && "1".equals(hkSaleFlag)) {
                if (!hkSaleFlag.equals(highProductDbInfoBean.getHkSaleFlag())) {
                    ite.remove();
                }
            }
        }
    }

    public List<CustBooksPo> queryByFundCode(List<String> fundCodeList) {
        return custBooksRepository.queryByFundCode(fundCodeList);
    }
}