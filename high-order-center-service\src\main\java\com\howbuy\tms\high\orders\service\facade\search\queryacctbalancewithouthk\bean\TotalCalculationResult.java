package com.howbuy.tms.high.orders.service.facade.search.queryacctbalancewithouthk.bean;

import com.howbuy.tms.common.enums.busi.IncomeCalStatEnum;
import com.howbuy.tms.high.orders.facade.search.queryacctbalance.UnconfirmeProduct;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 汇总计算结果
 */
@Getter
@Setter
public class TotalCalculationResult {
    /**
     * 总收益
     */
    private BigDecimal totalCurrentAsset = BigDecimal.ZERO;
    /**
     * 总市值
     */
    private BigDecimal totalMarketValue = BigDecimal.ZERO;
    /**
     * 总回款
     */
    private BigDecimal totalCashCollection = BigDecimal.ZERO;
    /**
     * 总收益计算状态
     */
    private String totalIncomCalStat = IncomeCalStatEnum.FINISHED.getCode();
    /**
     * 总累计收益
     */
    private BigDecimal totalAccumIncome = BigDecimal.ZERO;
    /**
     * 总累计收益(当前币种)
     */
    private BigDecimal totalCurrencyAccumIncome = BigDecimal.ZERO;
    /**
     * 总未确认金额
     */
    private BigDecimal totalUnConfirmAmt = BigDecimal.ZERO;

    /**
     * 总未确认份额
     */
    private BigDecimal totalUnConfirmVol = BigDecimal.ZERO;
    /**
     * 在途赎回数量
     */
    private int redeemUnconfirmedNum;
    /**
     * 在途订单数量
     */
    private int totalUnconfirmedNum;

    /**
     * 买入到确认交易
     */
    private List<UnconfirmeProduct> unconfirmeProductList = new ArrayList<>();

    public void addBuyUnConfirmList(UnconfirmeProduct unconfirmeProduct) {
        if (unconfirmeProduct!=null) {
            this.unconfirmeProductList.add(unconfirmeProduct);
        }
    }

    public void addTotalUnConfirmVol(BigDecimal amount) {
        if (amount != null) {
            this.totalUnConfirmVol = this.totalUnConfirmVol.add(amount);
        }
    }

    public void addTotalUnConfirmAmt(BigDecimal amount) {
        if (amount != null) {
            this.totalUnConfirmAmt = this.totalUnConfirmAmt.add(amount);
        }
    }


    public void addTotalCurrentAsset(BigDecimal amount) {
        if (amount != null) {
            this.totalCurrentAsset = this.totalCurrentAsset.add(amount);
        }
    }

    public void addTotalMarketValue(BigDecimal amount) {
        if (amount != null) {
            this.totalMarketValue = this.totalMarketValue.add(amount);
        }
    }

    public void addTotalCashCollection(BigDecimal amount) {
        if (amount != null) {
            this.totalCashCollection = this.totalCashCollection.add(amount);
        }
    }

    public void addTotalAccumIncome(BigDecimal amount) {
        if (amount != null) {
            this.totalAccumIncome = this.totalAccumIncome.add(amount);
        }
    }

    public void addTotalCurrencyAccumIncome(BigDecimal amount) {
        if (amount != null) {
            this.totalCurrencyAccumIncome = this.totalCurrencyAccumIncome.add(amount);
        }
    }
}