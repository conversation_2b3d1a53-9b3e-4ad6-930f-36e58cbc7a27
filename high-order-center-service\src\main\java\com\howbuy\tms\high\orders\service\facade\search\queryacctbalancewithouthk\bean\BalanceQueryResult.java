package com.howbuy.tms.high.orders.service.facade.search.queryacctbalancewithouthk.bean;

import com.howbuy.tms.high.orders.service.service.acctbalancebaseinfo.bean.*;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 持仓查询结果内部类
 */
@Getter
@Setter
public class BalanceQueryResult {
    private List<CustConfirmBalanceDto> confirmBalanceList;
    private List<BuyUnConfirmUnHkOrderBean> buyUnConfirmUnHkOrderBeanList;
    private List<SellUnConfirmUnHkOrderBean> sellUnConfirmUnHkOrderBeanList;
    private List<RefundDealOrderInfo> refundDealOrderInfoList;
    private List<QueryLiquidationBalanceFundInfo> queryLiquidationBalanceFundInfoList;

}