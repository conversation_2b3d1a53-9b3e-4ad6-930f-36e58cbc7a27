/**
 * Copyright (c) 2017, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.orders.service.business.ordercreater;

import com.howbuy.common.date.DateUtil;
import com.howbuy.tms.common.enums.busi.BusinessCodeEnum;
import com.howbuy.tms.common.outerservice.acccenter.queryallcustinfo.QueryAllCustInfoResult;
import com.howbuy.tms.common.outerservice.interlayer.hzFundAmtLockCfg.bean.HzFundAmtLockCfgDto;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductBaseInfoBean;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductFeeRateBean;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductInfoBean;
import com.howbuy.tms.common.outerservice.interlayer.queryproductinfo.bean.ProductAppointmentInfoBean;
import com.howbuy.tms.high.orders.dao.po.HighDealOrderDtlPo;
import com.howbuy.tms.high.orders.dao.po.SubscribeAmtDetailPo;
import com.howbuy.tms.high.orders.facade.common.OrderTradeBaseRequest;
import com.howbuy.tms.high.orders.service.facade.search.queryHzSubscribeAmtInfo.SubscribeAmtInfoDto;
import com.howbuy.tms.high.orders.service.service.acctbalancebaseinfo.bean.AcctBalanceBaseInfo;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Description:交易业务上下文
 *
 * <AUTHOR>
 * @reason:
 * @date 2017年3月16日 上午10:23:53
 * @since JDK 1.7
 */
public class OrderCreateContext {
    /**
     * 下单系统时间
     */
    private Date now = new Date();
    /**
     * 申请TA工作日
     */
    private String taTradeDt;
    /**
     * 上报TA日期
     */
    private String submitTaDt;
    /**
     * 中台业务码
     */
    private String zBusiCode;
    /**
     * 协议号
     */
    private String protocolNo;
    /**
     * 协议类型
     */
    private String protocolType;
    /**
     * 基金分红方式
     */
    private String fundDivMode;
    /**
     * 业务码
     */
    private BusinessCodeEnum businessCode;

    /**
     * 费率业务码
     */
    private BusinessCodeEnum feeRateBusinessCode;
    /**
     * 客户信息和资金账户信息
     */
    private QueryAllCustInfoResult custInfo;
    /**
     * 客户信息和资金账户信息
     */
    private QueryAllCustInfoResult targetCustInfo;
    /**
     * 高端产品详细信息
     */
    private HighProductInfoBean highProductInfoBean;

    /**
     * 交易基本请求参数
     */
    private OrderTradeBaseRequest orderTradeBaseRequest;
    /**
     * 申购参数
     */
    private BuyBean buyBean;
    /**
     * 申购参数列表
     */
    private List<BuyBean> buyList;
    /**
     * 赎回参数
     */
    private RedeemBean redeemBean;
    /**
     * 赎回参数列表
     */
    private List<RedeemBean> redeemList;

    /**
     * 计算费率方式,0-固定收费 1-按金额 2-按天数 3-按份额,4-认缴,5-实缴
     */
    private String getFeeRateMethod;

    /**
     * 固定费用
     */
    private BigDecimal constantFee;
    /**
     * 订单扩展信息
     */
    private OrderExtendBean orderExtendBean;
    /**
     * 费率信息
     */
    private HighProductFeeRateBean highProductFeeRateBean;
    /**
     * 份额合并/迁移转出基金信息
     */
    private Map<String, HighProductBaseInfoBean> highProductBaseInfoBeanMap;

    /**
     * 份额合并/迁移转出份额相关信息
     */
    private List<ShareMergeBean> outShareMergeBeanList;

    /**
     * 预约订单信息
     */
    private HighOrderAppointInfoBean highOrderAppointInfoBean;

    /**
     * 预约日历ID
     */
    private String appointId;

    /**
     * 非交易过户参数
     */
    private NoTradeOverAccountBean noTradeOverAccountBean;

    /**
     * 电子合同版本号
     */
    private String contractVersion;

    /**
     * 是否高端定投 0-否 1-是
     */
    private String highFundInvPlanFlag;
    /**
     * 有确认持仓
     */
    private List<AcctBalanceBaseInfo> confirmBalanceBaseInfoList;
    /**
     * 有在途持仓
     */
    private List<HighDealOrderDtlPo> onWayHighDealOrderDtlPoList;
    /**
     * 好臻金额锁定配置
     */
    private List<HzFundAmtLockCfgDto> hzFundAmtLockCfgDtoList;

    /**
     * 好臻认缴记录
     */
    private SubscribeAmtDetailPo subscribeAmtDetailPo;
    /**
     * 签章id
     */
    private String sealId;

    /**
     * 补充表单单号
     */
    private String formNo;
    /**
     * 原订单是否已经支付,1:已支付,0:未支付
     */
    private String originalOrderPayed;
    /**
     * 认缴金额信息
     */
    private SubscribeAmtInfoDto subscribeAmtInfoDto;

    /**
     * 预约日历
     */
    private ProductAppointmentInfoBean productAppointmentInfoBean;

    public SubscribeAmtInfoDto getSubscribeAmtInfoDto() {
        return subscribeAmtInfoDto;
    }

    public void setSubscribeAmtInfoDto(SubscribeAmtInfoDto subscribeAmtInfoDto) {
        this.subscribeAmtInfoDto = subscribeAmtInfoDto;
    }

    public SubscribeAmtDetailPo getSubscribeAmtDetailPo() {
        return subscribeAmtDetailPo;
    }

    public List<HzFundAmtLockCfgDto> getHzFundAmtLockCfgDtoList() {
        return hzFundAmtLockCfgDtoList;
    }

    public void setHzFundAmtLockCfgDtoList(List<HzFundAmtLockCfgDto> hzFundAmtLockCfgDtoList) {
        this.hzFundAmtLockCfgDtoList = hzFundAmtLockCfgDtoList;
    }

    public BigDecimal getConstantFee() {
        return constantFee;
    }

    public void setConstantFee(BigDecimal constantFee) {
        this.constantFee = constantFee;
    }

    public String getOriginalOrderPayed() {
        return originalOrderPayed;
    }

    public void setOriginalOrderPayed(String originalOrderPayed) {
        this.originalOrderPayed = originalOrderPayed;
    }

    public String getSealId() {
        return sealId;
    }

    public void setSealId(String sealId) {
        this.sealId = sealId;
    }

    public String getFormNo() {
        return formNo;
    }

    public HighProductFeeRateBean getHighProductFeeRateBean() {
        return highProductFeeRateBean;
    }

    public void setHighProductFeeRateBean(HighProductFeeRateBean highProductFeeRateBean) {
        this.highProductFeeRateBean = highProductFeeRateBean;
    }

    public void setFormNo(String formNo) {
        this.formNo = formNo;
    }

    public void setSubscribeAmtDetailPo(SubscribeAmtDetailPo subscribeAmtDetailPo) {
        this.subscribeAmtDetailPo = subscribeAmtDetailPo;
    }

    public String getGetFeeRateMethod() {
        return getFeeRateMethod;
    }

    public void setGetFeeRateMethod(String getFeeRateMethod) {
        this.getFeeRateMethod = getFeeRateMethod;
    }

    public ProductAppointmentInfoBean getProductAppointmentInfoBean() {
        return productAppointmentInfoBean;
    }

    public void setProductAppointmentInfoBean(ProductAppointmentInfoBean productAppointmentInfoBean) {
        this.productAppointmentInfoBean = productAppointmentInfoBean;
    }

    public BusinessCodeEnum getFeeRateBusinessCode() {
        return feeRateBusinessCode;
    }

    public void setFeeRateBusinessCode(BusinessCodeEnum feeRateBusinessCode) {
        this.feeRateBusinessCode = feeRateBusinessCode;
    }

    public List<AcctBalanceBaseInfo> getConfirmBalanceBaseInfoList() {
        return confirmBalanceBaseInfoList;
    }

    public void setConfirmBalanceBaseInfoList(List<AcctBalanceBaseInfo> confirmBalanceBaseInfoList) {
        this.confirmBalanceBaseInfoList = confirmBalanceBaseInfoList;
    }

    public List<HighDealOrderDtlPo> getOnWayHighDealOrderDtlPoList() {
        return onWayHighDealOrderDtlPoList;
    }

    public void setOnWayHighDealOrderDtlPoList(List<HighDealOrderDtlPo> onWayHighDealOrderDtlPoList) {
        this.onWayHighDealOrderDtlPoList = onWayHighDealOrderDtlPoList;
    }

    public String getzBusiCode() {
        return zBusiCode;
    }

    public void setzBusiCode(String zBusiCode) {
        this.zBusiCode = zBusiCode;
    }

    public Date getNow() {
        return now;
    }

    public void setNow(Date now) {
        this.now = now;
    }

    public BusinessCodeEnum getBusinessCode() {
        return businessCode;
    }

    public void setBusinessCode(BusinessCodeEnum businessCode) {
        this.businessCode = businessCode;
    }

    public String getTaTradeDt() {
        return taTradeDt;
    }

    public void setTaTradeDt(String taTradeDt) {
        this.taTradeDt = taTradeDt;
    }

    public String getSubmitTaDt() {
        return submitTaDt;
    }

    public void setSubmitTaDt(String submitTaDt) {
        this.submitTaDt = submitTaDt;
    }

    public String getProtocolNo() {
        return protocolNo;
    }

    public void setProtocolNo(String protocolNo) {
        this.protocolNo = protocolNo;
    }

    public String getProtocolType() {
        return protocolType;
    }

    public void setProtocolType(String protocolType) {
        this.protocolType = protocolType;
    }

    public String getFundDivMode() {
        return fundDivMode;
    }

    public void setFundDivMode(String fundDivMode) {
        this.fundDivMode = fundDivMode;
    }

    public QueryAllCustInfoResult getCustInfo() {
        return custInfo;
    }

    public void setCustInfo(QueryAllCustInfoResult custInfo) {
        this.custInfo = custInfo;
    }

    public HighProductInfoBean getHighProductInfoBean() {
        return highProductInfoBean;
    }

    public void setHighProductInfoBean(HighProductInfoBean highProductInfoBean) {
        this.highProductInfoBean = highProductInfoBean;
    }

    public OrderTradeBaseRequest getOrderTradeBaseRequest() {
        return orderTradeBaseRequest;
    }

    public void setOrderTradeBaseRequest(OrderTradeBaseRequest orderTradeBaseRequest) {
        this.orderTradeBaseRequest = orderTradeBaseRequest;
    }

    public BuyBean getBuyBean() {
        return buyBean;
    }

    public void setBuyBean(BuyBean buyBean) {
        this.buyBean = buyBean;
    }

    public List<BuyBean> getBuyList() {
        return buyList;
    }

    public void setBuyList(List<BuyBean> buyList) {
        this.buyList = buyList;
    }

    public RedeemBean getRedeemBean() {
        return redeemBean;
    }

    public void setRedeemBean(RedeemBean redeemBean) {
        this.redeemBean = redeemBean;
    }

    public List<RedeemBean> getRedeemList() {
        return redeemList;
    }

    public void setRedeemList(List<RedeemBean> redeemList) {
        this.redeemList = redeemList;
    }

    public OrderExtendBean getOrderExtendBean() {
        return orderExtendBean;
    }

    public void setOrderExtendBean(OrderExtendBean orderExtendBean) {
        this.orderExtendBean = orderExtendBean;
    }

    public HighOrderAppointInfoBean getHighOrderAppointInfoBean() {
        return highOrderAppointInfoBean;
    }

    public void setHighOrderAppointInfoBean(HighOrderAppointInfoBean highOrderAppointInfoBean) {
        this.highOrderAppointInfoBean = highOrderAppointInfoBean;
    }

    public QueryAllCustInfoResult getTargetCustInfo() {
        return targetCustInfo;
    }

    public void setTargetCustInfo(QueryAllCustInfoResult targetCustInfo) {
        this.targetCustInfo = targetCustInfo;
    }

    /**
     * getAppDtm:(获取交易申请时间)
     *
     * @return
     * <AUTHOR>
     * @date 2017年4月11日 下午2:21:39
     */
    public Date getAppDtm() {
        if (orderTradeBaseRequest == null) {
            return null;
        }

        String appDtmStr = new StringBuilder(orderTradeBaseRequest.getAppDt()).append(orderTradeBaseRequest.getAppTm()).toString();
        return DateUtil.formatToDate(appDtmStr, DateUtil.YYYYMMDDHHMMSS);
    }

    public List<ShareMergeBean> getOutShareMergeBeanList() {
        return outShareMergeBeanList;
    }

    public void setOutShareMergeBeanList(List<ShareMergeBean> outShareMergeBeanList) {
        this.outShareMergeBeanList = outShareMergeBeanList;
    }

    public Map<String, HighProductBaseInfoBean> getHighProductBaseInfoBeanMap() {
        return highProductBaseInfoBeanMap;
    }

    public void setHighProductBaseInfoBeanMap(Map<String, HighProductBaseInfoBean> highProductBaseInfoBeanMap) {
        this.highProductBaseInfoBeanMap = highProductBaseInfoBeanMap;
    }

    public String getAppointId() {
        return appointId;
    }

    public void setAppointId(String appointId) {
        this.appointId = appointId;
    }

    public NoTradeOverAccountBean getNoTradeOverAccountBean() {
        return noTradeOverAccountBean;
    }

    public void setNoTradeOverAccountBean(NoTradeOverAccountBean noTradeOverAccountBean) {
        this.noTradeOverAccountBean = noTradeOverAccountBean;
    }

    public String getContractVersion() {
        return contractVersion;
    }

    public void setContractVersion(String contractVersion) {
        this.contractVersion = contractVersion;
    }

    public String getHighFundInvPlanFlag() {
        return highFundInvPlanFlag;
    }

    public void setHighFundInvPlanFlag(String highFundInvPlanFlag) {
        this.highFundInvPlanFlag = highFundInvPlanFlag;
    }


    public static class BuyBean {
        /**
         * 资金账号
         */
        private String cpAcctNo;
        /**
         * 支付方式
         */
        private String paymentType;
        /**
         * 申请金额
         */
        private BigDecimal appAmt;
        /**
         * 交易折扣
         */
        private BigDecimal discountRate;
        /**
         * 预约折扣
         */
        private BigDecimal appointmentDiscount;
        /**
         * 风险二次确认标识
         */
        private String riskFlag;
        /**
         * 交易手续费
         */
        private BigDecimal fee;
        /**
         * 预估手续费
         */
        private BigDecimal esitmateFee;
        /**
         * 首次购买标识
         */
        private String firstBuyFlag;
        /**
         * 预约金额
         */
        private BigDecimal appointmentAmt;

        /**
         * 预约认缴金额
         */
        private BigDecimal appointSubsAmt;

        /**
         * 总预约认缴金额
         */
        private BigDecimal totalAppointSubsAmt;
        /**
         * 预约打款截止日
         */
        private String payDeadlineDtm;
        /**
         * 预约开放截止日
         */
        private String openEndTime;
        /**
         * 支行名称
         */
        private String subBankName;

        /**
         * 产品风险等级
         */
        private String fundRiskLevel;
        /**
         * 客户风险等级
         */
        private String custRiskLevel;

        /**
         * 提前下单标识
         */
        private String supportAdvanceFlag;

        /**
         * 交易递延标志
         */
        private String advanceFlag;
        /**
         * 支付对账日期
         */
        private String pmtCheckDt;
        /**
         * 成单方式
         */
        private String orderFormType;
        /**
         * 预约类型: 0:系统生成 ,1:投顾生成
         */
        private String appointmentType;
        /**
         * 双录状态: 0-不需要双录, 1-未双录, 2-已双录
         */
        private String dualentryStatus;
        /**
         * 双录干预标识: 0-不需干预, 1-未干预, 2-已干预
         */
        private String dualentryInterposeFlag;
        /**
         * 回访状态: 0-不需回访, 1-未回访, 2-已回访
         */
        private String callbackStatus;
        /**
         * 回访干预标识: 0-不需干预, 1-未干预, 2-已干预
         */
        private String callbackInterposeFlag;
        /**
         * 冷静期干预标识: 0-不需干预, 1-未干预, 2-已干预
         */
        private String calmdtmInterposeFlag;
        /**
         * 资产证明状态: 0-无效, 1-有效
         */
        private String assetcertificateStatus;
        /**
         * 资产证明干预标识: 0-不需干预, 1-未干预, 2-已干预
         */
        private String assetInterposeFlag;
        /**
         * 到期是否赎回
         */
        private String isRedeemExpire;
        /**
         * 预计到期日期
         */
        private String preExpireDate;

        /**
         * 首次实缴标识 0-否 1-是
         */
        private String firstCallFlag;

        /**
         * 双录完成时间
         */
        private Date dualentryFinishDtm;

        /**
         * 净申请金额
         */
        private BigDecimal netAppAmt;

        /**
         * 支付日期
         */
        private String pmtDt;

        /**
         * 认缴金额
         */
        private BigDecimal subsAmt;

        /**
         * 折扣类型：1-使用折扣率，2-使用折扣金额
         * {@link com.howbuy.crm.base.discount.DisCountUseTypeEnum}
         */
        private String discountUseType;

        /**
         * 折扣金额
         */
        private BigDecimal discountAmt;

        /**
         * 客户信息
         */
        private QueryAllCustInfoResult custInfo;

        public String getDiscountUseType() {
            return discountUseType;
        }

        public void setDiscountUseType(String discountUseType) {
            this.discountUseType = discountUseType;
        }

        public BigDecimal getDiscountAmt() {
            return discountAmt;
        }

        public void setDiscountAmt(BigDecimal discountAmt) {
            this.discountAmt = discountAmt;
        }

        public BigDecimal getAppointSubsAmt() {
            return appointSubsAmt;
        }

        public BigDecimal getTotalAppointSubsAmt() {
            return totalAppointSubsAmt;
        }

        public void setTotalAppointSubsAmt(BigDecimal totalAppointSubsAmt) {
            this.totalAppointSubsAmt = totalAppointSubsAmt;
        }

        public void setAppointSubsAmt(BigDecimal appointSubsAmt) {
            this.appointSubsAmt = appointSubsAmt;
        }

        public QueryAllCustInfoResult getCustInfo() {
            return custInfo;
        }

        public void setCustInfo(QueryAllCustInfoResult custInfo) {
            this.custInfo = custInfo;
        }

        public String getCpAcctNo() {
            return cpAcctNo;
        }

        public void setCpAcctNo(String cpAcctNo) {
            this.cpAcctNo = cpAcctNo;
        }

        public String getDualentryStatus() {
            return dualentryStatus;
        }

        public void setDualentryStatus(String dualentryStatus) {
            this.dualentryStatus = dualentryStatus;
        }

        public String getDualentryInterposeFlag() {
            return dualentryInterposeFlag;
        }

        public void setDualentryInterposeFlag(String dualentryInterposeFlag) {
            this.dualentryInterposeFlag = dualentryInterposeFlag;
        }

        public String getCallbackStatus() {
            return callbackStatus;
        }

        public void setCallbackStatus(String callbackStatus) {
            this.callbackStatus = callbackStatus;
        }

        public String getCallbackInterposeFlag() {
            return callbackInterposeFlag;
        }

        public void setCallbackInterposeFlag(String callbackInterposeFlag) {
            this.callbackInterposeFlag = callbackInterposeFlag;
        }

        public String getCalmdtmInterposeFlag() {
            return calmdtmInterposeFlag;
        }

        public void setCalmdtmInterposeFlag(String calmdtmInterposeFlag) {
            this.calmdtmInterposeFlag = calmdtmInterposeFlag;
        }

        public String getAssetcertificateStatus() {
            return assetcertificateStatus;
        }

        public void setAssetcertificateStatus(String assetcertificateStatus) {
            this.assetcertificateStatus = assetcertificateStatus;
        }

        public String getAssetInterposeFlag() {
            return assetInterposeFlag;
        }

        public void setAssetInterposeFlag(String assetInterposeFlag) {
            this.assetInterposeFlag = assetInterposeFlag;
        }

        public BigDecimal getAppointmentAmt() {
            return appointmentAmt;
        }

        public void setAppointmentAmt(BigDecimal appointmentAmt) {
            this.appointmentAmt = appointmentAmt;
        }

        public String getPmtCheckDt() {
            return pmtCheckDt;
        }

        public void setPmtCheckDt(String pmtCheckDt) {
            this.pmtCheckDt = pmtCheckDt;
        }

        public String getFundRiskLevel() {
            return fundRiskLevel;
        }

        public void setFundRiskLevel(String fundRiskLevel) {
            this.fundRiskLevel = fundRiskLevel;
        }

        public String getCustRiskLevel() {
            return custRiskLevel;
        }

        public void setCustRiskLevel(String custRiskLevel) {
            this.custRiskLevel = custRiskLevel;
        }

        public String getSubBankName() {
            return subBankName;
        }

        public void setSubBankName(String subBankName) {
            this.subBankName = subBankName;
        }

        public String getPaymentType() {
            return paymentType;
        }

        public void setPaymentType(String paymentType) {
            this.paymentType = paymentType;
        }

        public BigDecimal getAppAmt() {
            return appAmt;
        }

        public void setAppAmt(BigDecimal appAmt) {
            this.appAmt = appAmt;
        }

        public BigDecimal getDiscountRate() {
            return discountRate;
        }

        public void setDiscountRate(BigDecimal discountRate) {
            this.discountRate = discountRate;
        }

        public String getRiskFlag() {
            return riskFlag;
        }

        public void setRiskFlag(String riskFlag) {
            this.riskFlag = riskFlag;
        }

        public BigDecimal getAppointmentDiscount() {
            return appointmentDiscount;
        }

        public void setAppointmentDiscount(BigDecimal appointmentDiscount) {
            this.appointmentDiscount = appointmentDiscount;
        }

        public BigDecimal getEsitmateFee() {
            return esitmateFee;
        }

        public void setEsitmateFee(BigDecimal esitmateFee) {
            this.esitmateFee = esitmateFee;
        }

        public String getFirstBuyFlag() {
            return firstBuyFlag;
        }

        public void setFirstBuyFlag(String firstBuyFlag) {
            this.firstBuyFlag = firstBuyFlag;
        }

        public String getPayDeadlineDtm() {
            return payDeadlineDtm;
        }

        public void setPayDeadlineDtm(String payDeadlineDtm) {
            this.payDeadlineDtm = payDeadlineDtm;
        }

        public String getOpenEndTime() {
            return openEndTime;
        }

        public void setOpenEndTime(String openEndTime) {
            this.openEndTime = openEndTime;
        }

        public BigDecimal getFee() {
            return fee;
        }

        public void setFee(BigDecimal fee) {
            this.fee = fee;
        }

        public String getSupportAdvanceFlag() {
            return supportAdvanceFlag;
        }

        public void setSupportAdvanceFlag(String supportAdvanceFlag) {
            this.supportAdvanceFlag = supportAdvanceFlag;
        }

        public String getAdvanceFlag() {
            return advanceFlag;
        }

        public void setAdvanceFlag(String advanceFlag) {
            this.advanceFlag = advanceFlag;
        }

        public String getOrderFormType() {
            return orderFormType;
        }

        public void setOrderFormType(String orderFormType) {
            this.orderFormType = orderFormType;
        }

        public String getAppointmentType() {
            return appointmentType;
        }

        public void setAppointmentType(String appointmentType) {
            this.appointmentType = appointmentType;
        }

        public String getIsRedeemExpire() {
            return isRedeemExpire;
        }

        public void setIsRedeemExpire(String isRedeemExpire) {
            this.isRedeemExpire = isRedeemExpire;
        }

        public String getPreExpireDate() {
            return preExpireDate;
        }

        public void setPreExpireDate(String preExpireDate) {
            this.preExpireDate = preExpireDate;
        }

        public String getFirstCallFlag() {
            return firstCallFlag;
        }

        public void setFirstCallFlag(String firstCallFlag) {
            this.firstCallFlag = firstCallFlag;
        }

        public Date getDualentryFinishDtm() {
            return dualentryFinishDtm;
        }

        public void setDualentryFinishDtm(Date dualentryFinishDtm) {
            this.dualentryFinishDtm = dualentryFinishDtm;
        }

        public BigDecimal getNetAppAmt() {
            return netAppAmt;
        }

        public void setNetAppAmt(BigDecimal netAppAmt) {
            this.netAppAmt = netAppAmt;
        }

        public String getPmtDt() {
            return pmtDt;
        }

        public void setPmtDt(String pmtDt) {
            this.pmtDt = pmtDt;
        }

        public BigDecimal getSubsAmt() {
            return subsAmt;
        }

        public void setSubsAmt(BigDecimal subsAmt) {
            this.subsAmt = subsAmt;
        }
    }

    public static class RedeemBean {
        /**
         * 资金账号
         */
        private String cpAcctNo;
        /**
         * 赎回份额
         */
        private BigDecimal appVol;
        /**
         * 赎回去向
         */
        private String redeemDirection;
        /**
         * 巨额赎回标识
         */
        private String largeRedmFlag;
        /**
         * 可赎回日期
         */
        private String allowDt;
        /**
         * 特殊交易标记，用于异常订单标记，0-正常交易，1-异常交易, 2-特殊补单交易
         */
        private String unusualTransType;

        /**
         * 可提前下单标识
         */
        private String supportAdvanceFlag;

        /**
         * 成单方式
         */
        private String orderFormType;

        /**
         * 预约类型: 0:系统生成 ,1:投顾生成
         */
        private String appointmentType;

        private String repurchaseProtocolNo;
        /**
         * 预约订单号
         */
        private String appointmentDealNo;

        /**
         * 客户信息和资金账户信息
         */
        private QueryAllCustInfoResult custInfo;

        /**
         * 回可用金额
         */
        private BigDecimal refundAmt;
        /**
         * 回可用备注
         */
        private String refundMemo;


        public String getCpAcctNo() {
            return cpAcctNo;
        }

        public void setCpAcctNo(String cpAcctNo) {
            this.cpAcctNo = cpAcctNo;
        }

        public String getAppointmentType() {
            return appointmentType;
        }

        public void setAppointmentType(String appointmentType) {
            this.appointmentType = appointmentType;
        }

        public BigDecimal getAppVol() {
            return appVol;
        }

        public void setAppVol(BigDecimal appVol) {
            this.appVol = appVol;
        }

        public String getRedeemDirection() {
            return redeemDirection;
        }

        public void setRedeemDirection(String redeemDirection) {
            this.redeemDirection = redeemDirection;
        }

        public String getLargeRedmFlag() {
            return largeRedmFlag;
        }

        public void setLargeRedmFlag(String largeRedmFlag) {
            this.largeRedmFlag = largeRedmFlag;
        }

        public String getAllowDt() {
            return allowDt;
        }

        public void setAllowDt(String allowDt) {
            this.allowDt = allowDt;
        }

        public String getUnusualTransType() {
            return unusualTransType;
        }

        public void setUnusualTransType(String unusualTransType) {
            this.unusualTransType = unusualTransType;
        }

        public String getSupportAdvanceFlag() {
            return supportAdvanceFlag;
        }

        public void setSupportAdvanceFlag(String supportAdvanceFlag) {
            this.supportAdvanceFlag = supportAdvanceFlag;
        }

        public String getOrderFormType() {
            return orderFormType;
        }

        public void setOrderFormType(String orderFormType) {
            this.orderFormType = orderFormType;
        }

        public String getRepurchaseProtocolNo() {
            return repurchaseProtocolNo;
        }

        public void setRepurchaseProtocolNo(String repurchaseProtocolNo) {
            this.repurchaseProtocolNo = repurchaseProtocolNo;
        }

        public String getAppointmentDealNo() {
            return appointmentDealNo;
        }

        public void setAppointmentDealNo(String appointmentDealNo) {
            this.appointmentDealNo = appointmentDealNo;
        }

        public QueryAllCustInfoResult getCustInfo() {
            return custInfo;
        }

        public void setCustInfo(QueryAllCustInfoResult custInfo) {
            this.custInfo = custInfo;
        }

        public BigDecimal getRefundAmt() {
            return refundAmt;
        }

        public void setRefundAmt(BigDecimal refundAmt) {
            this.refundAmt = refundAmt;
        }

        public String getRefundMemo() {
            return refundMemo;
        }

        public void setRefundMemo(String refundMemo) {
            this.refundMemo = refundMemo;
        }
    }

    /**
     * 非交易过户参数
     *
     * @author: huaqiang.liu
     * @date: 2020/9/25 10:13
     * @since JDK 1.8
     */
    public static class NoTradeOverAccountBean {
        /**
         * 过户份额
         */
        private BigDecimal appVol;

        public BigDecimal getAppVol() {
            return appVol;
        }

        public void setAppVol(BigDecimal appVol) {
            this.appVol = appVol;
        }
    }

    /**
     * <AUTHOR>
     * @description:(子账本变动明细)
     * @reason:
     * @date 2017年7月26日 下午8:36:56
     * @since JDK 1.7
     */
    public static class SubBooksDtlBean {
        /**
         * 产品代码
         */
        private String productCode;
        /**
         * 份额登记日期
         */
        private String regDt;
        /**
         * 开放赎回日期
         */
        private String openRedeDt;
        /**
         * 申请份额
         */
        private BigDecimal appVol;
        /**
         * 份额明细流水号
         */
        private String booksDtlNo;
        /**
         * 协议号
         */
        private String protocolNo;
        /**
         * 确认日期
         */
        private String ackDt;

        public String getProductCode() {
            return productCode;
        }

        public void setProductCode(String productCode) {
            this.productCode = productCode;
        }

        public String getAckDt() {
            return ackDt;
        }

        public void setAckDt(String ackDt) {
            this.ackDt = ackDt;
        }

        public String getProtocolNo() {
            return protocolNo;
        }

        public void setProtocolNo(String protocolNo) {
            this.protocolNo = protocolNo;
        }

        public String getRegDt() {
            return regDt;
        }

        public void setRegDt(String regDt) {
            this.regDt = regDt;
        }

        public String getOpenRedeDt() {
            return openRedeDt;
        }

        public void setOpenRedeDt(String openRedeDt) {
            this.openRedeDt = openRedeDt;
        }

        public BigDecimal getAppVol() {
            return appVol;
        }

        public void setAppVol(BigDecimal appVol) {
            this.appVol = appVol;
        }

        public String getBooksDtlNo() {
            return booksDtlNo;
        }

        public void setBooksDtlNo(String booksDtlNo) {
            this.booksDtlNo = booksDtlNo;
        }
    }

    /**
     * 柜台信息
     */
    public static class OrderExtendBean {
        /**
         * 经办人证件号
         */
        private String transactorIdNo;
        /**
         * 经办人证件类型
         */
        private String transactorIdType;
        /**
         * 经办人姓名
         */
        private String transactorName;
        /**
         * 操作员编号
         */
        private String operatorNo;
        /**
         * 投资顾问代码
         */
        private String consCode;
        /**
         * 预约订单号
         */
        private String appointmentDealNo;
        /**
         * 风险二次确认时间
         */
        private String riskAckDtm;
        /**
         * 高风险提示时间
         */
        private String highRiskTipDtm;
        /**
         * 普通投资者风险提示时间
         */
        private String normalCustTipDtm;

        /**
         * 风测提醒确定时间
         */
        private String riskHintConfirmDtm;

        /**
         * 投资者类型认证提醒确认时间 yyyyMMddHHmmss
         */
        private String investorQualifiedHintConfirmDtm;
        /**
         * 风险评测日期 yyyyMMdd
         */
        private String riskToleranceDate;

        /**
         * 投资者类型认证日期 YYYYMMDD
         */
        private String investorQualifiedDate;
        /**
         * 合格投资者再次确认时间
         */
        private String investAckDtm;
        /**
         * 联系地址
         */
        private String liveAddress;
        /**
         * 身份证地址
         */
        private String cardAddress;

        /**
         * 服务主体
         */
        private String serviceEntityName;

        /**
         * 基金从业资格编号
         */
        private String fpqcCode;


        public String getServiceEntityName() {
            return serviceEntityName;
        }

        public void setServiceEntityName(String serviceEntityName) {
            this.serviceEntityName = serviceEntityName;
        }

        public String getFpqcCode() {
            return fpqcCode;
        }

        public void setFpqcCode(String fpqcCode) {
            this.fpqcCode = fpqcCode;
        }

        public String getRiskHintConfirmDtm() {
            return riskHintConfirmDtm;
        }

        public void setRiskHintConfirmDtm(String riskHintConfirmDtm) {
            this.riskHintConfirmDtm = riskHintConfirmDtm;
        }

        public String getInvestorQualifiedHintConfirmDtm() {
            return investorQualifiedHintConfirmDtm;
        }

        public void setInvestorQualifiedHintConfirmDtm(String investorQualifiedHintConfirmDtm) {
            this.investorQualifiedHintConfirmDtm = investorQualifiedHintConfirmDtm;
        }

        public String getRiskToleranceDate() {
            return riskToleranceDate;
        }

        public void setRiskToleranceDate(String riskToleranceDate) {
            this.riskToleranceDate = riskToleranceDate;
        }

        public String getInvestorQualifiedDate() {
            return investorQualifiedDate;
        }

        public void setInvestorQualifiedDate(String investorQualifiedDate) {
            this.investorQualifiedDate = investorQualifiedDate;
        }

        public String getLiveAddress() {
            return liveAddress;
        }

        public void setLiveAddress(String liveAddress) {
            this.liveAddress = liveAddress;
        }

        public String getCardAddress() {
            return cardAddress;
        }

        public void setCardAddress(String cardAddress) {
            this.cardAddress = cardAddress;
        }

        public String getTransactorIdNo() {
            return transactorIdNo;
        }

        public void setTransactorIdNo(String transactorIdNo) {
            this.transactorIdNo = transactorIdNo;
        }

        public String getTransactorIdType() {
            return transactorIdType;
        }

        public void setTransactorIdType(String transactorIdType) {
            this.transactorIdType = transactorIdType;
        }

        public String getTransactorName() {
            return transactorName;
        }

        public void setTransactorName(String transactorName) {
            this.transactorName = transactorName;
        }

        public String getOperatorNo() {
            return operatorNo;
        }

        public void setOperatorNo(String operatorNo) {
            this.operatorNo = operatorNo;
        }

        public String getConsCode() {
            return consCode;
        }

        public void setConsCode(String consCode) {
            this.consCode = consCode;
        }

        public String getAppointmentDealNo() {
            return appointmentDealNo;
        }

        public void setAppointmentDealNo(String appointmentDealNo) {
            this.appointmentDealNo = appointmentDealNo;
        }

        public String getRiskAckDtm() {
            return riskAckDtm;
        }

        public void setRiskAckDtm(String riskAckDtm) {
            this.riskAckDtm = riskAckDtm;
        }

        public String getHighRiskTipDtm() {
            return highRiskTipDtm;
        }

        public void setHighRiskTipDtm(String highRiskTipDtm) {
            this.highRiskTipDtm = highRiskTipDtm;
        }

        public String getNormalCustTipDtm() {
            return normalCustTipDtm;
        }

        public void setNormalCustTipDtm(String normalCustTipDtm) {
            this.normalCustTipDtm = normalCustTipDtm;
        }

        public String getInvestAckDtm() {
            return investAckDtm;
        }

        public void setInvestAckDtm(String investAckDtm) {
            this.investAckDtm = investAckDtm;
        }
    }

    public static class ShareMergeBean {
        /**
         * 资金账号
         */
        private String cpAcctNo;

        /**
         * 银行账号
         */
        private String bankAcct;

        /**
         * 银行代码
         */
        private String bankCode;

        /**
         * 申请份额
         */
        private BigDecimal appVol;

        /**
         * 基金代码
         */
        private String fundCode;

        /**
         * 协议号
         */
        private String protocolNo;

        /**
         * 协议类型
         */
        private String protocolType;

        public String getCpAcctNo() {
            return cpAcctNo;
        }

        public void setCpAcctNo(String cpAcctNo) {
            this.cpAcctNo = cpAcctNo;
        }

        public String getBankAcct() {
            return bankAcct;
        }

        public void setBankAcct(String bankAcct) {
            this.bankAcct = bankAcct;
        }

        public String getBankCode() {
            return bankCode;
        }

        public void setBankCode(String bankCode) {
            this.bankCode = bankCode;
        }

        public BigDecimal getAppVol() {
            return appVol;
        }

        public void setAppVol(BigDecimal appVol) {
            this.appVol = appVol;
        }

        public String getFundCode() {
            return fundCode;
        }

        public void setFundCode(String fundCode) {
            this.fundCode = fundCode;
        }

        public String getProtocolNo() {
            return protocolNo;
        }

        public void setProtocolNo(String protocolNo) {
            this.protocolNo = protocolNo;
        }

        public String getProtocolType() {
            return protocolType;
        }

        public void setProtocolType(String protocolType) {
            this.protocolType = protocolType;
        }

    }

    /**
     * <AUTHOR>
     * @description:(预约订单信息)
     * @reason:
     * @date 2018年7月6日 下午5:14:43
     * @since JDK 1.6
     */
    public static class HighOrderAppointInfoBean {

        /**
         * 订单号
         */
        private String dealNo;

        /**
         * 预约订单号
         */
        private String appointmentDealNo;

        public String getDealNo() {
            return dealNo;
        }

        public void setDealNo(String dealNo) {
            this.dealNo = dealNo;
        }

        public String getAppointmentDealNo() {
            return appointmentDealNo;
        }

        public void setAppointmentDealNo(String appointmentDealNo) {
            this.appointmentDealNo = appointmentDealNo;
        }

    }

}
