/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.orders.service.facade.search.querybuyunconfirmasset;

import com.alibaba.fastjson.JSON;
import com.howbuy.tms.high.orders.facade.search.querybuyunconfirmasset.QueryBuyUnConfirmAssetFacade;
import com.howbuy.tms.high.orders.facade.search.querybuyunconfirmasset.QueryBuyUnConfirmAssetRequest;
import com.howbuy.tms.high.orders.facade.search.querybuyunconfirmasset.QueryBuyUnConfirmAssetResponse;
import com.howbuy.tms.high.orders.service.service.querybuyunconfirmasset.QueryBuyUnConfirmAssetService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @description: 查询买入待确认资产Facade服务实现类
 * <AUTHOR>
 * @date 2025/9/19 21:00
 * @since JDK 1.8
 */
@Slf4j
@Service
public class QueryBuyUnConfirmAssetFacadeService implements QueryBuyUnConfirmAssetFacade {

    @Autowired
    private QueryBuyUnConfirmAssetService queryBuyUnConfirmAssetService;

    /**
     * @description: 查询买入待确认资产
     * @param request 请求参数
     * @return com.howbuy.tms.high.orders.facade.search.querybuyunconfirmasset.QueryBuyUnConfirmAssetResponse
     * @author: jinqing.rao
     * @date: 2025/9/19 21:00
     * @since JDK 1.8
     */
    @Override
    public QueryBuyUnConfirmAssetResponse execute(QueryBuyUnConfirmAssetRequest request) {
        log.info("QueryBuyUnConfirmAssetFacadeService-查询买入待确认资产-开始，参数：{}", JSON.toJSONString(request));
        
        QueryBuyUnConfirmAssetResponse response = queryBuyUnConfirmAssetService.queryBuyUnConfirmAsset(request);
        
        log.info("QueryBuyUnConfirmAssetFacadeService-查询买入待确认资产-结束，响应：{}", JSON.toJSONString(response));
        return response;
    }
}
