/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.orders.service.facade.search.querymemberinfo;

import com.howbuy.tms.common.constant.ExceptionCodes;
import com.howbuy.tms.common.enums.busi.YesOrNoEnum;
import com.howbuy.tms.common.message.MessageSource;
import com.howbuy.tms.common.outerservice.cc.center.querymembertag.QueryUserTagsListOuterService;
import com.howbuy.tms.common.outerservice.cc.request.QueryUserTagsListRequestDTO;
import com.howbuy.tms.common.outerservice.cc.response.QueryUserTagsListResponseDTO;
import com.howbuy.tms.common.outerservice.cms.cmsconf.SmccConfigOuterService;
import com.howbuy.tms.common.outerservice.cms.request.GetSmccSwitchMemberBackgroundRequestDTO;
import com.howbuy.tms.common.outerservice.cms.response.GetSmccSwitchMemberBackgroundResponseDTO;
import com.howbuy.tms.high.orders.facade.search.querymemberinfo.QueryMemberInfoFacade;
import com.howbuy.tms.high.orders.facade.search.querymemberinfo.QueryMemberInfoRequest;
import com.howbuy.tms.high.orders.facade.search.querymemberinfo.QueryMemberInfoResponse;
import com.howbuy.tms.high.orders.facade.search.querymemberinfo.enums.BackgroundColorEnum;
import com.howbuy.tms.high.orders.facade.search.querymemberinfo.enums.MemberLevelEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.Optional;

/**
 * @description: 会员信息查询服务实现
 * <AUTHOR>
 * @date 2025/9/9
 * @since JDK 1.8
 */
@DubboService
@Service("queryMemberInfoFacade")
@Slf4j
public class QueryMemberInfoFacadeService implements QueryMemberInfoFacade {

    @Resource
    private QueryUserTagsListOuterService queryUserTagsListOuterService;

    @Resource
    private SmccConfigOuterService smccConfigOuterService;

    @Override
    public QueryMemberInfoResponse execute(QueryMemberInfoRequest request) {

        QueryMemberInfoResponse response = new QueryMemberInfoResponse();
        response.setReturnCode(ExceptionCodes.SUCCESS);
        response.setDescription(MessageSource.getMessageByCode(ExceptionCodes.SUCCESS));
        // 1.查询会员信息
        QueryMemberInfoResponse.MemberInfo memberInfo = queryMemberInfo(request.getHbOneNo(), request.getPageSource());
        response.setMemberInfo(memberInfo);
        return response;
    }
    
    /**
     * 查询会员信息
     * @param hbOneNo 一账通号
     * @param pageSource 页面来源
     * @return 会员信息
     */
    private QueryMemberInfoResponse.MemberInfo queryMemberInfo(String hbOneNo, String pageSource) {
        QueryMemberInfoResponse.MemberInfo memberInfo = new QueryMemberInfoResponse.MemberInfo();
        // 1.根据一账通查询会员级别
        QueryUserTagsListRequestDTO queryUserTagsListRequestDTO = new QueryUserTagsListRequestDTO();
        queryUserTagsListRequestDTO.setHboneNo(hbOneNo);
        QueryUserTagsListResponseDTO responseDTO = queryUserTagsListOuterService.queryUserTagsList(queryUserTagsListRequestDTO);
        // 非会员
        if(CollectionUtils.isEmpty(responseDTO.getUserTagsSet())){
            // 非会员
            memberInfo.setMemberLevel(MemberLevelEnum.NON_MEMBER.getCode());
            // 红色背景
            memberInfo.setBackgroundColor(BackgroundColorEnum.RED.getCode());
            return memberInfo;
        }
        // 是会员，获取最高等级的会员（根据tagNo降序排序，获取最大的一条）
        Optional<QueryUserTagsListResponseDTO.TagDetail> tagDetailOpt = responseDTO.getUserTagsSet()
                .stream()
                .max(Comparator.comparing(QueryUserTagsListResponseDTO.TagDetail::getTagNo));

        if (!tagDetailOpt.isPresent()) {
            // 如果没有找到标签详情，设置为非会员
            memberInfo.setMemberLevel(MemberLevelEnum.NON_MEMBER.getCode());
            memberInfo.setBackgroundColor(BackgroundColorEnum.RED.getCode());
            return memberInfo;
        }

        QueryUserTagsListResponseDTO.TagDetail tagDetail = tagDetailOpt.get();

        // 根据tagNo设置会员等级和背景色,会员默认是黑色
        String tagNo = tagDetail.getTagNo();
        memberInfo.setMemberLevel(tagNo);
        memberInfo.setBackgroundColor(BackgroundColorEnum.BLACK.getCode());
        // 获取CMS的配置,查看是否需要展示黑色背景
        GetSmccSwitchMemberBackgroundRequestDTO memberBackgroundRequestDTO = new GetSmccSwitchMemberBackgroundRequestDTO();
        memberBackgroundRequestDTO.setHboneNo(hbOneNo);
        memberBackgroundRequestDTO.setPageId(pageSource);
        GetSmccSwitchMemberBackgroundResponseDTO smccSwitchMemberBackground = smccConfigOuterService.getSmccSwitchMemberBackground(memberBackgroundRequestDTO);
        // 没有获取到会员背景色配置,直接返回
        if(null == smccSwitchMemberBackground){
           return memberInfo;
        }
        // 如果会员需要切换成红色,重新设置背景色为红色
        if(StringUtils.equals(YesOrNoEnum.YES.getCode(), smccSwitchMemberBackground.getSfqh())){
            memberInfo.setBackgroundColor(BackgroundColorEnum.RED.getCode());
        }
        return memberInfo;
    }
}
