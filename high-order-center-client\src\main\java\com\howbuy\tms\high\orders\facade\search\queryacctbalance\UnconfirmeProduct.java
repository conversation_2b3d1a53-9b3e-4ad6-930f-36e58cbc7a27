package com.howbuy.tms.high.orders.facade.search.queryacctbalance;

import com.howbuy.tms.high.orders.facade.common.BaseDto;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @description:(高端在途基金)
 * @reason:
 * <AUTHOR>
 * @date 2018年6月21日 下午9:06:36
 * @since JDK 1.7
 */
@Setter
@Getter
public class UnconfirmeProduct extends BaseDto {
    private static final long serialVersionUID = -5610418332175150805L;

    /**
     * 产品代码
     */
    private String fundCode;
    /**
     * 产品类型
     */
    private String productType;
    /**
     * 产品子类型
     */
    private String productSubType;
    /**
     * 待确认金额(人民币)
     */
    private BigDecimal unconfirmedAmt;
    /**
     * 好买香港代销标识: 0-否; 1-是
     */
    private String hkSaleFlag;

    /**
     * 销售渠道
     */
    private String disCode;

}
