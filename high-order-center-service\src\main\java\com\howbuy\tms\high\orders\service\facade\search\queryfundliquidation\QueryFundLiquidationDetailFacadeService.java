/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.orders.service.facade.search.queryfundliquidation;

import com.alibaba.fastjson.JSON;
import com.howbuy.tms.common.constant.ExceptionCodes;
import com.howbuy.tms.common.enums.busi.ProductDBTypeEnum;
import com.howbuy.tms.common.enums.busi.YesOrNoEnum;
import com.howbuy.tms.common.exception.BusinessException;
import com.howbuy.tms.common.outerservice.asset.response.QueryFundLiquidationDetailResponseDTO;
import com.howbuy.tms.common.outerservice.cc.asset.fundLiquidation.FundLiquidationOuterService;
import com.howbuy.tms.common.outerservice.cc.request.QueryFundLiquidationDetailRequestDTO;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.QueryHighProductOuterService;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductDBInfoBean;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductNavBean;
import com.howbuy.tms.common.utils.DateUtils;
import com.howbuy.tms.high.orders.facade.search.queryfundliquidation.QueryFundLiquidationDetailFacade;
import com.howbuy.tms.high.orders.facade.search.queryfundliquidation.QueryFundLiquidationDetailRequest;
import com.howbuy.tms.high.orders.facade.search.queryfundliquidation.QueryFundLiquidationDetailResponse;
import com.howbuy.tms.high.orders.service.common.utils.BigDecimalUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description: 清仓产品详情查询服务实现
 * @date 2025/9/4 21:15
 * @since JDK 1.8
 */
@DubboService
@Service("queryFundLiquidationDetailFacade")
@Slf4j
public class QueryFundLiquidationDetailFacadeService implements QueryFundLiquidationDetailFacade {

    @Resource
    private FundLiquidationOuterService fundLiquidationOuterService;

    @Resource
    private QueryHighProductOuterService queryHighProductOuterService;

    @Override
    public QueryFundLiquidationDetailResponse execute(QueryFundLiquidationDetailRequest request) {
        log.info("查询清仓产品详情开始, request: {}", request);

        // 参数校验
        checkParamIsNull(request);

        // 调用资产中心接口查询清仓产品详情
        QueryFundLiquidationDetailResponseDTO assetResponse = queryQueryFundLiquidationDetailResponseDTO(request);

        if (assetResponse == null) {
            log.warn("资产中心返回清仓产品详情为空, hbOneNo: {}, mainFundCode: {}",
                    request.getHbOneNo(), request.getMainFundCode());
            return new QueryFundLiquidationDetailResponse();
        }
        List<String> fundCodeList = new ArrayList<>();
        fundCodeList.add(request.getMainFundCode());
        if (CollectionUtils.isNotEmpty(assetResponse.getSubFundList())) {
            fundCodeList.addAll(assetResponse.getSubFundList().stream().map(QueryFundLiquidationDetailResponseDTO.SubFundDetail::getFundCode).collect(Collectors.toList()));
        }
        // 获取产品信息
        Map<String, HighProductDBInfoBean> highProductDBInfoMap = queryHighProductOuterService.getHighProductDBInfoMap(fundCodeList);
        if (null == highProductDBInfoMap || highProductDBInfoMap.isEmpty()) {
            log.warn("查询不到产品信息, fundCode: {}", request.getMainFundCode());
            return new QueryFundLiquidationDetailResponse();
        }
        // 获取产品最新净值日期
        List<HighProductNavBean> highProductNavInfo = queryHighProductOuterService.getHighProductNavInfo(fundCodeList);
        // 转MAP
        Map<String, HighProductNavBean> highProductNavMap = highProductNavInfo.stream()
                .collect(Collectors.toMap(HighProductNavBean::getFundCode, Function.identity(), (existing, replacement) -> existing));
        // 封装返回结果
        return buildFundLiquidationDetail(assetResponse, highProductDBInfoMap, highProductNavMap);
    }

    private static void checkParamIsNull(QueryFundLiquidationDetailRequest request) {
        if (StringUtils.isBlank(request.getHbOneNo())) {
            throw new BusinessException(ExceptionCodes.PARAMS_IS_EMPTY, "参数错误,一账通号不能为空");
        }

        if (StringUtils.isBlank(request.getMainFundCode())) {
            throw new BusinessException(ExceptionCodes.PARAMS_IS_EMPTY, "参数错误,基金代码不能为空");
        }
    }

    /**
     * @param request 请求参数
     * @return com.howbuy.tms.common.outerservice.asset.response.QueryFundLiquidationDetailResponseDTO
     * @description: 获取清仓产品详情
     * @author: jinqing.rao
     * @date: 2025/9/4 17:24
     * @since JDK 1.8
     */
    private QueryFundLiquidationDetailResponseDTO queryQueryFundLiquidationDetailResponseDTO(QueryFundLiquidationDetailRequest request) {
        QueryFundLiquidationDetailRequestDTO assetRequest = buildQueryFundLiquidationDetailRequestDTO(request);

        return fundLiquidationOuterService.queryFundLiquidationDetail(assetRequest);
    }

    /**
     * @param request
     * @return com.howbuy.tms.common.outerservice.asset.request.QueryFundLiquidationDetailRequestDTO
     * @description: 构建请求参数
     * @author: jinqing.rao
     * @date: 2025/9/4 17:24
     * @since JDK 1.8
     */
    private static QueryFundLiquidationDetailRequestDTO buildQueryFundLiquidationDetailRequestDTO(QueryFundLiquidationDetailRequest request) {
        QueryFundLiquidationDetailRequestDTO assetRequest = new QueryFundLiquidationDetailRequestDTO();
        assetRequest.setHboneNo(request.getHbOneNo());
        assetRequest.setFundCode(request.getMainFundCode());
        return assetRequest;
    }

    /**
     * 构建清仓产品详情
     */
    private QueryFundLiquidationDetailResponse buildFundLiquidationDetail(QueryFundLiquidationDetailResponseDTO assetResponse,
                                                                          Map<String, HighProductDBInfoBean> highProductDBInfoMap,
                                                                          Map<String, HighProductNavBean> highProductNavMap) {
        QueryFundLiquidationDetailResponse response = new QueryFundLiquidationDetailResponse();
        // 基金代码
        response.setFundCode(assetResponse.getFundCode());
        // 产品信息
        HighProductDBInfoBean highProductDBInfoBean = highProductDBInfoMap.get(assetResponse.getFundCode());
        if (null == highProductDBInfoBean) {
            log.warn("没有找到基金信息, 基金代码: {}", assetResponse.getFundCode());
            return response;
        }
        HighProductNavBean highProductNavBean = highProductNavMap.get(assetResponse.getFundCode());
        if (null == highProductNavBean) {
            log.warn("没有找到基金净值信息, 基金代码: {}", assetResponse.getFundCode());
            return response;
        }
        // 基金名称
        response.setFundName(highProductDBInfoBean.getFundAttr());
        // 基金代码
        response.setFundCode(highProductDBInfoBean.getFundCode());
        // 单位，默认是元
        response.setUnit("元");
        // 子基金明细
        List<QueryFundLiquidationDetailResponseDTO.SubFundDetail> subFundList = assetResponse.getSubFundList();
        // 清仓收益
        response.setClearUpIncome(assetResponse.getAccumIncomeExFee());

        // 累计收益率 - 服务端处理小数位,前端处理百分位
        response.setAccumIncomeRate(assetResponse.getAccumIncomeExFee());
        // 产品类型
        response.setProductType(highProductDBInfoBean.getFundType());
        // 产品子类型
        response.setProductSubType(highProductDBInfoBean.getFundSubType());
        // 累计持有天数
        response.setTotalHoldDays(assetResponse.getTotalHoldDays() != null ?
                assetResponse.getTotalHoldDays().toString() : null);
        // 收益计算中标签 1：有计算中标签 0：无计算中标签
        response.setIncomeStatus(getMainIncomeStatus(assetResponse));
        // 清仓后涨跌幅
        response.setClearUpRate(BigDecimalUtils.divide(highProductNavBean.getNav(), assetResponse.getNav(), 6, RoundingMode.DOWN));

        // 股权类产品
        if (ProductDBTypeEnum.GUQUAN.getCode().equals(highProductDBInfoBean.getFundSubType())) {
            // 回款金额 - 服务端处理小数位,前端处理千分位
            response.setCashCollection(assetResponse.getAccumCollection());
            // 初始成本
            response.setInitInvestCost(assetResponse.getAccumCostExFeeNew());
            if (null == assetResponse.getAccumCostExFeeNew() || assetResponse.getAccumCostExFeeNew().compareTo(BigDecimal.ZERO) > 0) {
                // 回款进度
                response.setCashCollectionProgress(BigDecimalUtils.divide(assetResponse.getAccumCollection(), assetResponse.getAccumCostExFeeNew(), 6, RoundingMode.DOWN));
            }
        }
        // 分期成立产品
        if (CollectionUtils.isNotEmpty(subFundList)) {

            List<QueryFundLiquidationDetailResponse.ClearDetailInfo> detailInfoList = subFundList.stream().map(subFund -> {
                QueryFundLiquidationDetailResponse.ClearDetailInfo clearDetailInfo = new QueryFundLiquidationDetailResponse.ClearDetailInfo();
                // 产品信息
                HighProductDBInfoBean subhighProductDBInfoBean = highProductDBInfoMap.get(subFund.getFundCode());
                // 基金净值
                HighProductNavBean subHighProductNavBean = highProductNavMap.get(subFund.getFundCode());

                // 增加空值判断，如果产品信息或净值信息为空，则跳过该子基金
                if (null == subhighProductDBInfoBean) {
                    log.warn("没有找到子基金信息, 基金代码: {}", subFund.getFundCode());
                    return null; // 返回null，后续通过filter过滤掉
                }

                if (null == subHighProductNavBean) {
                    log.warn("没有找到子基金净值信息, 基金代码: {}", subFund.getFundCode());
                    return null; // 返回null，后续通过filter过滤掉
                }
                // 基金名称
                clearDetailInfo.setFundName(subhighProductDBInfoBean.getFundAttr());
                // 基金编码
                clearDetailInfo.setFundCode(subFund.getFundCode());
                clearDetailInfo.setUnit("元");
                // 收益状态
                clearDetailInfo.setIncomeStatus(getIncomeStatus(subFund.getLastClearDt(), subFund.getIncomeDt()));
                clearDetailInfo.setClearUpIncome(subFund.getAccumIncomeExFee());

                // 清仓后涨跌幅
                response.setClearUpRate(BigDecimalUtils.divide(subHighProductNavBean.getNav(), subFund.getNav(), 6, RoundingMode.DOWN));
                clearDetailInfo.setProductType(subhighProductDBInfoBean.getFundType());
                clearDetailInfo.setProductSubType(subhighProductDBInfoBean.getFundSubType());
                clearDetailInfo.setTotalHoldDays(subFund.getTotalHoldDays());
                clearDetailInfo.setAccumIncomeRate(subFund.getAccumIncomeRateExFee());
                clearDetailInfo.setIncomeStatus(compare(subFund.getLastClearDt(), subFund.getIncomeDt()));
                return clearDetailInfo;
            }).filter(Objects::nonNull).collect(Collectors.toList());
            // 清仓明细列表
            response.setClearDetailList(detailInfoList);
        }
        return response;
    }

    /**
     * @param lastClearDt 持仓最后日期
     * @param incomeDt    收益日期
     * @return java.lang.String
     * @description: 获取收益状态  1：有计算中标签 0：无计算中标签
     * @author: jinqing.rao
     * @date: 2025/9/4 17:56
     * @since JDK 1.8
     */
    private String getIncomeStatus(String lastClearDt, String incomeDt) {
        return compare(lastClearDt, incomeDt);
    }

    /**
     * @param assetResponse
     * @return java.lang.String
     * @description: 计算收益中状态
     * @author: jinqing.rao
     * @date: 2025/9/5 14:06
     * @since JDK 1.8
     */
    private String getMainIncomeStatus(QueryFundLiquidationDetailResponseDTO assetResponse) {
        // 分期成立产品
        if (CollectionUtils.isNotEmpty(assetResponse.getSubFundList())) {
            return assetResponse.getSubFundList().stream()
                    .anyMatch(this::isSubFundIncomeCalculating)
                    ? YesOrNoEnum.YES.getCode()
                    : YesOrNoEnum.NO.getCode();
        }

        // 非分期产品
        return isIncomeCalculating(assetResponse.getLastClearDt(), assetResponse.getIncomeDt());
    }

    /**
     * 判断子基金是否收益计算中
     */
    private boolean isSubFundIncomeCalculating(QueryFundLiquidationDetailResponseDTO.SubFundDetail subFund) {
        // 存在空数据,默认显示没有计算中标签
        if (StringUtils.isAnyBlank(subFund.getLastClearDt(), subFund.getIncomeDt())) {
            log.warn("存在空数据, 默认显示没有计算中标签, orderDetail: {}", JSON.toJSONString(subFund));
            return false;
        }
        return YesOrNoEnum.YES.getCode().equals(compare(subFund.getLastClearDt(), subFund.getIncomeDt()));
    }

    /**
     * 判断基金是否收益计算中
     */
    private String isIncomeCalculating(String lastClearDt, String incomeDt) {
        // 存在空数据,默认显示没有计算中标签
        if (StringUtils.isAnyBlank(lastClearDt, incomeDt)) {
            log.warn("主清仓基金,存在空数据, 默认显示没有计算中标签, lastClearDt: {}, incomeDt: {}", lastClearDt, incomeDt);
            return YesOrNoEnum.NO.getCode();
        }
        return compare(lastClearDt, incomeDt);
    }

    private static String compare(String lastClearDt, String incomeDt) {
        boolean compared = DateUtils.compareDateString(lastClearDt, incomeDt, DateUtils.YYYYMMDD);
        if (compared) {
            return YesOrNoEnum.YES.getCode();
        }
        return YesOrNoEnum.NO.getCode();
    }
}
