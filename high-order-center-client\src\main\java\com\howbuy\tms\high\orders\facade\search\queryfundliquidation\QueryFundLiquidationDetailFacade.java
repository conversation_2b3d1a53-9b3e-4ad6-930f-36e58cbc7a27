/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.orders.facade.search.queryfundliquidation;

import com.howbuy.tms.common.client.BaseFacade;

/**
 * @description: 清仓产品详情查询接口
 * <AUTHOR>
 * @date 2025/9/4 21:10
 * @since JDK 1.8
 */
public interface QueryFundLiquidationDetailFacade extends BaseFacade<QueryFundLiquidationDetailRequest, QueryFundLiquidationDetailResponse> {

}
