/**
 * Copyright (c) 2025, <PERSON>gHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.orders.facade.search.queryfundpositiondate;

import com.howbuy.tms.common.client.BaseFacade;

/**
 * @api {DUBBO} com.howbuy.tms.high.orders.facade.search.queryfundpositiondate.QueryFundPositionDateByFundCodeFacade.execute()
 * @apiVersion 1.0.0
 * @apiGroup QueryFundPositionDateByFundCodeFacadeService
 * @apiName execute
 * @apiDescription 根据基金编码查询基金持仓/清仓日期
 * @apiParam (请求参数) {String} hbOneNo 一账通号
 * @apiParam (请求参数) {String} fundCode 基金编码
 * @apiParam (请求参数) {String} disCode 分销机构代码
 * @apiParam (请求参数) {String} outletCode 网点代码
 * @apiParam (请求参数) {String} appDt 申请日期
 * @apiParam (请求参数) {String} appTm 申请时间
 * @apiParam (请求参数) {String} operIp 交易IP
 * @apiParam (请求参数) {String} txCode 交易码
 * @apiParam (请求参数) {String} txChannel 交易渠道 1-柜台,2-网站,3-电话,4-Wap,5-App,6-机构
 * @apiParamExample 请求参数示例
 * {"hbOneNo":"HB123456789","fundCode":"HM001","disCode":"HM","outletCode":"001","appDt":"20250911","appTm":"103500","operIp":"***********","txCode":"Z330091","txChannel":"2"}
 * @apiSuccess (响应结果) {String} fundCode 基金编码
 * @apiSuccess (响应结果) {String} firstHoldDate 首次持仓日期 yyyyMMdd
 * @apiSuccess (响应结果) {String} liquidationDate 清仓日期 yyyyMMdd
 * @apiSuccess (响应结果) {String} returnCode 返回码
 * @apiSuccess (响应结果) {String} description 描述信息
 * @apiSuccessExample 响应结果示例
 * {"fundCode":"HM001","firstHoldDate":"20240101","liquidationDate":"20250901","returnCode":"0000","description":"成功"}
 */
/**
 * @description: 根据基金编码查询基金持仓/清仓日期接口
 * <AUTHOR>
 * @date 2025/9/11 10:30
 * @since JDK 1.8
 */
public interface QueryFundPositionDateByFundCodeFacade extends BaseFacade<QueryFundPositionDateByFundCodeRequest, QueryFundPositionDateByFundCodeResponse> {

} 