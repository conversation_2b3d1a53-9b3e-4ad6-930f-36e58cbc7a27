package com.howbuy.tms.high.orders.service.service.acctbalancebaseinfo.bean;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * @Description: 买入非香港待确认订单交易记录Bean
 * @Author: yun.lu
 */
@Getter
@Setter
public class BuyUnConfirmUnHkOrderBean {
    /**
     * 订单号
     */
    private String dealNo;

    /**
     * 分销渠道
     */
    private String disCode;

    /**
     * 产品编码
     */
    private String fundCode;

    /**
     * 待确认金额:申请净金额-储蓄罐预约冻结金额(人民币)
     */
    private BigDecimal unConfirmAmt;


    /**
     * 待确认金额当前币种
     */
    private BigDecimal currencyUnConfirmAmt;

    /**
     * 中台业务类型
     */
    private String mBusiCode;

    /**
     * 销售类型: 1-直销;2-代销
     */
    private String scaleType;
}
