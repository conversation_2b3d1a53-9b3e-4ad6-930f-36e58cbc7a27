/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.orders.facade.search.querybuyunconfirmasset;

import com.howbuy.tms.common.enums.busi.DisCodeEnum;
import com.howbuy.tms.high.orders.facade.common.OrderSearchBaseRequest;
import com.howbuy.tms.high.orders.facade.enums.TxCodes;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * @description: 查询买入待确认资产请求参数
 * <AUTHOR>
 * @date 2025/9/19 21:00
 * @since JDK 1.8
 */
@Setter
@Getter
public class QueryBuyUnConfirmAssetRequest extends OrderSearchBaseRequest implements Serializable {


    private static final long serialVersionUID = 725680625132209589L;

    /**
     * 构造函数，设置默认交易代码
     */
    public QueryBuyUnConfirmAssetRequest() {
        setTxCode(TxCodes.QUERY_BUY_UNCONFIRM_ASSET);
        setDisCode(DisCodeEnum.HM.getCode());
    }
}
