/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.orders.facade.search.querymemberinfo;

import com.howbuy.tms.high.orders.facade.common.OrderSearchBaseResponse;
import com.howbuy.tms.high.orders.facade.search.querymemberinfo.enums.BackgroundColorEnum;
import com.howbuy.tms.high.orders.facade.search.querymemberinfo.enums.MemberLevelEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * @description: 会员信息查询响应结果
 * <AUTHOR>
 * @date 2025/9/9
 * @since JDK 1.8
 */
@Setter
@Getter
public class QueryMemberInfoResponse extends OrderSearchBaseResponse {


    private static final long serialVersionUID = 233958679944827708L;
    /**
     * 会员信息
     */
    private MemberInfo memberInfo;

    /**
     * 会员信息
     */
    @Setter
    @Getter
    public static class MemberInfo implements Serializable {


        private static final long serialVersionUID = 3203676096473948599L;
        /**
         * 用户会员等级
         * @see MemberLevelEnum
         * 23301-臻享会员
         * 23302-私享会员
         * 23303-尊享会员
         */
        private String memberLevel;

        /**
         * 会员背景色字段
         * @see BackgroundColorEnum
         * 1-红色
         * 2-黑色
         */
        private String backgroundColor;
    }
}
