/**
 * Copyright (c) 2021, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.tms.high.orders.facade.trade.invplan;

import com.howbuy.tms.high.orders.facade.common.OrderTradeBaseResponse;

/**
 * @description: (基金定投抽象response)
 * <AUTHOR>
 * @date 2021/11/16 9:27
 * @since JDK 1.8
 */
/**
 * @apiDefine baseInvPlanResponse 定投应答共有响应参数(resp)
 * @apiGroup high-order-center
 *
 */
public abstract class BaseInvPlanResponse extends OrderTradeBaseResponse {

    /**
     * 校验状态 1：成功  2：失败  3：提醒
     */
    private String checkStatus;

    public String getCheckStatus() {
        return checkStatus;
    }

    public void setCheckStatus(String checkStatus) {
        this.checkStatus = checkStatus;
    }
}