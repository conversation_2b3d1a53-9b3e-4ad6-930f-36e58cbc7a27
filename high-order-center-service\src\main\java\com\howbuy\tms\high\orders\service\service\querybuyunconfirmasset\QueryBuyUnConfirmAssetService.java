/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.orders.service.service.querybuyunconfirmasset;

import com.alibaba.fastjson.JSON;
import com.howbuy.tms.common.constant.ExceptionCodes;
import com.howbuy.tms.common.exception.BusinessException;
import com.howbuy.tms.common.message.MessageSource;
import com.howbuy.tms.high.orders.facade.search.querybuyunconfirmasset.BuyUnConfirmOrderDetailResponse;
import com.howbuy.tms.high.orders.facade.search.querybuyunconfirmasset.QueryBuyUnConfirmAssetRequest;
import com.howbuy.tms.high.orders.facade.search.querybuyunconfirmasset.QueryBuyUnConfirmAssetResponse;
import com.howbuy.tms.high.orders.service.service.acctbalancebaseinfo.AcctBalanceBaseInfoService;
import com.howbuy.tms.high.orders.service.service.acctbalancebaseinfo.bean.BuyUnConfirmUnHkOrderBean;
import com.howbuy.tms.high.orders.service.service.acctbalancebaseinfo.bean.QueryBalanceParam;
import com.howbuy.tms.common.outerservice.cc.center.queryhboneno.QueryHbOneNoOuterService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * @description: 查询买入待确认资产服务实现类
 * <AUTHOR>
 * @date 2025/9/19 21:00
 * @since JDK 1.8
 */
@Slf4j
@Service
public class QueryBuyUnConfirmAssetService {

    @Autowired
    private AcctBalanceBaseInfoService acctBalanceBaseInfoService;

    @Autowired
    private QueryHbOneNoOuterService queryHbOneNoOuterService;

    /**
     * @description: 查询买入待确认资产
     * @param request 请求参数
     * @return com.howbuy.tms.high.orders.facade.search.querybuyunconfirmasset.QueryBuyUnConfirmAssetResponse
     * @author: jinqing.rao
     * @date: 2025/9/19 21:00
     * @since JDK 1.8
     */
    public QueryBuyUnConfirmAssetResponse queryBuyUnConfirmAsset(QueryBuyUnConfirmAssetRequest request) {
        log.info("queryBuyUnConfirmAsset-查询买入待确认资产-开始，参数：{}", JSON.toJSONString(request));

        // 1. 初始化响应对象
        QueryBuyUnConfirmAssetResponse response = buildDefaultResponse();

        try {
            if (StringUtils.isBlank(request.getHbOneNo())) {
                throw new BusinessException(ExceptionCodes.PARAMS_IS_EMPTY, "参数错误,交易账号不能为空");
            }
            // 2. 通过一账通获取交易账号
            String txAcctNo = queryHbOneNoOuterService.queryTxAcctNoByHbOneNo(request.getHbOneNo());
            if (StringUtils.isBlank(txAcctNo)) {
                throw new BusinessException(ExceptionCodes.PARAMS_IS_EMPTY, "参数错误,交易账号不能为空");
            }

            // 3. 构建查询参数
            QueryBalanceParam queryBalanceParam = buildQueryBalanceParam(request.getHbOneNo(), txAcctNo);

            // 4. 调用AcctBalanceBaseInfoService查询买入待确认订单
            List<BuyUnConfirmUnHkOrderBean> orderList = acctBalanceBaseInfoService.queryBuyUnConfirmUnHkOrderList(queryBalanceParam);

            // 5. 如果返回的数据集合为空，返回空数据
            if (CollectionUtils.isEmpty(orderList)) {
                log.info("queryBuyUnConfirmAsset-未查询到买入待确认订单，hbOneNo={}, txAcctNo={}", request.getHbOneNo(), txAcctNo);
                return response;
            }

            // 6. 遍历返回的数据集合，统计总金额并转换为响应对象
            processOrderList(orderList, response);

            log.info("queryBuyUnConfirmAsset-查询买入待确认资产-结束，订单数量：{}", orderList.size());

        } catch (Exception e) {
            log.error("queryBuyUnConfirmAsset-查询买入待确认资产异常，hbOneNo={}", request.getHbOneNo(), e);
            response.setReturnCode(ExceptionCodes.HIGH_ORDER_SYSTEM_ERROR);
            response.setDescription(MessageSource.getMessageByCode(ExceptionCodes.HIGH_ORDER_SYSTEM_ERROR));
        }

        return response;
    }

    /**
     * @description: 构建默认响应对象
     * @return com.howbuy.tms.high.orders.facade.search.querybuyunconfirmasset.QueryBuyUnConfirmAssetResponse
     * @author: jinqing.rao
     * @date: 2025/9/19 21:00
     * @since JDK 1.8
     */
    private QueryBuyUnConfirmAssetResponse buildDefaultResponse() {
        QueryBuyUnConfirmAssetResponse response = new QueryBuyUnConfirmAssetResponse();
        response.setReturnCode(ExceptionCodes.SUCCESS);
        response.setDescription(MessageSource.getMessageByCode(ExceptionCodes.SUCCESS));
        response.setTotalUnConfirmAmt(BigDecimal.ZERO);
        response.setTotalCurrencyUnConfirmAmt(BigDecimal.ZERO);
        response.setOrderDetailList(new ArrayList<>());
        return response;
    }

    /**
     * @description: 构建查询参数
     * @param hbOneNo 一账通号
     * @param txAcctNo 交易账号
     * @return com.howbuy.tms.high.orders.service.service.acctbalancebaseinfo.bean.QueryBalanceParam
     * @author: jinqing.rao
     * @date: 2025/9/19 21:00
     * @since JDK 1.8
     */
    private QueryBalanceParam buildQueryBalanceParam(String hbOneNo, String txAcctNo) {
        QueryBalanceParam queryBalanceParam = new QueryBalanceParam();
        queryBalanceParam.setHboneNo(hbOneNo);
        queryBalanceParam.setTxAcctNo(txAcctNo);
        return queryBalanceParam;
    }

    /**
     * @description: 处理订单列表，统计总金额并转换为响应对象
     * @param orderList 订单列表
     * @param response 响应对象
     * @author: jinqing.rao
     * @date: 2025/9/19 21:00
     * @since JDK 1.8
     */
    private void processOrderList(List<BuyUnConfirmUnHkOrderBean> orderList, QueryBuyUnConfirmAssetResponse response) {
        BigDecimal totalUnConfirmAmt = BigDecimal.ZERO;
        BigDecimal totalCurrencyUnConfirmAmt = BigDecimal.ZERO;
        List<BuyUnConfirmOrderDetailResponse> orderDetailList = new ArrayList<>();

        for (BuyUnConfirmUnHkOrderBean order : orderList) {
            // 统计总金额
            if (order.getUnConfirmAmt() != null) {
                totalUnConfirmAmt = totalUnConfirmAmt.add(order.getUnConfirmAmt());
            }
            if (order.getCurrencyUnConfirmAmt() != null) {
                totalCurrencyUnConfirmAmt = totalCurrencyUnConfirmAmt.add(order.getCurrencyUnConfirmAmt());
            }

            // 转换为响应对象
            BuyUnConfirmOrderDetailResponse detail = convertToDetailResponse(order);
            orderDetailList.add(detail);
        }

        // 设置总金额和订单明细列表
        response.setTotalUnConfirmAmt(totalUnConfirmAmt);
        response.setTotalCurrencyUnConfirmAmt(totalCurrencyUnConfirmAmt);
        response.setOrderDetailList(orderDetailList);
    }

    /**
     * @description: 转换订单为明细响应对象
     * @param order 订单对象
     * @return com.howbuy.tms.high.orders.facade.search.querybuyunconfirmasset.BuyUnConfirmOrderDetailResponse
     * @author: jinqing.rao
     * @date: 2025/9/19 21:00
     * @since JDK 1.8
     */
    private BuyUnConfirmOrderDetailResponse convertToDetailResponse(BuyUnConfirmUnHkOrderBean order) {
        BuyUnConfirmOrderDetailResponse detail = new BuyUnConfirmOrderDetailResponse();
        detail.setDealNo(order.getDealNo());
        detail.setDisCode(order.getDisCode());
        detail.setFundCode(order.getFundCode());
        detail.setUnConfirmAmt(order.getUnConfirmAmt());
        detail.setCurrencyUnConfirmAmt(order.getCurrencyUnConfirmAmt());
        detail.setMBusiCode(order.getMBusiCode());
        detail.setScaleType(order.getScaleType());
        return detail;
    }
}
