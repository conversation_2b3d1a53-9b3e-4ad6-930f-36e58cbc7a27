package com.howbuy.tms.high.orders.service.business.fundbuystatus;

import com.howbuy.tms.common.enums.busi.BuyStatusTypeEnum;
import com.howbuy.tms.common.enums.busi.FundBuyStatusEnum;
import com.howbuy.tms.common.enums.busi.YesOrNoEnum;
import com.howbuy.tms.common.outerservice.acccenter.querycustInfo.QueryCustInfoOuterService;
import com.howbuy.tms.common.outerservice.acccenter.querycustInfo.QueryCustInfoResult;
import com.howbuy.tms.common.utils.StringUtils;
import com.howbuy.tms.high.orders.service.business.factory.fundBuyStatus.AbstractFundBuyStatusChannelSourceService;
import com.howbuy.tms.high.orders.service.business.factory.fundBuyStatus.FundBuyStatusPageSourceFactory;
import com.howbuy.tms.high.orders.service.facade.search.querybuyfundstatus.bean.FundBuyStatusDto;
import com.howbuy.tms.high.orders.service.facade.search.querybuyfundstatus.bean.QueryFundBuyStatusListParam;
import com.howbuy.tms.high.orders.service.facade.search.querybuyfundstatus.bean.QueryFundBuyStatusParam;
import com.howbuy.tms.high.orders.service.repository.CustBooksRepository;
import com.howbuy.tms.high.orders.service.repository.DealOrderRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class FundBuyStatusService {
    @Autowired
    private FundBuyStatusPageSourceFactory fundBuyStatusPageSourceFactory;
    @Autowired
    private QueryCustInfoOuterService queryCustInfoOuterService;
    @Autowired
    private CustBooksRepository custBooksRepository;
    @Autowired
    private DealOrderRepository dealOrderRepository;
    public List<FundBuyStatusDto> queryProcess(QueryFundBuyStatusListParam fundBuyStatusListParam) {
        List<FundBuyStatusDto> list = new ArrayList<>();
        long start1 = System.currentTimeMillis();
        for (String fundCode : fundBuyStatusListParam.getFundCodeList()) {
            long start = System.currentTimeMillis();
            try {
                // 查询产品控制信息
                String controlDisCode = fundBuyStatusListParam.getProductInfoModelMap().get(fundCode).getControlDisCode();
                QueryFundBuyStatusParam queryFundBuyStatusParam = new QueryFundBuyStatusParam();
                BeanUtils.copyProperties(fundBuyStatusListParam, queryFundBuyStatusParam);
                queryFundBuyStatusParam.setFundCode(fundCode);
                queryFundBuyStatusParam.setDisCode(controlDisCode);
                queryFundBuyStatusParam.setBlackFundCodes(fundBuyStatusListParam.getBlackFundCodes());
                queryFundBuyStatusParam.setCcOrZtList(fundBuyStatusListParam.getCcOrZtList());
                queryFundBuyStatusParam.setCustInfo(fundBuyStatusListParam.getCustInfo());
                queryFundBuyStatusParam.setHighQuotaBeanNew(fundBuyStatusListParam.getHighQuotaBeanNewMap().get(fundCode));
                queryFundBuyStatusParam.setHighProductCanBuyInfoModel(fundBuyStatusListParam.getProductInfoModelMap().get(fundCode));
                queryFundBuyStatusParam.setPartnersNumber(fundBuyStatusListParam.getPartnersNumber());
                queryFundBuyStatusParam.setHzOnWayAgentDealDtlList(fundBuyStatusListParam.getHzOnWayAgentDealDtlMap().get(fundCode));
                queryFundBuyStatusParam.setHzConfirmBalanceBaseInfoList(fundBuyStatusListParam.getHzConfirmBalanceBaseInfoMap().get(fundCode));
                // 产品购买状态处理类
                AbstractFundBuyStatusChannelSourceService abstractFundBuyStatusChannelSourceService
                        = fundBuyStatusPageSourceFactory.getFundBuyStatusLogicService(controlDisCode,fundBuyStatusListParam.getPageSource());
                FundBuyStatusDto fundBuyStatus = abstractFundBuyStatusChannelSourceService.getFundBuyStatus(queryFundBuyStatusParam);
                list.add(fundBuyStatus);
            } catch (Exception e) {
                log.error("FundBuyStatusTask-查询用户产品可购买状态出现异常,fundCode={}", fundCode, e);
                FundBuyStatusDto fundBuyStatusDto = new FundBuyStatusDto();
                fundBuyStatusDto.setProductCode(fundCode);
                fundBuyStatusDto.setFundBuyStatusEnum(FundBuyStatusEnum.CAN_NOT_BUY);
                fundBuyStatusDto.setBuyStatusType(BuyStatusTypeEnum.ERROR.getCode());
                fundBuyStatusDto.setDelayedOrderFlag(YesOrNoEnum.NO.getCode());
                fundBuyStatusDto.setMsg("查询用户产品可购买状态出现异常");
                list.add(fundBuyStatusDto);
            }
            long end = System.currentTimeMillis();
            log.info("txAcctNo：{}，fundCode:{},耗时:{}", fundBuyStatusListParam.getTxAcctNo(), fundCode, end - start);
        }
        long end1 = System.currentTimeMillis();
        log.info("txAcctNo：{}，fundCodes:{},耗时:{}", fundBuyStatusListParam.getTxAcctNo(), fundBuyStatusListParam.getFundCodeList(), end1 - start1);
        return list;
    }

    /**
     * 查询客户信息明文
     *
     * @param txAcctNo
     * @param disCode
     * @return com.howbuy.tms.common.outerservice.acccenter.querycustInfo.QueryCustInfoResult
     * @author: huaqiang.liu
     * @date: 2021/4/23 19:00
     * @since JDK 1.8
     */
    public QueryCustInfoResult queryCustInfo(String txAcctNo, String disCode) {
        return queryCustInfoOuterService.queryCustInfo(txAcctNo);
    }

    /**
     * 查询持仓和在途产品
     *
     * @param fundCodeList
     * @return
     */
    public List<String> getCcOrZtList(String txAcctNo, List<String> fundCodeList) {
        if (StringUtils.isEmpty(txAcctNo)) {
            return new ArrayList<>();
        }
        // 查询持仓产品
        List<String> ccList = custBooksRepository.selectStockBooksByTxAcctNoAndFundCodeBatch(txAcctNo, fundCodeList);
        List<String> ccOrZtList = new ArrayList<>(ccList);
        // 查询在途产品
        List<String> ztList = dealOrderRepository.selectNotCompleteBuyCountMap(txAcctNo, fundCodeList);
        ccOrZtList.addAll(ztList);
        ccOrZtList = ccOrZtList.stream().distinct().collect(Collectors.toList());
        return ccOrZtList;
    }

}
