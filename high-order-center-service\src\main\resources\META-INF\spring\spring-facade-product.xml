<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
	xmlns:context="http://www.springframework.org/schema/context"
	xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
	xsi:schemaLocation="http://www.springframework.org/schema/beans 
	http://www.springframework.org/schema/beans/spring-beans.xsd
	http://www.springframework.org/schema/context 
    http://www.springframework.org/schema/context/spring-context.xsd 
	http://code.alibabatech.com/schema/dubbo http://code.alibabatech.com/schema/dubbo/dubbo.xsd">
	
	<context:component-scan base-package="com.howbuy.tms.common.outerservice.interlayer"
		use-default-filters="false">
		<context:include-filter type="annotation" expression="org.springframework.stereotype.Service" />
	</context:component-scan>
	
	<!-- 产品中心-组合投资产品接口 -->
	<dubbo:reference id="portfolioProductService" interface="com.howbuy.interlayer.product.service.PortfolioProductService"  registry="product-center-remote"/>
		
	<!-- 查询组合基金最新净值接口 -->
	<dubbo:reference id="productNavService" interface="com.howbuy.interlayer.product.service.ProductNavService"  registry="product-center-remote"/>
	
	<!-- 查询交易日接口 -->	
	<dubbo:reference id="tradeDayService" interface="com.howbuy.interlayer.product.service.TradeDayService"  registry="product-center-remote"/>
		
	<!-- 查询基金信息 -->	
	<dubbo:reference id="queryFundInfoService" interface="com.howbuy.interlayer.product.service.fund.QueryFundInfoService"  registry="product-center-remote"/>
		
	<!-- 中台产品相关服务接口 -->	
	<dubbo:reference id="productService" interface="com.howbuy.interlayer.product.service.fund.ProductService"  registry="product-center-remote"/>
		
	<!-- 中台高端产品相关服务接口(私募、专户) -->	
	<dubbo:reference id="highProductService" interface="com.howbuy.interlayer.product.service.HighProductService"  registry="product-center-remote" timeout="300000"/>
		
	<!-- 组合产品CMS接口 -->
	<dubbo:reference id="cmsPortfolioProductLimitCfgService" interface="com.howbuy.interlayer.product.service.CmsPortfolioProductLimitCfgService"  registry="product-center-remote"/>
	
	<!-- 中台零售产品相关服务接口 -->
	<dubbo:reference id="fundProductService" interface="com.howbuy.interlayer.product.service.FundProductService"  registry="product-center-remote"/>
	
	<!-- 产品相关配置信息 -->
	<dubbo:reference id="productCfgService" interface="com.howbuy.interlayer.product.service.ProductCfgService"  registry="product-center-remote"/>
		
	<!-- 用户服务 -->
	<dubbo:reference id="userService" interface="com.howbuy.interlayer.product.service.permission.UserService"  registry="product-center-remote" />
		
	<!-- 权限服务 -->
	<dubbo:reference id="permissionService" interface="com.howbuy.interlayer.product.service.permission.PermissionService"  registry="product-center-remote" />
		
	<!-- 查询ta相关信息 -->
	<dubbo:reference id="queryTaInfoService" interface="com.howbuy.interlayer.product.service.QueryTaInfoService"  registry="product-center-remote"/>
		
	<dubbo:reference id="queryIntransNotSupportTaService" interface="com.howbuy.interlayer.product.service.QueryIntransNotSupportTaService"  registry="product-center-remote"/>

		
	<!-- 交易限额配置服务接口 -->
	<dubbo:reference id="cashLimitCfgService" interface="com.howbuy.interlayer.product.service.config.CashLimitCfgService"  registry="product-center-remote"/>
		
	<!-- 垫资通道配置服务接口 -->
	<dubbo:reference id="cashLoaningCfgService" interface="com.howbuy.interlayer.product.service.config.CashLoaningCfgService"  registry="product-center-remote" />

	<!-- 基金升降级关系相关接口 -->
	<dubbo:reference id="fundLevelRelationService" interface="com.howbuy.interlayer.product.service.FundLevelRelationService"  registry="product-center-remote" />

	<!-- 灰度单查询接口 -->
	<dubbo:reference id="outservice.grayReleaseCustWhiteListService" interface="com.howbuy.interlayer.product.service.GrayReleaseCustWhiteListService"  registry="product-center-remote"/>
		
	<!-- 投顾产品服务 -->
	<dubbo:reference id="outservice.adviserProductService" interface="com.howbuy.interlayer.product.service.AdviserProductService"  registry="product-center-remote"/>

	<!-- 投顾产品商户服务 -->
	<dubbo:reference id="outservice.portfolioPartnerInfoService" interface="com.howbuy.interlayer.product.service.PortfolioPartnerInfoService"  registry="product-center-remote"/>

	<!-- 查询基金信息相关接口 -->
	<dubbo:reference id="queryLctFundInfoService" interface="com.howbuy.interlayer.product.service.lct.QueryLctFundInfoService"  registry="product-center-remote"/>


	<!-- 目标收益产品配置服务 -->
	<dubbo:reference id="portfolioAimProfitCfgService" interface="com.howbuy.interlayer.product.service.PortfolioAimProfitCfgService"  registry="product-center-remote"/>
					 
	<!-- 协议匹配查询 -->
	<dubbo:reference id="complianceAgreementService" interface="com.howbuy.interlayer.product.service.ComplianceAgreementService"  registry="product-center-remote"/>
					 
	<!-- 查询转投比例接口 -->
	<dubbo:reference id="transferRatioService" interface="com.howbuy.interlayer.product.service.config.TransferRatioService"  registry="product-center-remote"/>

	<!-- 查询转投比例接口 -->
	<dubbo:reference id="fundTransferService" interface="com.howbuy.interlayer.product.service.config.FundTransferService"  registry="product-center-remote"/>

	<!-- 查询转投额度配置 -->
	<dubbo:reference id="transferLoaningService" interface="com.howbuy.interlayer.product.service.config.TransferLoaningService"  registry="product-center-remote"/>

	<!-- 查询组合折扣 -->
	<dubbo:reference id="discountService" interface="com.howbuy.interlayer.product.service.discount.DiscountService"  registry="product-center-remote"/>

	<!-- 查询在途不能卖出产品 -->
	<dubbo:reference id="adviserProductRuleCfgService" interface="com.howbuy.interlayer.product.service.AdviserProductRuleCfgService"  registry="product-center-remote"/>

	<!-- 查询基金管理人-->
	<dubbo:reference id="highProductParamConfService" interface="com.howbuy.interlayer.product.service.HighProductParamConfService"  registry="product-center-remote"/>

	<!-- 查询基金管理人-->
	<dubbo:reference id="queryNotHBJGFundListService" interface="com.howbuy.interlayer.product.service.high.QueryNotHBJGFundListService"  registry="product-center-remote"/>

	<!-- 查询投顾产品问卷答案问题 -->
	<dubbo:reference id="adviserProductQuestionAnswerService" interface="com.howbuy.interlayer.product.service.AdviserProductQuestionAnswerService"  registry="product-center-remote"/>

	<dubbo:reference id="adviserProdInfoService" interface="com.howbuy.interlayer.product.service.AdviserProdInfoService"  registry="product-center-remote"/>

	<dubbo:reference id="portfolioTransferAdviserConfService" interface="com.howbuy.interlayer.product.service.portfoliotransferadviser.PortfolioTransferAdviserConfService"  registry="product-center-remote"/>

	<!--上报异常码查询-->
	<dubbo:reference id="submitExceptionCodeCfgService" interface="com.howbuy.interlayer.product.service.config.SubmitExceptionCodeCfgService"  registry="product-center-remote"/>

	<!--投顾调仓平衡接口-->
	<dubbo:reference id="adviserBalanceInfoService" interface="com.howbuy.interlayer.product.service.AdviserBalanceInfoService"  registry="product-center-remote"/>

	<!--潜龙产品接口-->
	<dubbo:reference id="uPlanProductService" interface="com.howbuy.interlayer.product.service.UPlanProductService"  registry="product-center-remote"/>

	<!--投顾产品中心文件接口-->
	<dubbo:reference id="adviserFileService" interface="com.howbuy.interlayer.product.service.AdviserFileService"  registry="product-center-remote"/>

	<!--产品控制信息临时表接口-->
	<dubbo:reference id="tmpHighProductParamConfService" interface="com.howbuy.interlayer.product.service.TmpHighProductParamConfService"  registry="product-center-remote"/>

	<!--产品锁定额度信息配置服务-->
	<dubbo:reference id="productLockInfoService" interface="com.howbuy.interlayer.product.service.ProductLockInfoService"  registry="product-center-remote"/>

	<!--业务类型转义配置-->
	<dubbo:reference id="businessCodeTransferConfigService" interface="com.howbuy.interlayer.product.service.high.BusinessCodeTransferConfigService"  registry="product-center-remote"/>

	<!--产品锁定额度信息配置服务-->
	<dubbo:reference id="hzFundAmtLockConfService" interface="com.howbuy.interlayer.product.service.config.HzFundAmtLockConfService"  registry="product-center-remote"/>
	<!--业务类型转义配置-->
	<dubbo:reference id="advanceBusService" interface="com.howbuy.interlayer.product.service.advance.AdvanceBusService"  registry="product-center-remote"/>

	<!--产品提示语配置-->
	<dubbo:reference id="stockRightFundNoticeMsgCfgService"  interface="com.howbuy.interlayer.product.service.config.StockRightFundNoticeMsgCfgService"  registry="product-center-remote"/>

	<!--产品通用邮箱-->
	<dubbo:reference id="commonManagerEmailConfService" interface="com.howbuy.interlayer.product.service.high.CommonManagerEmailConfService" check="false" registry="product-center-remote"/>

	<!--批量查询净值-->
	<dubbo:reference id="fundNavStatusService"  interface="com.howbuy.interlayer.product.service.fund.FundNavStatusService"  registry="product-center-remote"/>

	<!--管理人邮件-->
	<dubbo:reference id="fundManagerService"  interface="com.howbuy.interlayer.product.service.FundManagerService"  registry="product-center-remote"/>

	<!--预约日历-->
	<dubbo:reference id="productAppointmentInfoService"  interface="com.howbuy.interlayer.product.service.ProductAppointmentInfoService"  registry="product-center-remote"/>

	<!--理财分析白名单-->
	<dubbo:reference id="liCaiShouYiWhiteConfService"  interface="com.howbuy.interlayer.product.service.high.LiCaiShouYiWhiteConfService"  registry="product-center-remote"/>


	<!-- 查询ta相关信息 -->
	<dubbo:reference id="taInfoService" interface="com.howbuy.interlayer.product.service.TaInfoService"  registry="product-center-remote"/>



</beans>