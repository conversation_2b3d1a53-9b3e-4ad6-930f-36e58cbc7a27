/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.tms.high.orders.service.service.queryactivebuysellstats;

import com.howbuy.tms.common.constant.ExceptionCodes;
import com.howbuy.tms.common.enums.busi.BusinessCodeEnum;
import com.howbuy.tms.common.enums.database.OrderStatusEnum;
import com.howbuy.tms.common.exception.BusinessException;
import com.howbuy.tms.common.message.MessageSource;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.QueryHighProductOuterService;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductDBInfoBean;
import com.howbuy.tms.common.utils.DateUtils;
import com.howbuy.tms.high.orders.dao.po.CmCusttradeDirectPo;
import com.howbuy.tms.high.orders.dao.po.HighDealOrderDtlPo;
import com.howbuy.tms.high.orders.facade.search.queryactivebuysellstats.QueryActiveBuySellStatsRequest;
import com.howbuy.tms.high.orders.facade.search.queryactivebuysellstats.QueryActiveBuySellStatsResponse;
import com.howbuy.tms.high.orders.facade.search.queryactivebuysellstats.TradeDetailInfoResponse;
import com.howbuy.tms.high.orders.service.common.utils.TradeUtils;
import com.howbuy.tms.high.orders.service.repository.CmCusttradeDirectRepository;
import com.howbuy.tms.high.orders.service.repository.HighDealOrderDtlRepository;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description: 查询主动买卖统计数据业务服务
 * @date 2025/9/19 20:30
 * @since JDK 1.8
 */
@Service
public class QueryActiveBuySellStatsService {

    private static final Logger logger = LogManager.getLogger(QueryActiveBuySellStatsService.class);

    @Resource
    private HighDealOrderDtlRepository highDealOrderDtlRepository;

    @Resource
    private CmCusttradeDirectRepository cmCusttradeDirectRepository;

    @Resource
    private QueryHighProductOuterService queryHighProductOuterService;

    /**
     * 购买类中台业务码
     */
    private static final List<String> BUY_TRADE_TYPES = Arrays.asList(
            BusinessCodeEnum.PURCHASE.getMCode(),
            BusinessCodeEnum.SUBS.getMCode(),
            BusinessCodeEnum.SUBS_RESULT.getMCode()
    );

    /**
     * 卖出类中台业务码
     */
    private static final List<String> SELL_TRADE_TYPES = Arrays.asList(BusinessCodeEnum.REDEEM.getMCode());

    /**
     * 确认状态
     */
    private static final List<String> SUCCESS_AND_PARTIAL_STATUS = Arrays.asList(
            OrderStatusEnum.ACK_SUCCESS.getCode(),
            OrderStatusEnum.PART_ACK_SUCCESS.getCode());


    /**
     * 买操作类型
     */
    private static final String BUY_OPERATION_TYPE = "1";

    /**
     * 卖出操作类型
     */
    private static final String SELL_OPERATION_TYPE = "2";


    /**
     * @description: 查询主动买卖统计数据
     * @param request 请求参数
     * @return com.howbuy.tms.high.orders.facade.search.queryactivebuysellstats.QueryActiveBuySellStatsResponse
     * @author: jinqing.rao
     * @date: 2025/9/19 20:30
     * @since JDK 1.8
     */
    public QueryActiveBuySellStatsResponse queryActiveBuySellStats(QueryActiveBuySellStatsRequest request) {
        // 参数校验
        if (StringUtils.isBlank(request.getHbOneNo())) {
            throw new BusinessException(ExceptionCodes.PARAMS_IS_EMPTY, "参数错误,一账通号不能为空");
        }
        // 构建默认响应
        QueryActiveBuySellStatsResponse response = buildQueryActiveBuySellStatsResponse();

        List<TradeDetailInfoResponse> tradeDetailList = queryActiveBuySellStatsTradeRecords(request);
        response.setTradeDetailList(tradeDetailList);

        return response;
    }

    /**
     * @description: 构建默认响应对象
     * @return com.howbuy.tms.high.orders.facade.search.queryactivebuysellstats.QueryActiveBuySellStatsResponse
     * @author: jinqing.rao
     * @date: 2025/9/19 20:30
     * @since JDK 1.8
     */
    private QueryActiveBuySellStatsResponse buildQueryActiveBuySellStatsResponse() {
        QueryActiveBuySellStatsResponse response = new QueryActiveBuySellStatsResponse();
        // 默认成功状态
        response.setReturnCode(ExceptionCodes.SUCCESS);
        response.setDescription(MessageSource.getMessageByCode(ExceptionCodes.SUCCESS));
        return response;
    }


    /**
     * @description: 查询主动买卖统计交易记录，包括代销和直销
     * @param request 请求参数
     * @return java.util.List<com.howbuy.tms.high.orders.facade.search.queryactivebuysellstats.TradeDetailInfoResponse>
     * @author: jinqing.rao
     * @date: 2025/9/19 20:30
     * @since JDK 1.8
     */
    private List<TradeDetailInfoResponse> queryActiveBuySellStatsTradeRecords(QueryActiveBuySellStatsRequest request) {
        List<TradeDetailInfoResponse> tradeDetailList = new ArrayList<>();
        // 查询代销买入订单
        Date date = new Date();
        String startDate = DateUtils.formatToString(date, DateUtils.YYYYMMDD);
        String endDate = DateUtils.formatToString(DateUtils.addMonthOfYear(date, -12), DateUtils.YYYYMMDD);
        List<HighDealOrderDtlPo> buySellStatsOrderList = queryHighCustActiveBuySellStats(request.getHbOneNo(), startDate, endDate);

        // 查询直销买入订单
        List<CmCusttradeDirectPo> buyOrders = queryDirectBuyOrders(request.getHbOneNo(),startDate, endDate);

        // 获取基金Code,查询产品信息
        List<String> fundCodeList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(buySellStatsOrderList)){
            fundCodeList.addAll(buySellStatsOrderList.stream().map(HighDealOrderDtlPo::getFundCode).distinct().collect(Collectors.toList()));
        }
        if(CollectionUtils.isNotEmpty(buyOrders)){
            fundCodeList.addAll(buyOrders.stream().map(CmCusttradeDirectPo::getFundcode).distinct().collect(Collectors.toList()));
        }
        if(CollectionUtils.isEmpty(fundCodeList)){
           return tradeDetailList;
        }
        // 获取产品信息
        Map<String, HighProductDBInfoBean> highProductDBInfoMap = queryHighProductOuterService.getHighProductDBInfoMap(fundCodeList);
        // 构建代销的交易记录
        buildHighTradeDetailList(buySellStatsOrderList, highProductDBInfoMap, tradeDetailList);
        // 构建直销的交易记录
        buildDirectTradeDetailList(buyOrders, highProductDBInfoMap, tradeDetailList);
        return tradeDetailList;
    }

    /**
     * @description: 构建直销的交易记录
     * @param buyOrders	 直销交易记录订单
     * @param highProductDBInfoMap	产品信息MAP
     * @param tradeDetailList 返回数据列表
     * @return void
     * @author: jinqing.rao
     * @date: 2025/9/22 16:48
     * @since JDK 1.8
     */
    private void buildDirectTradeDetailList(List<CmCusttradeDirectPo> buyOrders, Map<String, HighProductDBInfoBean> highProductDBInfoMap, List<TradeDetailInfoResponse> tradeDetailList) {
        if (CollectionUtils.isNotEmpty(buyOrders)) {
            for (CmCusttradeDirectPo order : buyOrders) {
                HighProductDBInfoBean highProductDBInfoBean = highProductDBInfoMap.get(order.getFundcode());
                if(null == highProductDBInfoBean){
                    logger.error("产品信息不存在,基金Code:{}", order.getFundcode());
                    continue;
                }
                // 类型判断
                if(TradeUtils.isSunshineAndBrokerage(highProductDBInfoBean.getStandardFixedIncomeFlag(), highProductDBInfoBean.getFundSubType(), order.getFundcode())){
                    logger.error("不是好买基金名下的阳光私募&券商集合产品,StandardFixedIncomeFlag:{},FundSubType : {},基金Code:{}", highProductDBInfoBean.getStandardFixedIncomeFlag(), highProductDBInfoBean.getFundSubType(),order.getFundcode());
                    continue;
                }
                TradeDetailInfoResponse detail = convertDirectToTradeDetail(order); // 1-买入
                if (detail != null) {
                    tradeDetailList.add(detail);
                }
            }
        }
    }
    /**
     * @description: 构建代销的交易记录
     * @param buySellStatsOrderList	 代销交易记录订单
     * @param highProductDBInfoMap	产品信息MAP
     * @param tradeDetailList 返回数据列表
     * @return void
     * @author: jinqing.rao
     * @date: 2025/9/22 16:48
     * @since JDK 1.8
     */
    private void buildHighTradeDetailList(List<HighDealOrderDtlPo> buySellStatsOrderList, Map<String, HighProductDBInfoBean> highProductDBInfoMap, List<TradeDetailInfoResponse> tradeDetailList) {
        if (CollectionUtils.isNotEmpty(buySellStatsOrderList)) {
            for (HighDealOrderDtlPo order : buySellStatsOrderList) {
                HighProductDBInfoBean highProductDBInfoBean = highProductDBInfoMap.get(order.getFundCode());
                if(null == highProductDBInfoBean){
                    logger.error("产品信息不存在,基金Code:{}", order.getFundCode());
                    continue;
                }
                // 类型判断
                if(TradeUtils.isSunshineAndBrokerage(highProductDBInfoBean.getStandardFixedIncomeFlag(), highProductDBInfoBean.getFundSubType(), order.getFundCode())){
                    logger.error("不是好买基金名下的阳光私募&券商集合产品,StandardFixedIncomeFlag:{},FundSubType : {},基金Code:{}", highProductDBInfoBean.getStandardFixedIncomeFlag(), highProductDBInfoBean.getFundSubType(),order.getFundCode());
                    continue;
                }
                TradeDetailInfoResponse detail = convertConsignmentToTradeDetail(order);
                if (detail != null) {
                    tradeDetailList.add(detail);
                }
            }
        }
    }


    /**
     * @description: 查询代销买卖订单
     * @param hbOneNo 一账通号
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return java.util.List<com.howbuy.tms.high.orders.dao.po.HighDealOrderDtlPo>
     * @author: jinqing.rao
     * @date: 2025/9/19 20:30
     * @since JDK 1.8
     */
    private List<HighDealOrderDtlPo> queryHighCustActiveBuySellStats(String hbOneNo, String startDate, String endDate) {

        List<String> businessCodes = new ArrayList<>();
        businessCodes.addAll(BUY_TRADE_TYPES);
        businessCodes.addAll(SELL_TRADE_TYPES);
        return highDealOrderDtlRepository.queryHighCustActiveBuySellStats(
                hbOneNo,
                startDate,
                endDate,
                businessCodes,
                SUCCESS_AND_PARTIAL_STATUS
        );
    }

    /**
     * @description: 查询直销买卖订单
     * @param hbOneNo 一账通号
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return java.util.List<com.howbuy.tms.high.orders.dao.po.CmCusttradeDirectPo>
     * @author: jinqing.rao
     * @date: 2025/9/19 20:30
     * @since JDK 1.8
     */
    private List<CmCusttradeDirectPo> queryDirectBuyOrders(String hbOneNo, String startDate, String endDate) {
        List<String> businessCodes = new ArrayList<>();
        businessCodes.addAll(BUY_TRADE_TYPES);
        businessCodes.addAll(SELL_TRADE_TYPES);
        return cmCusttradeDirectRepository.queryDirectBuyOrders(
                hbOneNo,
                startDate,
                endDate,
                businessCodes, // 直销对应的业务编码
               SUCCESS_AND_PARTIAL_STATUS // 确认成功
        );
    }

    /**
     * @description: 转换代销订单为交易明细
     * @param order 代销订单
     * @return com.howbuy.tms.high.orders.facade.search.queryactivebuysellstats.TradeDetailInfoResponse
     * @author: jinqing.rao
     * @date: 2025/9/19 20:30
     * @since JDK 1.8
     */
    private TradeDetailInfoResponse convertConsignmentToTradeDetail(HighDealOrderDtlPo order) {
        if (order == null) {
            return null;
        }

        TradeDetailInfoResponse detail = new TradeDetailInfoResponse();
        detail.setFundCode(order.getFundCode());
        if (BUY_TRADE_TYPES.contains(order.getmBusiCode())) {
            detail.setOperationType(BUY_OPERATION_TYPE);
            // 订单金额：买入取净买入金额
            detail.setOrderAmount(order.getNetAppAmt());
        }
        if (SELL_TRADE_TYPES.contains(order.getmBusiCode())) {
            detail.setOperationType(SELL_OPERATION_TYPE);
            // 订单金额：卖出取确认金额
            detail.setOrderAmount(order.getAckAmt());
        }
        // 订单日期：优先取上报日期，没有则取确认日期
        String orderDate = StringUtils.isNotBlank(order.getSubmitTaDt()) ?
                order.getSubmitTaDt() : order.getAckDt();
        detail.setOrderDate(orderDate);
        return detail;
    }

   /**
    * @description: 转换直销订单为交易明细
    * @param order	直销订单
    * @return com.howbuy.tms.high.orders.facade.search.queryactivebuysellstats.TradeDetailInfoResponse
    * @author: jinqing.rao
    * @date: 2025/9/19 15:13
    * @since JDK 1.8
    */
    private TradeDetailInfoResponse convertDirectToTradeDetail(CmCusttradeDirectPo order) {
        if (order == null) {
            return null;
        }

        TradeDetailInfoResponse detail = new TradeDetailInfoResponse();
        detail.setFundCode(order.getFundcode());
        if (BUY_TRADE_TYPES.contains(order.getBusicode())) {
            detail.setOperationType(BUY_OPERATION_TYPE);
            // 订单金额：买入取净买入金额
            detail.setOrderAmount(order.getAppamt());
        }
        if (SELL_TRADE_TYPES.contains(order.getBusicode())) {
            detail.setOperationType(SELL_OPERATION_TYPE);
            // 订单金额：卖出取确认金额
            detail.setOrderAmount(order.getAckamt());
        }

        // 订单日期：优先取上报日期，没有则取确认日期
        String orderDate = StringUtils.isNotBlank(order.getTradedt()) ?
                order.getTradedt() : order.getCredt();
        detail.setOrderDate(orderDate);

        return detail;
    }
}
