/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.orders.facade.search.queryfundpositiondate;

import com.howbuy.commons.validator.MyValidation;
import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import com.howbuy.tms.common.enums.busi.DisCodeEnum;
import com.howbuy.tms.high.orders.facade.common.OrderSearchBaseRequest;
import com.howbuy.tms.high.orders.facade.enums.TxCodes;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * @description: 根据基金编码查询基金持仓/清仓日期请求参数
 * <AUTHOR>
 * @date 2025/9/11 10:30
 * @since JDK 1.8
 */
@Setter
@Getter
public class QueryFundPositionDateByFundCodeRequest extends OrderSearchBaseRequest implements Serializable {


    private static final long serialVersionUID = -5504152068881700997L;

    /**
     * 构造函数，设置默认交易代码
     */
    public QueryFundPositionDateByFundCodeRequest() {
        setTxCode(TxCodes.QUERY_FUND_POSITION_DATE_BY_FUND_CODE);
        setDisCode(DisCodeEnum.HM.getCode());
    }

    /**
     * 一账通号
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "一账通号", isRequired = true)
    private String hbOneNo;

    /**
     * 基金编码
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "基金编码", isRequired = true)
    private String fundCode;
} 